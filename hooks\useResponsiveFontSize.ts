import { useEffect, useState } from 'react';

type Mode = 'breakpoint' | 'clamp';

export interface Options {
  mode?: Mode;
  min?: number;
  max?: number;
  width?: number;
  height?: number;
  isAuto?: boolean;
}

type Orientation = 'portrait' | 'landscape';

const breakpoints: [number, number][] = [
  [0, 14], // base
  [640, 18], // sm
  [768, 20], // md
  [1024, 28], // lg
  [1080, 32], // xl
  [1280, 32], // xl
  [1536, 40], // 2xl
  [1920, 46], // 3xl
  [2560, 54], // 4xl
  [3200, 64], // 5xl
  [3840, 78], // 6xl
  [4096, 84], // 7xl
  [5120, 98], // 8xl
  [7680, 124], // 9xl
  [8192, 130], // 10xl
];

export function useResponsiveFontSize(options: Options = {}) {
  const { mode = 'breakpoint', min = 14, max = 128, width, height } = options;
  const [fontSize, setFontSize] = useState(min);

  useEffect(() => {
    const updateFontSize = () => {
      const widthScreen = width ?? window.innerWidth;
      const heightScreen = height ?? window.innerHeight;
      let orientation: Orientation = 'landscape';
      orientation = widthScreen >= heightScreen ? 'landscape' : 'portrait';
      const keySize = orientation === 'landscape' ? widthScreen : heightScreen;

      if (mode === 'breakpoint') {
        let size = min;
        for (const [bp, sz] of breakpoints) {
          if (keySize >= bp) {
            size = sz;
          } else {
            break;
          }
        }
        setFontSize(size);
      }

      if (mode === 'clamp') {
        const vw = Math.min(Math.max(keySize, 320), 8192);
        const calcSize = min + ((max - min) * (vw - 320)) / (8192 - 320);
        setFontSize(parseFloat(calcSize.toFixed(2)));
      }
    };

    updateFontSize();
    window.addEventListener('resize', updateFontSize);
    return () => window.removeEventListener('resize', updateFontSize);
  }, [mode, min, max, width, height]);

  return fontSize;
}
