'use client';

import Image from 'next/image';
import { FormattedMessage } from 'react-intl';

export default function NotFound() {
  return (
    <div className="h-full w-full flex items-center justify-center bg-white p-2">
      <div className="flex-1 h-full w-full bg-gray-100 border border-gray-200 rounded-[20px] flex items-center justify-center">
        <div className="w-[380px] max-h-[400px] h-auto flex flex-col justify-center items-center gap-5 p-5 shadow-default bg-white rounded-[32px]">
          <Image
            src={`/icons/not-found.svg`}
            alt="not-found"
            width={200}
            height={200}
            priority
            quality={75}
            loading="eager"
            className="h-[200px] w-[200px] object-contain"
          />
          <div className="flex flex-col gap-2 items-center">
            <span className="font-semibold text-lg text-center text-gray-800">
              <FormattedMessage defaultMessage="Không tìm thấy trang" id="screens.errors.NotFound.164136959" />
            </span>
            <span className="font-normal text-sm text-center text-gray-700">
              <FormattedMessage
                defaultMessage="Rất tiếc, không thể tìm thấy trang bạn đang tìm kiếm. Có thể trang đã
              bị xóa, hoặc bạn đã nhập sai đường dẫn. Vui lòng kiểm tra lại URL hoặc
              thử lại sau."
                id="screens.errors.NotFound.1244941653"
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
