{"app": {"led": {"[led_key]": {"(configure)": {"config": {"default-data": {"page": {"1079198337": "When you configure default data, default values will be displayed if data fields are empty.", "1132865453": "Default data configuration cannot be saved. Please check again later.", "1194256638": "Default data configuration has been saved successfully.", "1243419333": "<PERSON><PERSON><PERSON>", "1259021402": "Enter subtitle", "1573266568": "Warning color", "1604652103": "Good", "163121484": "Tend to exceed threshold", "*********": "Default data configuration", "1715188257": "Slide subtitle", "1879174810": "Do not enter more than 100 characters for slide subtitle", "1995737682": "Slide title", "393704478": "Lost connection", "412702605": "Enter title", "775148511": "Do not enter more than 72 characters for slide title.", "802954264": "Exceed threshold"}}, "layout": {"1669473708": "General configuration"}, "page": {"1026397723": "Public", "1085387155": "Allow the LED display data to be shown on other digital platforms via embed code.", "1327345319": "LED display public sharing has been activated successfully.", "1385638768": "LED display public sharing cannot be activated. Please check again later.", "1386742679": "Security password", "1402581559": "Only those who have been given a security password can access and view data in this LED display.", "1582789939": "LED display public sharing has been disabled successfully.", "1751753589": "LED display sharing URL", "1826411627": "Share LED display publicly", "1977774081": "Everyone else will be able to view datas in this LED display without logging in or requesting any permissions.", "2023325899": "Copy", "2024700209": "Board size", "2059658596": "Copy embed code", "268258777": "LED display sharing password has been copied successfully.", "30093112": "LED display embed code has been copied successfully.", "637000790": "LED display public sharing cannot be disabled. Please check again later.", "729825786": "Share LED display", "733065223": "Restricted access", "83529539": "LED display sharing URL has been copied successfully.", "903185644": "Embed LED display"}, "slide-shows": {"page": {"1043319462": "Save configuration", "1067860744": "Default display", "1103570150": "Slide configuration cannot be saved. Please check again later.", "1227310252": "Select font", "*********": "Slide configuration", "129890065": "Slide configuration has been saved successfully.", "1372795006": "Border color", "1509228774": "Font", "1674427239": "The minimum slide transition speed is set to 5 seconds.", "1779980518": "Slide transition speed (seconds)", "1845380780": "Text color", "1845624381": "Background color", "658262262": "Grid preview", "664844862": "Table preview"}}}, "presentations": {"page": {"1043319462": "Save configuration", "1103570150": "Slide configuration cannot be saved. Please check again later.", "*********": "Slide configuration", "129890065": "Slide configuration has been saved successfully.", "**********": "Invalid size. Please enter a value between 320 px and 3840 px.", "**********": "Display header", "**********": "Enable/disable display", "**********": "Cannot switch to default value configuration. Please try again later.", "*********": "Add slide", "*********": "Slide name", "*********": "Display footer", "*********": "Display content"}}}}}}, "components": {"commons": {"ContentChangedBase": {"*********": "Confirm", "*********": "Cancel"}}, "container": {"accounts": {"components": {"ConfirmChangeLanguage": {"**********": "<span>Some languages you don't have content set up yet. Would you like to quickly set them up with the content \"<strong>{valueSuggest}</strong>\" for these languages?</span>", "**********": "Some languages are still empty."}}}, "sharing": {"form-auth": {"**********": "Welcome to"}}}, "sessions": {"ComingSoon": {"**********": "Feature is under development, expected to be released in the near future."}, "HeaderConfig": {"**********": "LED display sharing configuration", "*********": "Slide configuration", "*********": "Default data configuration", "**********": "Slide design"}}, "ui": {"ContentChangedBase": {"*********": "Confirm", "*********": "Cancel"}, "UploadFile": {"**********": "You can upload a file in JPG, PNG, or SVG format.", "*********": "Are you sure you want to delete this {label}? Please confirm if you would like to proceed.", "**********": "File cannot be uploaded. Please check again later.", "**********": "Please select file with total size less than {maxSize}.", "2340486": "Select", "*********": "Confirm", "606995948": "Uploading avatar...", "62368309": "Confirm delete {label}", "745843351": "Avatar", "778747531": "Avatar uploaded.", "*********": "Cancel", "926063465": "File format not supported. Please choose another file."}, "input-language": {"1225600157": "Vietnamese", "1488782352": "Language settings", "1782945719": "Language", "216897238": "Save setting", "768903020": "Content", "901769905": "Enter content"}}}, "constants": {"defineMessages": {"1790118527": "{field} cannot be empty.", "852565916": "Do not enter more than {max} characters."}, "nav": {"1023776734": "Slide", "1669473708": "General configuration", "221760357": "Default data", "729825786": "Share LED display"}}, "screens": {"auth": {"AuthPage": {"1639349273": "Security password is incorrect. Please re-enter.", "1800833175": "Please enter your security password to access.", "1926033320": "Access", "1987375752": "Security password cannot be empty.", "2112159354": "Enter security password"}}, "errors": {"NotFound": {"1244941653": "Sorry, page you are looking for cannot be found. It may have been deleted, or you may have entered an incorrect URL. Please check the URL or try again later.", "164136959": "Page not found"}}, "presentations": {"CardSlider": {"140579658": "Are you sure you want to delete this slide? This action will permanently delete the slide and cannot be restored. Please confirm if you would like to proceed.", "1410546157": "Duplicate", "1528339624": "<span>Click</span> to open menu", "1548813057": "<span>Drag</span> to move", "1788853769": "Content not configured", "1907308715": "This slide will not be displayed in presentation mode.", "2027417406": "Hide slide", "410358751": "Display slide", "448583877": "Confirm slide deletion", "92198": "Deleted"}, "ConfigSlide": {"1191347981": "Enter slide name", "1435964239": "Font size", "1454429291": "Data type", "1564152116": "Data received time", "1672924925": "Subtitle", "1801769058": "Custom title", "1975998546": "Customize", "206334904": "Display configuration", "257768898": "Overview configuration", "320775361": "Automatically select appropriate size", "430321621": "Custom subtitle", "440955834": "Header", "466595280": "Title", "641803552": "Size configuration", "672150323": "Footer", "745843351": "Avatar", "768903020": "Content"}, "ContentConfigSlide": {"1036745043": "Monitoring station name", "1115456805": "Image", "11543690": "Display components", "1242488578": "Threshold limit", "1298622127": "Display value warning color", "1436455565": "Parameter name", "1480446057": "Real-time data", "1722724199": "Parameter value", "1913531627": "Warning color legend", "2020945267": "Parameter unit", "2056201202": "Text", "251967402": "Column title", "261939202": "T<PERSON><PERSON>old exceeded warning", "291032092": "The system will automatically adjust content size to fit screen dimensions.", "320775361": "Automatically select appropriate size", "355114431": "Approaching threshold warning", "43299210": "Historical data", "444330240": "Size configuration", "523491325": "Bạn có chắc muốn thay đổi loại dữ liệu này không? Các thông tin mà bạn vừa nhập sẽ không được lưu lại.", "83510683": "<PERSON><PERSON><PERSON> nhận thay đổi loại dữ liệu"}, "ContentSlider": {"1516263099": "Please select monitoring station, parameter information to display data on screen.", "1564152116": "Data received time", "1657403361": "Enter title content", "1746006750": "Please configure content to be displayed on screen.", "1788853769": "Content not configured", "613863636": "Enter subtitle content"}, "FooterConfigSlide": {"1461773419": "Current time", "1909588997": "Scrolling text banner", "206334904": "Display configuration", "768903020": "Content", "901769905": "Enter content"}, "HeaderConfigSlide": {"1145935829": "Size", "1155738296": "Enter custom subtitle", "1480117793": "Enter custom title", "1590576924": "If custom title and custom subtitle have no data. Default values will be replaced.", "1672924925": "Subtitle", "270961639": "Cannot disable last display configuration.", "291032092": "The system will automatically adjust content size to fit screen dimensions.", "466595280": "Title", "595062375": "Allows customization of content size displayed on screen. The system will not automatically adjust size.", "767979549": "Do you want to save changes before switching to default data configuration? Please click confirm to save and continue.", "768760477": "Switch to default data configuration", "768903020": "Content", "817909374": "Default data configuration.", "885104111": "Time"}, "PreviewPresentation": {"165867170": "Please select video to display content on screen.", "859523826": "No video is currently displayed"}, "compoType": {"HistoriesOptions": {"1192966251": "Evaluation column", "1242488578": "Threshold limit", "1803181196": "Value", "2020945267": "Parameter unit"}, "HistoryType": {"1244918798": "From date", "1371798518": "Display calculation formula", "1576522092": "Display time", "1819193952": "Last 7 days", "2026936738": "Yesterday", "2026940217": "Today", "411173370": "Last 30 days", "442280563": "Last 15 days", "967860450": "Daily average"}, "ImagesOptions": {"211939211": "Display mode"}, "RealtimeType": {"1057146701": "Display layout", "1288792407": "Some cameras will be hidden", "1597700344": "Empty list", "1619163834": "Monitoring parameter", "1630238613": "Station name", "1696311895": "Max rows (2-4)", "1787698267": "Select data type", "1835055841": "Please adjust the number of cameras or the display layout to ensure all cameras can be shown in slides.", "2135278082": "Displayed camera", "214170661": "Select station type", "220788582": "Camera configuration", "2924242": "Grid", "375683652": "Select station", "478006785": "Select camera", "64631071": "Type of station", "64631072": "No data", "64631074": "No data", "64631075": "No data", "665826750": "Display camera", "9506842": "Table", "988057983": "Max columns (2-4)"}, "VideoType": {"1716374367": "Uploading video...", "305657034": "Video uploaded.", "343718065": "You can upload one MP4 video file with a size less than 25MB."}}, "components": {"BoxCamera": {"2036303983": "Please select camera to display"}, "GridConfigTemplate": {"100098174": "Custom layout", "118472795": "Applies to devices with large screens (> 960 px), such as desktops or laptops.", "1415847971": "Allows customizing the number of displayed columns and rows across all devices.", "1696341717": "Max rows (1-3)", "1929447243": "Parameter layout", "2096532185": "Applies to devices with small screens (≤ 480 px), such as mobile phones.", "56747109": "Applies to devices with medium screens (481 px – 960 px), such as tablets.", "910370731": "Camera layout", "940681017": "General layout"}, "GridItem": {"1038629890": "Limit: {minLimit} -> {maxLimit}", "235960741": "No limit", "810771583": "Limit: ≥ {value}", "996682880": "Limit: ≤ {value}"}, "ImageUpload": {"1180100933": "image"}, "TableConfigTemplate": {"1314136401": "Max columns {value}", "281785528": "Max rows {value}"}, "TableHistories": {"1625784854": "Evaluation", "785718693": "Limit", "885104111": "Time"}, "TableItem": {"303474035": "{minLimit} -> {maxLimit}", "882401620": "≥ {value}", "924052843": "≤ {value}"}}, "contentType": {"ContentImage": {"399412569": "Please select image to display"}, "ContentRealtime": {"130682665": "Tend to exceed", "1436455565": "Parameter name", "1603181196": "Value", "1604652103": "Good", "414377960": "Unit", "785718693": "Limit", "802954264": "Exceed threshold"}, "ContentText": {"1260435920": "<PERSON><PERSON> lòng nhập nội dung sẽ hiển thị trên màn hình.", "160664721": "<PERSON><PERSON><PERSON> có nội dung hiển thị"}}}}}