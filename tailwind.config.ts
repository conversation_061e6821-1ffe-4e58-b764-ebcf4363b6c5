import type { Config } from 'tailwindcss';

export default {
  content: [
    './node_modules/ui-components/dist/**/*.{js,ts,jsx,tsx,mdx,css}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './screens/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          '50': 'rgb(var(--primary-50) / <alpha-value>)',
          '100': 'rgb(var(--primary-100) / <alpha-value>)',
          '200': 'rgb(var(--primary-200) / <alpha-value>)',
          '300': 'rgb(var(--primary-300) / <alpha-value>)',
          '400': 'rgb(var(--primary-400) / <alpha-value>)',
          '500': 'rgb(var(--primary-500) / <alpha-value>)',
          '600': 'rgb(var(--primary-600) / <alpha-value>)',
          '700': 'rgb(var(--primary-700) / <alpha-value>)',
          '800': 'rgb(var(--primary-800) / <alpha-value>)',
          '900': 'rgb(var(--primary-900) / <alpha-value>)',
          '950': 'rgb(var(--primary-950) / <alpha-value>)',
        },
        gray: {
          '50': 'rgb(var(--gray-50) / <alpha-value>)',
          '100': 'rgb(var(--gray-100) / <alpha-value>)',
          '200': 'rgb(var(--gray-200) / <alpha-value>)',
          '300': 'rgb(var(--gray-300) / <alpha-value>)',
          '400': 'rgb(var(--gray-400) / <alpha-value>)',
          '500': 'rgb(var(--gray-500) / <alpha-value>)',
          '600': 'rgb(var(--gray-600) / <alpha-value>)',
          '700': 'rgb(var(--gray-700) / <alpha-value>)',
          '800': 'rgb(var(--gray-800) / <alpha-value>)',
          '900': 'rgb(var(--gray-900) / <alpha-value>)',
          '950': 'rgb(var(--gray-950) / <alpha-value>)',
        },
        red: {
          '50': 'rgb(var(--red-50) / <alpha-value>)',
          '100': 'rgb(var(--red-100) / <alpha-value>)',
          '200': 'rgb(var(--red-200) / <alpha-value>)',
          '300': 'rgb(var(--red-300) / <alpha-value>)',
          '400': 'rgb(var(--red-400) / <alpha-value>)',
          '500': 'rgb(var(--red-500) / <alpha-value>)',
          '600': 'rgb(var(--red-600) / <alpha-value>)',
          '700': 'rgb(var(--red-700) / <alpha-value>)',
          '800': 'rgb(var(--red-800) / <alpha-value>)',
          '900': 'rgb(var(--red-900) / <alpha-value>)',
          '950': 'rgb(var(--red-950) / <alpha-value>)',
        },
        pink: {
          '50': 'rgb(var(--pink-50) / <alpha-value>)',
          '100': 'rgb(var(--pink-100) / <alpha-value>)',
          '200': 'rgb(var(--pink-200) / <alpha-value>)',
          '300': 'rgb(var(--pink-300) / <alpha-value>)',
          '400': 'rgb(var(--pink-400) / <alpha-value>)',
          '500': 'rgb(var(--pink-500) / <alpha-value>)',
          '600': 'rgb(var(--pink-600) / <alpha-value>)',
          '700': 'rgb(var(--pink-700) / <alpha-value>)',
          '800': 'rgb(var(--pink-800) / <alpha-value>)',
          '900': 'rgb(var(--pink-900) / <alpha-value>)',
          '950': 'rgb(var(--pink-950) / <alpha-value>)',
        },
        orange: {
          '50': 'rgb(var(--orange-50) / <alpha-value>)',
          '100': 'rgb(var(--orange-100) / <alpha-value>)',
          '200': 'rgb(var(--orange-200) / <alpha-value>)',
          '300': 'rgb(var(--orange-300) / <alpha-value>)',
          '400': 'rgb(var(--orange-400) / <alpha-value>)',
          '500': 'rgb(var(--orange-500) / <alpha-value>)',
          '600': 'rgb(var(--orange-600) / <alpha-value>)',
          '700': 'rgb(var(--orange-700) / <alpha-value>)',
          '800': 'rgb(var(--orange-800) / <alpha-value>)',
          '900': 'rgb(var(--orange-900) / <alpha-value>)',
          '950': 'rgb(var(--orange-950) / <alpha-value>)',
        },
        yellow: {
          '50': 'rgb(var(--yellow-50) / <alpha-value>)',
          '100': 'rgb(var(--yellow-100) / <alpha-value>)',
          '200': 'rgb(var(--yellow-200) / <alpha-value>)',
          '300': 'rgb(var(--yellow-300) / <alpha-value>)',
          '400': 'rgb(var(--yellow-400) / <alpha-value>)',
          '500': 'rgb(var(--yellow-500) / <alpha-value>)',
          '600': 'rgb(var(--yellow-600) / <alpha-value>)',
          '700': 'rgb(var(--yellow-700) / <alpha-value>)',
          '800': 'rgb(var(--yellow-800) / <alpha-value>)',
          '900': 'rgb(var(--yellow-900) / <alpha-value>)',
          '950': 'rgb(var(--yellow-950) / <alpha-value>)',
        },
        green: {
          '50': 'rgb(var(--green-50) / <alpha-value>)',
          '100': 'rgb(var(--green-100) / <alpha-value>)',
          '200': 'rgb(var(--green-200) / <alpha-value>)',
          '300': 'rgb(var(--green-300) / <alpha-value>)',
          '400': 'rgb(var(--green-400) / <alpha-value>)',
          '500': 'rgb(var(--green-500) / <alpha-value>)',
          '600': 'rgb(var(--green-600) / <alpha-value>)',
          '700': 'rgb(var(--green-700) / <alpha-value>)',
          '800': 'rgb(var(--green-800) / <alpha-value>)',
          '900': 'rgb(var(--green-900) / <alpha-value>)',
          '950': 'rgb(var(--green-950) / <alpha-value>)',
        },
        teal: {
          '50': 'rgb(var(--teal-50) / <alpha-value>)',
          '100': 'rgb(var(--teal-100) / <alpha-value>)',
          '200': 'rgb(var(--teal-200) / <alpha-value>)',
          '300': 'rgb(var(--teal-300) / <alpha-value>)',
          '400': 'rgb(var(--teal-400) / <alpha-value>)',
          '500': 'rgb(var(--teal-500) / <alpha-value>)',
          '600': 'rgb(var(--teal-600) / <alpha-value>)',
          '700': 'rgb(var(--teal-700) / <alpha-value>)',
          '800': 'rgb(var(--teal-800) / <alpha-value>)',
          '900': 'rgb(var(--teal-900) / <alpha-value>)',
          '950': 'rgb(var(--teal-950) / <alpha-value>)',
        },
        sky: {
          '50': 'rgb(var(--sky-50) / <alpha-value>)',
          '100': 'rgb(var(--sky-100) / <alpha-value>)',
          '200': 'rgb(var(--sky-200) / <alpha-value>)',
          '300': 'rgb(var(--sky-300) / <alpha-value>)',
          '400': 'rgb(var(--sky-400) / <alpha-value>)',
          '500': 'rgb(var(--sky-500) / <alpha-value>)',
          '600': 'rgb(var(--sky-600) / <alpha-value>)',
          '700': 'rgb(var(--sky-700) / <alpha-value>)',
          '800': 'rgb(var(--sky-800) / <alpha-value>)',
          '900': 'rgb(var(--sky-900) / <alpha-value>)',
          '950': 'rgb(var(--sky-950) / <alpha-value>)',
        },
        indigo: {
          '50': 'rgb(var(--indigo-50) / <alpha-value>)',
          '100': 'rgb(var(--indigo-100) / <alpha-value>)',
          '200': 'rgb(var(--indigo-200) / <alpha-value>)',
          '300': 'rgb(var(--indigo-300) / <alpha-value>)',
          '400': 'rgb(var(--indigo-400) / <alpha-value>)',
          '500': 'rgb(var(--indigo-500) / <alpha-value>)',
          '600': 'rgb(var(--indigo-600) / <alpha-value>)',
          '700': 'rgb(var(--indigo-700) / <alpha-value>)',
          '800': 'rgb(var(--indigo-800) / <alpha-value>)',
          '900': 'rgb(var(--indigo-900) / <alpha-value>)',
          '950': 'rgb(var(--indigo-950) / <alpha-value>)',
        },
        purple: {
          '50': 'rgb(var(--purple-50) / <alpha-value>)',
          '100': 'rgb(var(--purple-100) / <alpha-value>)',
          '200': 'rgb(var(--purple-200) / <alpha-value>)',
          '300': 'rgb(var(--purple-300) / <alpha-value>)',
          '400': 'rgb(var(--purple-400) / <alpha-value>)',
          '500': 'rgb(var(--purple-500) / <alpha-value>)',
          '600': 'rgb(var(--purple-600) / <alpha-value>)',
          '700': 'rgb(var(--purple-700) / <alpha-value>)',
          '800': 'rgb(var(--purple-800) / <alpha-value>)',
          '900': 'rgb(var(--purple-900) / <alpha-value>)',
          '950': 'rgb(var(--purple-950) / <alpha-value>)',
        },
      },
      fontFamily: {
        inter: ['Inter', 'sans-serif'],
        montserrat: ['Montserrat', 'sans-serif'],
        tektur: ['Tektur', 'sans-serif'],
        'jetbrains-mono': ['JetBrains Mono', 'monospace'],
        'be-vietnam-pro': ['Be Vietnam Pro', 'sans-serif'],
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
        spinerCircle: {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(359deg)' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        spinerCircle: 'spinerCircle 0.6s infinite linear',
      },
      boxShadow: {
        c1: '0px 12px 32px 0px rgba(0, 0, 0, 0.2)',
        c2: '0px 4px 8px 0px rgba(0, 0, 0, 0.2)',
        c3: '0px 1px 4px 0px rgba(0, 0, 0, 0.2)',
        c4: '0px 6px 16px 0px rgba(0, 0, 0, 0.2)',
        c5: '0px 14px 32px 0px rgba(0, 0, 0, 0.12)',
        c6: '0px 2px 4px 0px rgba(0, 0, 0, 0.2)',
        c7: '0px 20px 64px 0px rgba(0, 0, 0, 0.16)',
        default: '0px 20px 64px 0px #00000029',
      },
      screens: {
        'mobile-short-height': {
          raw: '(max-width: 480px) and ((max-aspect-ratio: 9/20) or (min-aspect-ratio: 9/16))',
        },
        '3xl': {
          raw: '(min-width: 1920px)',
        },
        '4xl': {
          raw: '(min-width: 2560px)',
        },
        '5xl': {
          raw: '(min-width: 3200px)',
        },
        '6xl': {
          raw: '(min-width: 3840px)',
        },
        '7xl': {
          raw: '(min-width: 4096px)',
        },
        '8xl': {
          raw: '(min-width: 5120px)',
        },
        '9xl': {
          raw: '(min-width: 7680px)',
        },
        '10xl': {
          raw: '(min-width: 8192px)',
        },
      },
    },
  },
  plugins: [
    function ({ addUtilities }: any) {
      const newUtilities = {
        '.nth-child-n2-boder-left': {
          '&:nth-child(n+2)': {
            /* Các thuộc tính Tailwind bạn muốn áp dụng */
            borderLeft: '1px solid #E4E4E7',
          },
        },
      };
      addUtilities(newUtilities);
    },
  ],
} satisfies Config;
