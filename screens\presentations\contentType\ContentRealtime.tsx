import { ContentLayout, GridView } from '@/constants';
import { usePagination } from '@/hooks/usePagination';
import { useLedConfigStores, useLedStores, useSlideConfigStores } from '@/stores';
import { ICameraList, IDataStation, IMeasuring } from '@/types';
import { EContentType, SlideConfigDto, RealTimeMetadata, IConfigLayoutData } from '@/types/slide';
import { paginateArray } from '@/utils';
import { cn } from '@/utils/tailwind';
import { EmblaCarouselType } from 'embla-carousel';
import Fade from 'embla-carousel-fade';
import useEmblaCarousel from 'embla-carousel-react';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import BoxCamera from '../components/BoxCamera';
import GridItem from '../components/GridItem';
import NotConfigure from '../components/NotConfigure';
import { ResponsiveTextWithOverride } from '../components/ResponsiveTextWithOverride';
import TableItem from '../components/TableItem';

type Props = {
  dataSlide: SlideConfigDto;
  isIframeReady: boolean;
  setEmblaApi: (emblaApi: EmblaCarouselType) => void;
};

enum Tag {
  desktop = 'desktop',
  tablet = 'tablet',
  mobile = 'mobile',
}

export default function ContentRealtime({ dataSlide, isIframeReady, setEmblaApi }: Props) {
  const dataLedDetail = useLedStores((store) => store.dataLedDetail);
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const config = useSlideConfigStores((store) => store.config);
  const defaultConfig = useMemo(() => {
    return {
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    };
  }, [dataConfig]);
  const { width } = config;
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: false, watchDrag: false, containScroll: false }, [Fade()]);
  // Use pagination hook but we don't need selectedIndex for now
  usePagination(emblaApi);

  const dataContentSlide = useMemo(() => dataSlide.content, [dataSlide]) as {
    type: EContentType.realtime;
    metadata: RealTimeMetadata;
  };

  const slide = useMemo(() => dataSlide, [dataSlide]);

  const layout = useMemo(
    () => dataContentSlide.metadata?.layout?.template,
    [dataContentSlide.metadata?.layout?.template],
  );
  const dataItems = useMemo(() => {
    return dataContentSlide.metadata?.measure ?? [];
  }, [slide]);

  const getDeviceFromSize = useCallback((width: number): Tag => {
    if (width <= 480) {
      return Tag.mobile;
    }
    if (width > 480 && width <= 960) {
      return Tag.tablet;
    }
    return Tag.desktop;
  }, []);

  const device = useMemo(() => getDeviceFromSize(width), [width]);

  const configGrid = useMemo(() => {
    const view = dataContentSlide.metadata?.layout.config.view;
    if (view === GridView.general) {
      const settings = dataContentSlide.metadata?.layout?.config.settings as IConfigLayoutData;
      return {
        column: settings.column ?? 7,
        row: settings.row ?? 1,
        cameraPerPage: settings?.cameraPerPage ?? 3,
      };
    }
    const settingsCustom = dataContentSlide.metadata?.layout?.config.settings as {
      desktop: IConfigLayoutData;
      mobile: IConfigLayoutData;
      tablet: IConfigLayoutData;
    };
    return {
      column: settingsCustom?.[device].column ?? 7,
      row: settingsCustom?.[device].row ?? 1,
      cameraPerPage: settingsCustom?.[device].cameraPerPage ?? 3,
    };
  }, [dataContentSlide, device]);

  const pages = useMemo(() => {
    return paginateArray(dataItems, configGrid.column * configGrid.row);
  }, [dataItems, configGrid, slide]);

  // Fix: Remove 'pages' from dependency array to prevent state updates during render
  // and use a ref to track if we've already set the emblaApi
  const hasSetEmblaApiRef = useRef(false);

  useEffect(() => {
    if (emblaApi && isIframeReady && !hasSetEmblaApiRef.current) {
      hasSetEmblaApiRef.current = true;
      setEmblaApi(emblaApi);
    }
  }, [emblaApi, setEmblaApi, isIframeReady]); // Removed 'pages' from dependencies

  // Reset the ref when emblaApi changes
  useEffect(() => {
    if (!emblaApi) {
      hasSetEmblaApiRef.current = false;
    }
  }, [emblaApi]);

  const dataStations = useMemo(
    () => dataLedDetail?.dataStations.find((station) => station.key === dataContentSlide.metadata?.station),
    [dataLedDetail, dataContentSlide],
  );

  const camera = useMemo(() => {
    return (dataContentSlide.metadata?.cameras?.map(
      (cameraId) => dataStations?.cameras?.find((camera) => camera._id === cameraId) || null,
    ) || undefined) as ICameraList[];
  }, [dataContentSlide, dataStations]);

  const pageCameras = useMemo(() => {
    return paginateArray(camera ?? [], configGrid.cameraPerPage * 1);
  }, [camera, configGrid.cameraPerPage]);

  return (
    <div className="h-full w-full overflow-hidden bg-transparent" ref={emblaRef}>
      <div className={cn('embla__container flex h-full w-full bg-transparent')}>
        {pages.length > 0 ? (
          pages.map((page, index) => (
            <div key={index} className="embla__slide flex-[0_0_100%] min-w-0 h-full bg-transparent">
              <div className="flex-1 h-full w-full flex flex-col">
                {(dataContentSlide.metadata?.showOptions?.isShowStationName ||
                  dataContentSlide.metadata?.showOptions?.isThresholdApproaching ||
                  dataContentSlide.metadata?.showOptions?.isThresholdExceeded) && (
                  <div
                    className={cn(
                      'flex-shrink-0 flex flex-col border-b-2 w-full',
                      'landscape:flex-row landscape:items-center landscape:justify-between',
                    )}
                    style={{
                      borderColor: defaultConfig.slideConfig?.borderColor,
                    }}
                  >
                    <div
                      className={cn(
                        'flex-shrink-0 min-h-0 px-1 flex items-center',
                        'landscape:flex-1 landscape:min-h-0 landscape:h-full landscape:min-w-0',
                        'sm:px-2',
                        'md:px-3',
                        'lg:px-[10px]',
                        'xl:px-[14px]',
                        '2xl:px-4',
                        '3xl:px-5',
                        {
                          'hidden sm:opacity-0 sm:flex': !dataContentSlide.metadata?.showOptions?.isShowStationName,
                        },
                      )}
                    >
                      <div className="h-full w-full flex flex-col justify-center">
                        <ResponsiveTextWithOverride
                          className="font-semibold truncate"
                          value={dataContentSlide.metadata.layout.config.fontSize.config.stationName}
                        >
                          {dataStations?.name?.[locale]}
                        </ResponsiveTextWithOverride>
                      </div>
                    </div>
                    {(dataContentSlide.metadata?.showOptions?.isThresholdApproaching ||
                      dataContentSlide.metadata?.showOptions?.isThresholdExceeded) && (
                      <div
                        className={cn(
                          'flex-shrink-0 flex flex-row gap-1 px-1',
                          'landscape:h-full landscape:flex landscape:justify-end',
                          'sm:px-2 sm:gap-2',
                          'md:px-3 md:gap-3',
                          'lg:px-[10px]',
                          'xl:px-[14px]',
                          '2xl:px-4',
                          '3xl:px-5',
                        )}
                      >
                        <ResponsiveTextWithOverride
                          className="h-full w-fit flex flex-row items-center font-normal"
                          value={dataContentSlide.metadata.layout.config.fontSize.config.legendWarning}
                        >
                          <svg
                            height={'1.2em'}
                            width={'1em'}
                            className="h-[1.2em] w-[1em] flex-shrink-0 text-[100%]"
                            viewBox="0 0 1 6"
                            xmlns="http://www.w3.org/2000/svg"
                            style={{
                              fill: defaultConfig.init?.colors.color_within_limit,
                            }}
                          >
                            <rect x="0" y="0" width="2" height="6" />
                          </svg>
                          <span className="font-normal w-fit text-current text-[100%]">
                            <FormattedMessage
                              defaultMessage="Trong ngưỡng"
                              id="screens.presentations.contentType.ContentRealtime.1604652103"
                            />
                          </span>
                        </ResponsiveTextWithOverride>
                        {dataContentSlide.metadata?.showOptions?.isThresholdApproaching && (
                          <ResponsiveTextWithOverride
                            className="h-full w-fit flex flex-row items-center font-normal"
                            value={dataContentSlide.metadata.layout.config.fontSize.config.legendWarning}
                          >
                            <svg
                              height={'1.2em'}
                              width={'1em'}
                              className="h-[1.2em] w-[1em] flex-shrink-0 text-[100%]"
                              viewBox="0 0 1 6"
                              xmlns="http://www.w3.org/2000/svg"
                              style={{
                                fill: defaultConfig.init?.colors.color_near_limit,
                              }}
                            >
                              <rect x="0" y="0" width="2" height="6" />
                            </svg>
                            <span className="font-normal w-fit text-current text-[100%]">
                              <FormattedMessage
                                defaultMessage="Chuẩn bị vượt"
                                id="screens.presentations.contentType.ContentRealtime.130682665"
                              />
                            </span>
                          </ResponsiveTextWithOverride>
                        )}
                        {dataContentSlide.metadata?.showOptions?.isThresholdExceeded && (
                          <ResponsiveTextWithOverride
                            className="h-full w-fit flex flex-row items-center font-normal"
                            value={dataContentSlide.metadata.layout.config.fontSize.config.legendWarning}
                          >
                            <svg
                              height={'1.2em'}
                              width={'1em'}
                              className="h-[1.2em] w-[1em] flex-shrink-0 text-[100%]"
                              viewBox="0 0 1 6"
                              xmlns="http://www.w3.org/2000/svg"
                              style={{
                                fill: defaultConfig.init?.colors.color_over_limit,
                              }}
                            >
                              <rect x="0" y="0" width="2" height="6" />
                            </svg>
                            <span className="font-normal w-fit text-current text-[100%]">
                              <FormattedMessage
                                defaultMessage="Vượt ngưỡng"
                                id="screens.presentations.contentType.ContentRealtime.802954264"
                              />
                            </span>
                          </ResponsiveTextWithOverride>
                        )}
                      </div>
                    )}
                  </div>
                )}
                <div className="flex-1 min-h-0">
                  <LayoutRender
                    configGrid={configGrid}
                    dataSlide={dataSlide}
                    currentItems={page}
                    dataStations={dataStations!}
                    device={device}
                    currentCamera={pageCameras?.[index] ?? []}
                    camera={camera}
                    layout={layout}
                  />
                </div>
              </div>
            </div>
          ))
        ) : (
          <NotConfigure />
        )}
      </div>
    </div>
  );
}

type PropsLayoutRender = {
  configGrid: any;
  currentItems: any;
  dataStations: IDataStation;
  dataSlide: SlideConfigDto;
  device: string;
  currentCamera: ICameraList[];
  camera: ICameraList[] | undefined;
  layout: 'grid' | 'table';
};

const LayoutRender = ({
  configGrid,
  currentItems,
  dataStations,
  dataSlide,
  device,
  currentCamera,
  camera,
  layout,
}: PropsLayoutRender) => {
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );

  const metaRealtime = dataSlide.content.metadata as RealTimeMetadata;

  const layoutRender = useMemo(() => {
    if (layout === ContentLayout.grid) {
      return (
        <div
          className={cn('flex flex-col gap-[2px] h-full w-full landscape:flex-row')}
          style={{ background: defaultConfig.slideConfig?.borderColor }}
        >
          <div
            className={cn('flex-1 grid min-h-0 w-full gap-[2px]')}
            style={{
              gridTemplateColumns: `repeat(${configGrid.column}, minmax(0, 1fr))`,
              gridTemplateRows: `repeat(${configGrid.row}, minmax(0, 1fr))`,
            }}
          >
            {Array.from({ length: configGrid.column * configGrid.row }).map((_, index) => {
              if (currentItems[index]?.length > 0) {
                const dataItem = dataStations?.measuringList
                  .map((measure) => {
                    const measuringLogs = dataStations.lastLog?.measuringLogs[measure.key];
                    return {
                      ...measure,
                      value: measuringLogs?.value || null,
                    };
                  })
                  ?.find((item) => item.key === currentItems[index]) as IMeasuring & {
                  value: number | null;
                };
                return (
                  <GridItem
                    totalItem={configGrid.column * configGrid.row}
                    key={index}
                    dataSlide={dataSlide}
                    dataItem={dataItem}
                  />
                );
              }
              return (
                <div
                  key={index}
                  style={{
                    background: defaultConfig.slideConfig?.background,
                  }}
                ></div>
              );
            })}
          </div>

          {camera !== undefined ? (
            <div
              className={cn(
                'flex-shrink-0 h-[20dvh] min-h-[20dvh] flex flex-row items-center gap-[2px] w-full',
                'landscape:flex-col landscape:h-full landscape:min-w-[25dvw] landscape:w-[25dvw]',
                'mobile-short-height:hidden',
              )}
            >
              {Array.from({ length: configGrid.cameraPerPage }).map((_, index) => {
                return (
                  <BoxCamera
                    key={index}
                    fontSize={metaRealtime?.layout.config.fontSize.config.limitThreshold}
                    className="flex-1 w-full h-full min-w-0 min-h-0 bg-black"
                    camera={currentCamera?.[index]}
                  />
                );
              })}
            </div>
          ) : null}
        </div>
      );
    }

    return (
      <TableItem
        configGrid={configGrid}
        currentItems={currentItems}
        dataStations={dataStations!}
        dataSlide={dataSlide}
        cameras={currentCamera}
        camera={camera}
      />
    );
  }, [configGrid, currentItems, dataStations, dataSlide, device, currentCamera]);

  return layoutRender;
};
