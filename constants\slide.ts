import {
  ETimeRange,
  ETypeTimeRange,
  HistoriesContent,
  ImageContent,
  ImageMode,
  ISlide,
  RealtimeContent,
  TextContent,
  VideoContent,
} from '@/types/slide';
import dayjs from 'dayjs';
import { ContentLayout, DATETIME_FORMAT, footerType, GridView } from '.';

export const initValueHeader: ISlide['header'] = {
  show: true,
  fontSize: {
    auto: true,
    config: {
      title: 18,
      description: 14,
      datetime: 16,
    },
  },
  title: {
    vi: '',
    en: '',
  },
  sub_title: {
    vi: '',
    en: '',
  },
  showOptions: {
    isDataSynced: false,
    showAvatar: true,
    showSubTitle: true,
    showTitle: true,
  },
};

export const initValueFooter: ISlide['footer'] = {
  show: true,
  type: footerType.bannerText,
  config: {
    background: '#000000',
    color: '#FFFFFF',
  },
  bannerText: {
    content: {
      vi: '',
      en: '',
    },
  },
  timestamp: {
    content: {
      vi: dayjs().format(DATETIME_FORMAT),
      en: dayjs().format(DATETIME_FORMAT),
    },
  },
  fontSize: {
    auto: true,
    config: {
      content: 22,
    },
  },
};

export const initContentRealtime: RealtimeContent = {
  stationType: '',
  station: '',
  measure: [],
  camera: {
    isShow: false,
    list: [],
  },
  layout: {
    template: ContentLayout.grid,
    grid: {
      view: GridView.general,
      general: {
        column: 3,
        row: 3,
        cameraPerPage: 3,
      },
      custom: {
        desktop: {
          column: 3,
          row: 3,
          cameraPerPage: 3,
        },
        mobile: { column: 3, row: 3, cameraPerPage: 3 },
        tablet: { column: 3, row: 3, cameraPerPage: 3 },
      },
      fontSize: {
        auto: true,
        config: {
          stationName: 16,
          measureCode: 28,
          limitThreshold: 24,
          measureValue: 44,
          measureUnit: 24,
          legendWarning: 12,
        },
      },
    },
    table: {
      view: GridView.general,
      general: {
        column: 7,
        row: 1,
        cameraPerPage: 3,
      },
      custom: {
        desktop: {
          column: 7,
          row: 1,
          cameraPerPage: 3,
        },
        mobile: { column: 7, row: 1, cameraPerPage: 3 },
        tablet: { column: 7, row: 1, cameraPerPage: 3 },
      },
      fontSize: {
        auto: true,
        config: {
          headerTable: 16,
          stationName: 16,
          measureCode: 20,
          limitThreshold: 20,
          measureValue: 20,
          measureUnit: 20,
          legendWarning: 16,
        },
      },
    },
  },
  showOptions: {
    isShowStationName: true,
    isThresholdApproaching: true,
    isThresholdExceeded: true,
  },
};

export const initContentHistory: HistoriesContent = {
  stationType: '',
  station: '',
  measure: [],
  timeRange: ETimeRange['7 days'],
  typeTimeRange: ETypeTimeRange['yesterday'],
  calc: 'average_day',
  layout: {
    view: GridView.general,
    general: {
      column: 8,
      row: 7,
    },
    custom: {
      desktop: {
        column: 8,
        row: 7,
      },
      mobile: { column: 8, row: 7 },
      tablet: { column: 8, row: 7 },
    },
  },
  config: {
    showOptions: {
      isShowStationName: true,
      isShowUnit: true,
      isShowColumnEvaluate: true,
      isShowLimitThreshold: true,
    },
    warningColor: {
      isThresholdExceeded: true,
      isThresholdApproaching: true,
    },
  },
  fontSize: {
    auto: true,
    config: {
      headerTable: 16,
      stationName: 16,
      limitThreshold: 20,
      measureValue: 20,
      measureUnit: 20,
      legendWarning: 16,
      measureCode: 20,
    },
  },
};

export const initContentVideo: VideoContent = {
  video: null,
};

export const initContentImage: ImageContent = {
  images: [],
  config: {
    column: 3,
    row: 3,
  },
  mode: ImageMode.contain,
};

export const initContentText: TextContent = {
  config: {
    background: '#000000',
    color: '#ffffff',
  },
  content: {
    vi: '',
    en: '',
  },
  fontSize: {
    auto: true,
    config: {
      content: 24,
    },
  },
};

export const initValueSlide: ISlide = {
  title: {
    vi: 'Trang số 1',
    en: 'Slide 1',
  },
  id: '',
  show: true,
  order: 0,
  header: initValueHeader,
  content: {
    show: true,
    type: null,
    metadata: initContentRealtime,
  },
  footer: initValueFooter,
};
