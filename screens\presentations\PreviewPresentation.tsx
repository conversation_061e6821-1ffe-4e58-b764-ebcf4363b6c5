import { ISlide } from '@/types/slide';
import { cn } from '@/utils/tailwind';
import { EmblaCarouselType } from 'embla-carousel';
import React, { useMemo } from 'react';
import { FormattedMessage } from 'react-intl';
import NotConfigure from './components/NotConfigure';
import Wrapper from './contentType/Wrapper';
import WrapperSessionPresent from './sessions/WrapperSessionPresent';
import { prepareDataSlide } from '@/utils/slide';

type PropsPreviewPresentation = {
  dataSlide: ISlide;
  setEmblaApi: (emblaApi: EmblaCarouselType) => void;
  isIframeReady: boolean;
  isTargeted: boolean;
} & React.HTMLAttributes<HTMLDivElement>;

export default function PreviewPresentation({
  dataSlide,
  setEmblaApi,
  isIframeReady,
  isTargeted,
  className,
  ...props
}: PropsPreviewPresentation) {
  const slide = useMemo(() => dataSlide, [dataSlide]);
  const slideConverted = useMemo(() => prepareDataSlide(dataSlide), [dataSlide]);

  return (
    <div
      {...props}
      className={cn('relative flex flex-col justify-center items-center h-full w-full select-none', className)}
    >
      {!slide.show && <div className="absolute h-full w-full bg-white opacity-50 z-20"></div>}
      <WrapperSessionPresent slide={slideConverted}>
        {slide.content.type ? (
          <Wrapper
            dataSlide={slideConverted}
            isIframeReady={isIframeReady}
            setEmblaApi={setEmblaApi}
            isTargeted={isTargeted}
          />
        ) : (
          <NotConfigure
            description={
              <FormattedMessage
                defaultMessage="Vui lòng cấu hình nội dung sẽ hiển thị trên màn hình."
                id="screens.presentations.ContentSlider.1746006750"
              />
            }
          />
        )}
      </WrapperSessionPresent>
    </div>
  );
}
