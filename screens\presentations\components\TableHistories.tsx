import { useLedConfigStores, useLedStores } from '@/stores';
import { IDataStation } from '@/types';
import { EStatus, HistoriesContent, HistoryMetadata } from '@/types/slide';
import { hexToRgba } from '@/utils';
import { HistoryPage } from '@/utils/slide';
import { cn } from '@/utils/tailwind';
import { CheckIcon } from '@phosphor-icons/react';
import { useCallback, useMemo } from 'react';
import { useIntl } from 'react-intl';
import { ResponsiveTextWithOverride } from './ResponsiveTextWithOverride';
import { useCreateClient } from '@/api/useCreateClient';

enum Tag {
  desktop = 'desktop',
  tablet = 'tablet',
  mobile = 'mobile',
}
type PropsTableHistory = {
  dataSlide: {
    content: {
      metadata: HistoryMetadata;
    };
  };
  device: Tag;
  dataStations?: IDataStation;
  currentPage: HistoryPage;
};

export default function TableHistories({ dataSlide, currentPage }: PropsTableHistory) {
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const dataLedDetail = useLedStores((store) => store.dataLedDetail);
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';
  if (!dataConfig) return null;
  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );
  const decimalFormat = dataLedDetail?.decimal ?? 2;
  const { metadata } = dataSlide.content;

  const getBackgroundColor = useCallback(
    (
      dataRow: {
        values: (
          | string
          | {
              measure: string;
              status: EStatus;
              value: number | string;
            }
        )[];
        isLimit?: boolean;
        hasStandard?: boolean;
      },
      dataValue:
        | {
            measure: string;
            status: EStatus;
            value: number | string;
          }
        | string,
    ) => {
      if (dataRow?.isLimit) return hexToRgba(defaultConfig.slideConfig.color, 0.4);

      // Regular cells with no threshold data are transparent
      if (typeof dataValue === 'string') return 'transparent';

      if (dataValue.status === EStatus.Exceeded && metadata?.config?.warningColor?.isThresholdExceeded) {
        return defaultConfig.init.colors.color_over_limit;
      }
      if (dataValue.status === EStatus.ExceededPreparing && metadata?.config?.warningColor?.isThresholdApproaching) {
        return defaultConfig.init.colors.color_near_limit;
      }

      return 'transparent';
    },
    [defaultConfig, metadata?.config?.warningColor],
  );

  return (
    <table className="h-full w-full">
      <thead>
        <tr>
          {currentPage?.header.map((head, index) => {
            if (typeof head.value === 'string') {
              return (
                <th
                  key={head.value + index}
                  align={head.type === 'standard' || head.type === 'time' ? 'center' : 'right'}
                  className={cn('p-[4px]', 'md:px-2')}
                >
                  <ResponsiveTextWithOverride
                    className="text-current font-bold truncate w-full text-[100%]"
                    value={metadata?.fontSize?.config?.headerTable}
                  >
                    {head.value}
                  </ResponsiveTextWithOverride>
                </th>
              );
            }
            return (
              <th
                key={head.value.unit + index}
                align={head.type === 'standard' || head.type === 'time' ? 'center' : 'right'}
                className={cn('p-[4px] font-bold md:px-2')}
                style={{
                  maxWidth: `calc(100vw / ${currentPage?.header.length})`,
                }}
              >
                <ResponsiveTextWithOverride
                  as="p"
                  className="flex-shrink-0 text-current truncate w-full text-[100%] font-bold leading-tight"
                  value={metadata?.fontSize?.config?.headerTable}
                >
                  {head.value.name[locale]}
                </ResponsiveTextWithOverride>
                {head.value && head.value.unit && metadata.config.showOptions.isShowUnit ? (
                  <p className="flex-shrink-0 text-current text-[100%] h-fit leading-tight w-fit max-w-full flex flex-row">
                    <ResponsiveTextWithOverride
                      className="flex-shrink-0 text-[100%] font-bold leading-tight"
                      value={metadata?.fontSize?.config?.measureUnit}
                    >
                      (
                    </ResponsiveTextWithOverride>
                    <ResponsiveTextWithOverride
                      className="flex-1 min-w-0 truncate text-[100%] font-bold leading-tight"
                      value={metadata?.fontSize?.config?.measureUnit}
                    >
                      {head.value.unit}
                    </ResponsiveTextWithOverride>
                    <ResponsiveTextWithOverride
                      className="flex-shrink-0 text-[100%] font-bold leading-tight"
                      value={metadata?.fontSize?.config?.measureUnit}
                    >
                      )
                    </ResponsiveTextWithOverride>
                  </p>
                ) : null}
              </th>
            );
          })}
        </tr>
      </thead>
      <tbody>
        {currentPage?.body.map((row, rowIndex) => {
          return (
            <tr key={rowIndex}>
              {row.values.map((dataBody, colIndex) => {
                const type = currentPage?.header[colIndex].type;
                if (type === 'standard' && row.hasStandard) {
                  return (
                    <ResponsiveTextWithOverride
                      key={colIndex}
                      as="td"
                      align="center"
                      className="px-[4px] visible font-medium md:px-2"
                      value={metadata?.fontSize?.config?.measureValue}
                      style={{
                        backgroundColor: 'transparent',
                      }}
                    >
                      <CheckIcon weight="bold" className="text-current h-[1.2em] w-fit" />
                    </ResponsiveTextWithOverride>
                  );
                }
                if (typeof dataBody === 'string') {
                  return (
                    <ResponsiveTextWithOverride
                      key={colIndex}
                      as="td"
                      align={type === 'time' ? 'center' : 'right'}
                      className={cn('px-[4px] visible font-medium md:px-2', {
                        'font-bold truncate': row?.isLimit,
                      })}
                      value={
                        row?.isLimit
                          ? metadata.fontSize.config.limitThreshold
                          : metadata?.fontSize?.config?.measureValue
                      }
                      style={{
                        backgroundColor: getBackgroundColor(row, dataBody),
                        ...(row?.isLimit && { maxWidth: `calc(100vw / ${currentPage?.header.length})` }),
                      }}
                    >
                      {dataBody}
                    </ResponsiveTextWithOverride>
                  );
                }

                return (
                  <ResponsiveTextWithOverride
                    key={colIndex}
                    as="td"
                    align={type === 'time' ? 'center' : 'right'}
                    className={cn('px-[4px] visible font-medium md:px-2 truncate', {
                      'font-bold truncate': row?.isLimit,
                    })}
                    value={
                      row?.isLimit ? metadata.fontSize.config.limitThreshold : metadata?.fontSize?.config?.measureValue
                    }
                    style={{
                      backgroundColor: getBackgroundColor(row, dataBody),
                      ...(row?.isLimit && { maxWidth: `calc(100vw / ${currentPage?.header.length})` }),
                    }}
                  >
                    {new Intl.NumberFormat('en-US', {
                      minimumFractionDigits: decimalFormat,
                      maximumFractionDigits: decimalFormat,
                    }).format(+dataBody.value)}
                  </ResponsiveTextWithOverride>
                );
              })}
            </tr>
          );
        })}
      </tbody>
    </table>
  );
}
