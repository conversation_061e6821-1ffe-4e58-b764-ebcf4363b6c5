import { useLedConfigStores, useSlideConfigStores } from '@/stores';
import { ICameraList } from '@/types';
import { hexToRgba } from '@/utils';
import { LinkBreakIcon, WebcamIcon } from '@phosphor-icons/react';
import Image from 'next/image';
import React, { PropsWithChildren } from 'react';
import { FormattedMessage } from 'react-intl';
import { ResponsiveTextWithOverride } from './ResponsiveTextWithOverride';
import { cn } from '@/utils/tailwind';
import { priorityText } from '../compoType/constants';
import FillRatioBox from '@/components/ui/FillRatioBox';

export default function BoxCamera({
  camera,
  fontSize,
  ...props
}: PropsWithChildren<{ camera?: ICameraList; fontSize: number } & React.HTMLAttributes<HTMLDivElement>>) {
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const config = useSlideConfigStores((store) => store.config);

  if (!dataConfig) return null;

  const defaultConfig = {
    init: dataConfig?.init,
    slideConfig: dataConfig.slide_config_default,
  };

  return (
    <div {...props}>
      <div
        className="flex flex-col gap-1 items-center justify-center h-full w-full aspect-video text-[clamp(10px,_0.94vw_+_4px,_22px)] overflow-hidden"
        style={{
          color: hexToRgba(defaultConfig.slideConfig.color, 0.8),
        }}
      >
        {camera && camera.thumbnailUrl ? (
          <div className="relative h-full w-full flex items-center justify-center">
            <FillRatioBox ratio={13 / 9}>
              {camera.status === 'online' ? (
                <div className="relative h-full w-full max-w-full aspect-video">
                  <Image
                    src={camera.thumbnailUrl}
                    alt={'camera-thumbnail' + camera.cameraId}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    className="select-none"
                    loading="lazy"
                    priority={false}
                  />
                  <div
                    className="absolute z-10 top-0 left-0 w-full p-2"
                    style={{
                      background: 'linear-gradient(rgba(0, 0, 0, 0.6) -9.43%, rgba(0, 0, 0, 0) 100%)',
                    }}
                  >
                    <ResponsiveTextWithOverride
                      className="font-normal block truncate"
                      style={{
                        color: defaultConfig.slideConfig?.color,
                      }}
                      value={fontSize}
                    >
                      {camera.name}
                    </ResponsiveTextWithOverride>
                  </div>
                </div>
              ) : (
                <ResponsiveTextWithOverride
                  value={fontSize}
                  as="div"
                  className="w-full h-full flex flex-col items-center justify-center"
                  style={{
                    backgroundColor: '#FFFFFF36',
                  }}
                >
                  <LinkBreakIcon
                    weight="regular"
                    className="h-[2em] w-[2em] text-[100%] flex-shrink-0"
                    style={{
                      color: hexToRgba(defaultConfig.slideConfig?.color, 0.4),
                    }}
                  />
                  <ResponsiveTextWithOverride
                    className="absolute top-0 left-0 w-full p-2 font-normal block truncate text-[100%]"
                    value={fontSize}
                    style={{
                      color: defaultConfig.slideConfig?.color,
                    }}
                  >
                    {camera.name}
                  </ResponsiveTextWithOverride>
                </ResponsiveTextWithOverride>
              )}
            </FillRatioBox>
          </div>
        ) : (
          <ResponsiveTextWithOverride
            options={config}
            priority={priorityText.subDescription as any}
            className={cn(
              'w-full h-full flex flex-col items-center justify-center',
              'px-1 py-[2px]',
              'sm:py-1 sm:px-2',
              'md:py-2 md:px-3',
              'lg:py-2 lg:px-[10px]',
              'xl:py-[10px] xl:px-[14px]',
              '2xl:py-[14px] 2xl:px-4',
              '3xl:py-4 3xl:px-5',
            )}
            style={{
              color: hexToRgba(defaultConfig.slideConfig?.color, 0.4),
            }}
          >
            <WebcamIcon className="h-[2em] w-auto flex-shrink-0 text-current" />
            <span className="font-medium text-current text-center text-[100%]">
              <FormattedMessage
                defaultMessage="Vui lòng chọn camera hiển thị"
                id="screens.presentations.components.BoxCamera.2036303983"
              />
            </span>
          </ResponsiveTextWithOverride>
        )}
      </div>
    </div>
  );
}
