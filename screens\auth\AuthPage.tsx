'use client';

import { useCreateClient } from '@/api/useCreateClient';
import DropdownLanguage from '@/components/ui/DropdownLanguage';
import { routePath } from '@/constants';
import { useLedConfigStores, useLedStores } from '@/stores';
import { cn } from '@/utils/tailwind';
import { EyeClosedIcon, EyeIcon, LockIcon } from '@phosphor-icons/react';
import { AxiosError } from 'axios';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { defineMessages, FormattedMessage, useIntl } from 'react-intl';
import { Button, Form, FormItem, Input, toast } from 'ui-components';

const messagesAuthPage = defineMessages({
  messageRequiredPassword: {
    defaultMessage: 'Mật khẩu bảo mật không được để trống.',
    id: 'screens.auth.AuthPage.1987375752',
  },
  messageInvalidPassword: {
    defaultMessage: 'Mật khẩu bảo mật không chính xác. Vui lòng nhập lại.',
    id: 'screens.auth.AuthPage.1639349273',
  },
});

export default function AuthPageContainer() {
  const intl = useIntl();
  const [form] = Form.useForm();
  const params = useParams();
  const router = useRouter();
  const { led_key } = params as { led_key: string };
  const { ledConfig } = useCreateClient();
  const { isInitConfigured, setIsLogined, redirectTo, setRedirectTo } = useLedStores((store) => store);
  const { setIsLoginedConfig } = useLedConfigStores((store) => store);

  const [showPass, setShowPass] = useState({
    isShowPass: false,
    isShowConfirmPass: false,
  });

  const { mutateAsync: mutateAsyncSignIn, isPending } = ledConfig.loginLedBoard();

  useEffect(() => {
    const error = form.getFieldError('password');
    const isError = error.length > 0;
    if (isError) {
      const hasPassword: string = form.getFieldValue('password') ?? '';
      form.setFields([
        {
          name: 'password',
          errors: [
            hasPassword.length > 0
              ? intl.formatMessage(messagesAuthPage.messageInvalidPassword)
              : intl.formatMessage(messagesAuthPage.messageRequiredPassword),
          ],
        },
      ]);
    }
  }, [intl.locale, form]);

  const onFinish = async (values: { password: string }) => {
    try {
      const res = await mutateAsyncSignIn([led_key, values.password]);
      const { login } = res.data;
      setIsLogined(login);

      if (!isInitConfigured) {
        setIsLoginedConfig(login);
        return router.push(routePath.ledConfig(led_key));
      }
      if (redirectTo.length > 0) {
        setRedirectTo('');
        setIsLoginedConfig(true);
        return router.push(redirectTo);
      }
      return router.push(routePath.ledDetail(led_key));
    } catch (error:
      | any
      | {
          code: string;
          message: string;
          status: number;
        }) {
      if (error.code === 'credential_not_valid') {
        return form.setFields([
          {
            name: 'password',
            errors: [intl.formatMessage(messagesAuthPage.messageInvalidPassword)],
          },
        ]);
      }
      return toast({
        title: 'Có lỗi xảy ra khi đăng nhập',
        type: 'error',
        options: {
          position: 'top-center',
        },
      });
    }
  };

  return (
    <div className="h-full w-full flex items-center justify-center bg-white p-2">
      <div className="flex-1 h-full w-full bg-gray-100 border border-gray-200 rounded-[20px] flex items-center justify-center relative">
        <div className="absolute top-4 right-4">
          <DropdownLanguage />
        </div>
        <div className="flex flex-col gap-5 p-5 rounded-[32px] items-center h-auto w-[380px] shadow-default bg-white">
          <Image
            src="/images/login-card.jpg"
            alt="card-auth"
            width={340}
            height={225}
            priority
            quality={100}
            loading="eager"
            className="flex-shrink-0 object-cover rounded-xl relative max-h-[225px] max-w-[340px]"
          />
          <div className="flex flex-col items-center">
            <span className="font-bold text-2xl text-center text-gray-700">
              <FormattedMessage
                defaultMessage="Chào mừng bạn đến với"
                id="components.container.sharing.form-auth.1190180778"
              />
            </span>
            <span className="font-bold text-2xl text-center text-primary-500">iLotusLand for Public</span>
          </div>
          <Form
            initialValues={{
              password: '',
            }}
            form={form}
            name="auth-form"
            className="w-full"
            onFinish={onFinish}
          >
            <div className="flex flex-col gap-5">
              <div className="flex flex-col gap-2">
                <span className="font-medium text-sm text-center text-gray-700">
                  <FormattedMessage
                    defaultMessage="Vui lòng nhập mật khẩu bảo mật để truy cập."
                    id="screens.auth.AuthPage.1800833175"
                  />
                </span>
                <FormItem
                  name="password"
                  rules={[
                    {
                      required: true,
                      message: intl.formatMessage(messagesAuthPage.messageRequiredPassword),
                    },
                  ]}
                  renderItem={({ control, isError }) => {
                    return (
                      <Input
                        {...control}
                        type={showPass.isShowPass ? 'text' : 'password'}
                        placeholder={intl.formatMessage({
                          defaultMessage: 'Nhập mật khẩu bảo mật',
                          id: 'screens.auth.AuthPage.2112159354',
                        })}
                        value={control.value ?? ''}
                        onSubmitValue={control.onChange}
                        prefix={<LockIcon size={16} />}
                        variant={isError ? 'error' : 'default'}
                        autoComplete="current-password"
                        suffix={
                          control.value &&
                          (showPass.isShowPass ? (
                            <EyeIcon
                              size={16}
                              onClick={() =>
                                setShowPass((prevState) => ({
                                  ...prevState,
                                  isShowPass: !prevState.isShowPass,
                                }))
                              }
                            />
                          ) : (
                            <EyeClosedIcon
                              size={16}
                              onClick={() =>
                                setShowPass((prevState) => ({
                                  ...prevState,
                                  isShowPass: !prevState.isShowPass,
                                }))
                              }
                            />
                          ))
                        }
                      />
                    );
                  }}
                />
              </div>
              <Button type="submit" disabled={isPending} shape="circle" className={cn({ 'opacity-50': isPending })}>
                <FormattedMessage defaultMessage="Truy cập" id="screens.auth.AuthPage.1926033320" />
              </Button>
            </div>
          </Form>
          <span className="font-normal text-xs text-gray-500">© 2014 iLotusLand, Inc. All rights reserved.</span>
        </div>
      </div>
    </div>
  );
}
