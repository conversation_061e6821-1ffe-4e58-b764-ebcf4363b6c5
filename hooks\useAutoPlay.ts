import { useCallback, useEffect, useRef, useState } from 'react';

type UseAutoPlayType = {
  isPlaying: boolean;
  play: () => void;
  pause: () => void;
  toggle: () => void;
  reset: () => void;
  hasError: boolean;
  lastError: Error | null;
  retryCount: number;
  clearError: () => void;
};

export const useAutoPlay = (
  onAdvance: () => boolean | void, // Modified to return success status
  interval: number = 3000,
  autoStart: boolean = false,
  maxRetries: number = 3
): UseAutoPlayType => {
  const [isPlaying, setIsPlaying] = useState(autoStart);
  const [hasError, setHasError] = useState(false);
  const [lastError, setLastError] = useState<Error | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const onAdvanceRef = useRef(onAdvance);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update the ref when onAdvance changes to avoid stale closures
  useEffect(() => {
    onAdvanceRef.current = onAdvance;
  }, [onAdvance]);

  const clearAutoPlayInterval = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  const clearRetryTimeout = useCallback(() => {
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);

  const handleAdvanceError = useCallback((error: Error) => {
    console.error('Auto-play advance error:', error);
    setHasError(true);
    setLastError(error);
    setRetryCount(prev => prev + 1);
    
    // Stop auto-play on error
    setIsPlaying(false);
    clearAutoPlayInterval();
    
    // Attempt recovery if within retry limit
    if (retryCount < maxRetries) {
      console.log(`Attempting auto-play recovery (${retryCount + 1}/${maxRetries})`);
      
      retryTimeoutRef.current = setTimeout(() => {
        try {
          // Try to advance again
          const result = onAdvanceRef.current();
          const success = result === undefined ? true : result; // Backward compatibility
          
          if (success) {
            // Recovery successful, clear error state and resume
            setHasError(false);
            setLastError(null);
            setRetryCount(0);
            setIsPlaying(true);
            console.log('Auto-play recovery successful');
          } else {
            // Recovery failed, try again
            handleAdvanceError(new Error('Auto-play recovery failed'));
          }
        } catch (recoveryError) {
          handleAdvanceError(recoveryError instanceof Error ? recoveryError : new Error('Auto-play recovery failed'));
        }
      }, 1000); // 1 second retry delay
    } else {
      console.error('Auto-play recovery failed after maximum retries');
    }
  }, [retryCount, maxRetries, clearAutoPlayInterval]);

  const startAutoPlay = useCallback(() => {
    clearAutoPlayInterval();
    clearRetryTimeout();
    
    intervalRef.current = setInterval(() => {
      try {
        const result = onAdvanceRef.current();
        const success = result === undefined ? true : result; // Backward compatibility
        
        if (success === false) {
          // Advance failed, handle error
          handleAdvanceError(new Error('Auto-play advance returned false'));
        }
      } catch (error) {
        // Advance threw an error, handle it
        handleAdvanceError(error instanceof Error ? error : new Error('Auto-play advance threw an error'));
      }
    }, interval);
  }, [interval, clearAutoPlayInterval, clearRetryTimeout, handleAdvanceError]);

  const play = useCallback(() => {
    // Clear any existing errors when manually starting
    if (hasError) {
      setHasError(false);
      setLastError(null);
      setRetryCount(0);
    }
    setIsPlaying(true);
  }, [hasError]);

  const pause = useCallback(() => {
    setIsPlaying(false);
    clearRetryTimeout();
  }, [clearRetryTimeout]);

  const toggle = useCallback(() => {
    if (isPlaying) {
      pause();
    } else {
      play();
    }
  }, [isPlaying, pause, play]);

  const reset = useCallback(() => {
    setIsPlaying(false);
    setHasError(false);
    setLastError(null);
    setRetryCount(0);
    clearAutoPlayInterval();
    clearRetryTimeout();
  }, [clearAutoPlayInterval, clearRetryTimeout]);

  const clearError = useCallback(() => {
    setHasError(false);
    setLastError(null);
    setRetryCount(0);
    clearRetryTimeout();
  }, [clearRetryTimeout]);

  // Handle play/pause state changes
  useEffect(() => {
    if (isPlaying && !hasError) {
      startAutoPlay();
    } else {
      clearAutoPlayInterval();
    }
  }, [isPlaying, hasError, startAutoPlay, clearAutoPlayInterval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearAutoPlayInterval();
      clearRetryTimeout();
    };
  }, [clearAutoPlayInterval, clearRetryTimeout]);

  return {
    isPlaying,
    play,
    pause,
    toggle,
    reset,
    hasError,
    lastError,
    retryCount,
    clearError,
  };
};