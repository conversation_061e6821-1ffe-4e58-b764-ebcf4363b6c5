import GroupBox from '@/components/sessions/GroupBox';
import { NumberInput } from '@/components/ui/NumberInput';
import { ContentLayout } from '@/constants';
import { Fragment, useEffect, useMemo } from 'react';
import { FormattedMessage } from 'react-intl';
import { Form, FormInstance, Switch } from 'ui-components';
import { RealtimeContent } from '@/types/slide';

type PropsOptions = {
  focusedIndex: number;
  form: FormInstance;
  dataContent: {
    metadata: RealtimeContent;
  };
  initFont: {
    grid: {
      [x: string]: number;
    };
    table: {
      [x: string]: number;
    };
  };
};

export default function RealtimeOptions({ focusedIndex, form, dataContent, initFont }: PropsOptions) {
  const fontSizeData = useMemo(() => {
    return {
      grid: dataContent?.metadata?.layout?.grid?.fontSize.config,
      table: dataContent?.metadata?.layout?.table?.fontSize.config,
    };
  }, [dataContent]);

  const currentTemplate = useMemo(() => dataContent?.metadata?.layout.template, [dataContent]);

  const isAutoGrid = useMemo(() => dataContent?.metadata?.layout?.grid?.fontSize.auto, [dataContent]);
  const isAutoTable = useMemo(() => dataContent?.metadata?.layout?.table?.fontSize.auto, [dataContent]);

  useEffect(() => {
    if (currentTemplate === ContentLayout.grid && isAutoGrid) {
      return form.setFieldValue(
        ['presentations', focusedIndex, 'content', 'metadata', 'layout', 'grid', 'fontSize', 'config'],
        initFont.grid,
      );
    }
    if (currentTemplate === ContentLayout.table && isAutoTable) {
      return form.setFieldValue(
        ['presentations', focusedIndex, 'content', 'metadata', 'layout', 'table', 'fontSize', 'config'],
        initFont.table,
      );
    }
  }, [currentTemplate, isAutoGrid, isAutoTable, initFont]);

  return (
    <Fragment>
      <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
        <span className="font-semibold text-base text-gray-700">
          <FormattedMessage defaultMessage="Cấu hình hiển thị" id="screens.presentations.ConfigSlide.206334904" />
        </span>
        <div className="flex flex-col gap-2">
          <span className="font-medium text-sm text-gray-700">
            <FormattedMessage
              defaultMessage="Hiển thị thành phần"
              id="screens.presentations.ContentConfigSlide.11543690"
            />
          </span>
          <div className="h-full w-full">
            <GroupBox>
              <div className="flex flex-row gap-3 p-3 items-center">
                <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Tên trạm quan trắc"
                    id="screens.presentations.ContentConfigSlide.1036745043"
                  />
                </span>
                <Form.Field
                  name={['presentations', focusedIndex, 'content', 'metadata', 'showOptions', 'isShowStationName']}
                  trigger="onCheckedChange"
                  valuePropName="checked"
                >
                  <Switch className="flex-shrink-0" />
                </Form.Field>
              </div>
            </GroupBox>
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <span className="font-medium text-sm text-gray-700">
            <FormattedMessage
              defaultMessage="Hiển thị màu cảnh báo giá trị"
              id="screens.presentations.ContentConfigSlide.1298622127"
            />
          </span>
          <div className="h-full w-full">
            <GroupBox>
              <div className="flex flex-row gap-3 p-3 items-center">
                <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Cảnh báo chuẩn bị vượt"
                    id="screens.presentations.ContentConfigSlide.355114431"
                  />
                </span>
                <Form.Field
                  name={['presentations', focusedIndex, 'content', 'metadata', 'showOptions', 'isThresholdApproaching']}
                  trigger="onCheckedChange"
                  valuePropName="checked"
                >
                  <Switch className="flex-shrink-0" />
                </Form.Field>
              </div>
              <div className="flex flex-row gap-3 p-3 items-center">
                <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Cảnh báo vượt ngưỡng"
                    id="screens.presentations.ContentConfigSlide.261939202"
                  />
                </span>
                <Form.Field
                  name={['presentations', focusedIndex, 'content', 'metadata', 'showOptions', 'isThresholdExceeded']}
                  trigger="onCheckedChange"
                  valuePropName="checked"
                >
                  <Switch className="flex-shrink-0" />
                </Form.Field>
              </div>
            </GroupBox>
          </div>
        </div>
      </div>

      <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'layout', 'template']} shouldUpdate>
        {(control) => {
          const template = control.value ?? ContentLayout.grid;
          if (template === ContentLayout.grid) {
            return (
              <Form.Field
                name={['presentations', focusedIndex, 'content', 'metadata', 'layout', 'grid', 'fontSize', 'auto']}
              >
                {(control) => {
                  return (
                    <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
                      <span className="font-semibold text-base text-gray-700">
                        <FormattedMessage
                          defaultMessage="Kích thước kiểu chữ"
                          id="screens.presentations.ConfigSlide.1435964239"
                        />
                      </span>
                      <div className="flex flex-col gap-2">
                        <span className="font-medium text-sm text-gray-700">
                          <FormattedMessage
                            defaultMessage="Cấu hình kích thước"
                            id="screens.presentations.ContentConfigSlide.444330240"
                          />
                        </span>
                        <div className="h-full w-full">
                          <GroupBox>
                            <div className="flex flex-row gap-3 p-3 items-center">
                              <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                                <FormattedMessage
                                  defaultMessage="Tự động chọn kích thước phù hợp"
                                  id="screens.presentations.ContentConfigSlide.320775361"
                                />
                              </span>
                              <Form.Field
                                name={[
                                  'presentations',
                                  focusedIndex,
                                  'content',
                                  'metadata',
                                  'layout',
                                  'grid',
                                  'fontSize',
                                  'auto',
                                ]}
                                trigger="onCheckedChange"
                                valuePropName="checked"
                              >
                                <Switch className="flex-shrink-0" />
                              </Form.Field>
                            </div>
                          </GroupBox>
                        </div>
                        {control.value && (
                          <span className="font-normal text-xs text-gray-700">
                            <FormattedMessage
                              defaultMessage="Hệ thống sẽ tự động điều chỉnh kích thước nội dung để phù hợp với kích thước màn hình."
                              id="screens.presentations.ContentConfigSlide.291032092"
                            />
                          </span>
                        )}
                      </div>
                      {!control.value && (
                        <div className="flex flex-col gap-2">
                          <span className="font-medium text-sm text-gray-700">
                            <FormattedMessage
                              defaultMessage="Tùy biến riêng"
                              id="screens.presentations.ConfigSlide.1975998546"
                            />
                          </span>
                          <div className="h-full w-full">
                            <GroupBox>
                              <div className="flex flex-row items-center bg-gray-50">
                                <span className="flex-1 min-w-0 p-3 font-semibold text-xs text-gray-700 uppercase">
                                  <FormattedMessage
                                    defaultMessage="Nội dung"
                                    id="screens.presentations.HeaderConfigSlide.768903020"
                                  />
                                </span>
                                <span className="w-[110px] flex-shrink-0 p-3 font-semibold text-xs text-gray-700 uppercase text-center">
                                  <FormattedMessage
                                    defaultMessage="Kích thước"
                                    id="screens.presentations.HeaderConfigSlide.1145935829"
                                  />
                                </span>
                              </div>
                              <div className="flex flex-row items-center">
                                <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                                  <FormattedMessage
                                    defaultMessage="Tên trạm quan trắc"
                                    id="screens.presentations.ContentConfigSlide.1036745043"
                                  />
                                </span>
                                <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                                  <Form.Field
                                    name={[
                                      'presentations',
                                      focusedIndex,
                                      'content',
                                      'metadata',
                                      'layout',
                                      'grid',
                                      'fontSize',
                                      'config',
                                      'stationName',
                                    ]}
                                    trigger="onBlurInput"
                                  >
                                    <NumberInput
                                      precision={0}
                                      placeholder=""
                                      suffix="px"
                                      classNameWrapper="h-[33px]"
                                      defaultValue={fontSizeData.grid.stationName}
                                      clearable={false}
                                      controls={false}
                                      keyboard
                                    />
                                  </Form.Field>
                                </div>
                              </div>
                              <div className="flex flex-row items-center">
                                <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                                  <FormattedMessage
                                    defaultMessage="Tên thông số"
                                    id="screens.presentations.ContentConfigSlide.1436455565"
                                  />
                                </span>
                                <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                                  <Form.Field
                                    name={[
                                      'presentations',
                                      focusedIndex,
                                      'content',
                                      'metadata',
                                      'layout',
                                      'grid',
                                      'fontSize',
                                      'config',
                                      'measureCode',
                                    ]}
                                    trigger="onBlurInput"
                                  >
                                    <NumberInput
                                      precision={0}
                                      placeholder=""
                                      suffix="px"
                                      classNameWrapper="h-[33px]"
                                      defaultValue={fontSizeData.grid.measureCode}
                                      clearable={false}
                                      controls={false}
                                      keyboard
                                    />
                                  </Form.Field>
                                </div>
                              </div>
                              <div className="flex flex-row items-center">
                                <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                                  <FormattedMessage
                                    defaultMessage="Ngưỡng giới hạn"
                                    id="screens.presentations.ContentConfigSlide.1242488578"
                                  />
                                </span>
                                <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                                  <Form.Field
                                    name={[
                                      'presentations',
                                      focusedIndex,
                                      'content',
                                      'metadata',
                                      'layout',
                                      'grid',
                                      'fontSize',
                                      'config',
                                      'limitThreshold',
                                    ]}
                                    trigger="onBlurInput"
                                  >
                                    <NumberInput
                                      precision={0}
                                      placeholder=""
                                      suffix="px"
                                      classNameWrapper="h-[33px]"
                                      defaultValue={fontSizeData.grid.limitThreshold}
                                      clearable={false}
                                      controls={false}
                                      keyboard
                                    />
                                  </Form.Field>
                                </div>
                              </div>
                              <div className="flex flex-row items-center">
                                <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                                  <FormattedMessage
                                    defaultMessage="Giá trị thông số"
                                    id="screens.presentations.ContentConfigSlide.1722724199"
                                  />
                                </span>
                                <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                                  <Form.Field
                                    name={[
                                      'presentations',
                                      focusedIndex,
                                      'content',
                                      'metadata',
                                      'layout',
                                      'grid',
                                      'fontSize',
                                      'config',
                                      'measureValue',
                                    ]}
                                    trigger="onBlurInput"
                                  >
                                    <NumberInput
                                      precision={0}
                                      placeholder=""
                                      suffix="px"
                                      classNameWrapper="h-[33px]"
                                      defaultValue={fontSizeData.grid.measureValue}
                                      clearable={false}
                                      controls={false}
                                      keyboard
                                    />
                                  </Form.Field>
                                </div>
                              </div>
                              <div className="flex flex-row items-center">
                                <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                                  <FormattedMessage
                                    defaultMessage="Đơn vị thông số"
                                    id="screens.presentations.ContentConfigSlide.2020945267"
                                  />
                                </span>
                                <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                                  <Form.Field
                                    name={[
                                      'presentations',
                                      focusedIndex,
                                      'content',
                                      'metadata',
                                      'layout',
                                      'grid',
                                      'fontSize',
                                      'config',
                                      'measureUnit',
                                    ]}
                                    trigger="onBlurInput"
                                  >
                                    <NumberInput
                                      precision={0}
                                      placeholder=""
                                      suffix="px"
                                      classNameWrapper="h-[33px]"
                                      defaultValue={fontSizeData.grid.measureUnit}
                                      clearable={false}
                                      controls={false}
                                      keyboard
                                    />
                                  </Form.Field>
                                </div>
                              </div>
                              <div className="flex flex-row items-center">
                                <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                                  <FormattedMessage
                                    defaultMessage="Chú thích màu cảnh báo"
                                    id="screens.presentations.ContentConfigSlide.1913531627"
                                  />
                                </span>
                                <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                                  <Form.Field
                                    name={[
                                      'presentations',
                                      focusedIndex,
                                      'content',
                                      'metadata',
                                      'layout',
                                      'grid',
                                      'fontSize',
                                      'config',
                                      'legendWarning',
                                    ]}
                                    trigger="onBlurInput"
                                  >
                                    <NumberInput
                                      precision={0}
                                      placeholder=""
                                      suffix="px"
                                      classNameWrapper="h-[33px]"
                                      defaultValue={fontSizeData.grid.legendWarning}
                                      clearable={false}
                                      controls={false}
                                      keyboard
                                    />
                                  </Form.Field>
                                </div>
                              </div>
                            </GroupBox>
                          </div>
                          <span className="font-normal text-xs text-gray-700">
                            <FormattedMessage
                              defaultMessage="Cho phép tùy chỉnh kích thước nội dung hiển thị trên màn hình. Hệ thống sẽ không tự động thay đổi kích thước."
                              id="screens.presentations.HeaderConfigSlide.595062375"
                            />
                          </span>
                        </div>
                      )}
                    </div>
                  );
                }}
              </Form.Field>
            );
          }

          return (
            <Form.Field
              name={['presentations', focusedIndex, 'content', 'metadata', 'layout', 'table', 'fontSize', 'auto']}
            >
              {(control) => {
                return (
                  <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
                    <span className="font-semibold text-base text-gray-700">
                      <FormattedMessage
                        defaultMessage="Kích thước kiểu chữ"
                        id="screens.presentations.ConfigSlide.1435964239"
                      />
                    </span>
                    <div className="flex flex-col gap-2">
                      <span className="font-medium text-sm text-gray-700">
                        <FormattedMessage
                          defaultMessage="Cấu hình kích thước"
                          id="screens.presentations.ContentConfigSlide.444330240"
                        />
                      </span>
                      <div className="h-full w-full">
                        <GroupBox>
                          <div className="flex flex-row gap-3 p-3 items-center">
                            <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                              <FormattedMessage
                                defaultMessage="Tự động chọn kích thước phù hợp"
                                id="screens.presentations.ContentConfigSlide.320775361"
                              />
                            </span>
                            <Form.Field
                              name={[
                                'presentations',
                                focusedIndex,
                                'content',
                                'metadata',
                                'layout',
                                'table',
                                'fontSize',
                                'auto',
                              ]}
                              trigger="onCheckedChange"
                              valuePropName="checked"
                            >
                              <Switch className="flex-shrink-0" />
                            </Form.Field>
                          </div>
                        </GroupBox>
                      </div>
                      {control.value && (
                        <span className="font-normal text-xs text-gray-700">
                          <FormattedMessage
                            defaultMessage="Hệ thống sẽ tự động điều chỉnh kích thước nội dung để phù hợp với kích thước màn hình."
                            id="screens.presentations.ContentConfigSlide.291032092"
                          />
                        </span>
                      )}
                    </div>
                    {!control.value && (
                      <div className="flex flex-col gap-2">
                        <span className="font-medium text-sm text-gray-700">
                          <FormattedMessage
                            defaultMessage="Tùy biến riêng"
                            id="screens.presentations.ConfigSlide.1975998546"
                          />
                        </span>
                        <div className="h-full w-full">
                          <GroupBox>
                            <div className="flex flex-row items-center bg-gray-50">
                              <span className="flex-1 min-w-0 p-3 font-semibold text-xs text-gray-700 uppercase">
                                <FormattedMessage
                                  defaultMessage="Nội dung"
                                  id="screens.presentations.HeaderConfigSlide.768903020"
                                />
                              </span>
                              <span className="w-[110px] flex-shrink-0 p-3 font-semibold text-xs text-gray-700 uppercase text-center">
                                <FormattedMessage
                                  defaultMessage="Kích thước"
                                  id="screens.presentations.HeaderConfigSlide.1145935829"
                                />
                              </span>
                            </div>
                            <div className="flex flex-row items-center">
                              <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                                <FormattedMessage
                                  defaultMessage="Tên cột"
                                  id="screens.presentations.ContentConfigSlide.251967402"
                                />
                              </span>
                              <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                                <Form.Field
                                  name={[
                                    'presentations',
                                    focusedIndex,
                                    'content',
                                    'metadata',
                                    'layout',
                                    'table',
                                    'fontSize',
                                    'config',
                                    'headerTable',
                                  ]}
                                  trigger="onBlurInput"
                                >
                                  <NumberInput
                                    precision={0}
                                    placeholder=""
                                    suffix="px"
                                    classNameWrapper="h-[33px]"
                                    defaultValue={fontSizeData.table.headerTable}
                                    clearable={false}
                                    controls={false}
                                    keyboard
                                  />
                                </Form.Field>
                              </div>
                            </div>
                            <div className="flex flex-row items-center">
                              <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                                <FormattedMessage
                                  defaultMessage="Tên trạm quan trắc"
                                  id="screens.presentations.ContentConfigSlide.1036745043"
                                />
                              </span>
                              <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                                <Form.Field
                                  name={[
                                    'presentations',
                                    focusedIndex,
                                    'content',
                                    'metadata',
                                    'layout',
                                    'table',
                                    'fontSize',
                                    'config',
                                    'stationName',
                                  ]}
                                  trigger="onBlurInput"
                                >
                                  <NumberInput
                                    precision={0}
                                    placeholder=""
                                    suffix="px"
                                    classNameWrapper="h-[33px]"
                                    defaultValue={fontSizeData.table.stationName}
                                    clearable={false}
                                    controls={false}
                                    keyboard
                                  />
                                </Form.Field>
                              </div>
                            </div>
                            <div className="flex flex-row items-center">
                              <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                                <FormattedMessage
                                  defaultMessage="Tên thông số"
                                  id="screens.presentations.ContentConfigSlide.1436455565"
                                />
                              </span>
                              <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                                <Form.Field
                                  name={[
                                    'presentations',
                                    focusedIndex,
                                    'content',
                                    'metadata',
                                    'layout',
                                    'table',
                                    'fontSize',
                                    'config',
                                    'measureCode',
                                  ]}
                                  trigger="onBlurInput"
                                >
                                  <NumberInput
                                    precision={0}
                                    placeholder=""
                                    suffix="px"
                                    classNameWrapper="h-[33px]"
                                    defaultValue={fontSizeData.table.measureCode}
                                    clearable={false}
                                    controls={false}
                                    keyboard
                                  />
                                </Form.Field>
                              </div>
                            </div>
                            <div className="flex flex-row items-center">
                              <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                                <FormattedMessage
                                  defaultMessage="Ngưỡng giới hạn"
                                  id="screens.presentations.ContentConfigSlide.1242488578"
                                />
                              </span>
                              <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                                <Form.Field
                                  name={[
                                    'presentations',
                                    focusedIndex,
                                    'content',
                                    'metadata',
                                    'layout',
                                    'table',
                                    'fontSize',
                                    'config',
                                    'limitThreshold',
                                  ]}
                                  trigger="onBlurInput"
                                >
                                  <NumberInput
                                    precision={0}
                                    placeholder=""
                                    suffix="px"
                                    classNameWrapper="h-[33px]"
                                    defaultValue={fontSizeData.table.limitThreshold}
                                    clearable={false}
                                    controls={false}
                                    keyboard
                                  />
                                </Form.Field>
                              </div>
                            </div>
                            <div className="flex flex-row items-center">
                              <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                                <FormattedMessage
                                  defaultMessage="Giá trị thông số"
                                  id="screens.presentations.ContentConfigSlide.1722724199"
                                />
                              </span>
                              <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                                <Form.Field
                                  name={[
                                    'presentations',
                                    focusedIndex,
                                    'content',
                                    'metadata',
                                    'layout',
                                    'table',
                                    'fontSize',
                                    'config',
                                    'measureValue',
                                  ]}
                                  trigger="onBlurInput"
                                >
                                  <NumberInput
                                    precision={0}
                                    placeholder=""
                                    suffix="px"
                                    classNameWrapper="h-[33px]"
                                    defaultValue={fontSizeData.table.measureValue}
                                    clearable={false}
                                    controls={false}
                                    keyboard
                                  />
                                </Form.Field>
                              </div>
                            </div>
                            <div className="flex flex-row items-center">
                              <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                                <FormattedMessage
                                  defaultMessage="Đơn vị thông số"
                                  id="screens.presentations.ContentConfigSlide.2020945267"
                                />
                              </span>
                              <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                                <Form.Field
                                  name={[
                                    'presentations',
                                    focusedIndex,
                                    'content',
                                    'metadata',
                                    'layout',
                                    'table',
                                    'fontSize',
                                    'config',
                                    'measureUnit',
                                  ]}
                                  trigger="onBlurInput"
                                >
                                  <NumberInput
                                    precision={0}
                                    placeholder=""
                                    suffix="px"
                                    classNameWrapper="h-[33px]"
                                    defaultValue={fontSizeData.table.measureUnit}
                                    clearable={false}
                                    controls={false}
                                    keyboard
                                  />
                                </Form.Field>
                              </div>
                            </div>
                            <div className="flex flex-row items-center">
                              <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                                <FormattedMessage
                                  defaultMessage="Chú thích màu cảnh báo"
                                  id="screens.presentations.ContentConfigSlide.1913531627"
                                />
                              </span>
                              <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                                <Form.Field
                                  name={[
                                    'presentations',
                                    focusedIndex,
                                    'content',
                                    'metadata',
                                    'layout',
                                    'table',
                                    'fontSize',
                                    'config',
                                    'legendWarning',
                                  ]}
                                  trigger="onBlurInput"
                                >
                                  <NumberInput
                                    precision={0}
                                    placeholder=""
                                    suffix="px"
                                    classNameWrapper="h-[33px]"
                                    defaultValue={fontSizeData.table.legendWarning}
                                    clearable={false}
                                    controls={false}
                                    keyboard
                                  />
                                </Form.Field>
                              </div>
                            </div>
                          </GroupBox>
                        </div>
                        <span className="font-normal text-xs text-gray-700">
                          <FormattedMessage
                            defaultMessage="Cho phép tùy chỉnh kích thước nội dung hiển thị trên màn hình. Hệ thống sẽ không tự động thay đổi kích thước."
                            id="screens.presentations.HeaderConfigSlide.595062375"
                          />
                        </span>
                      </div>
                    )}
                  </div>
                );
              }}
            </Form.Field>
          );
        }}
      </Form.Field>
    </Fragment>
  );
}
