import { defineMessages } from 'react-intl';

export const messageErrorCommon = defineMessages({
  fieldRequired: {
    defaultMessage: '{field} không được để trống.',
    id: 'constants.defineMessages.1790118527',
  },
  fieldMax: {
    defaultMessage: 'Không được nhập quá {max} ký tự.',
    id: 'constants.defineMessages.852565916',
  },
  // fieldMin: {
  //   defaultMessage: '{field} phải có ít nhất {min} ký tự.',
  //   id: 'constants.defineMessages.84628235',
  // },
  // fieldInvalid: {
  //   defaultMessage: '{field} không hợp lệ.',
  //   id: 'constants.defineMessages.424075352',
  // },
  // fieldNotFormat: {
  //   defaultMessage: '{field} không đúng định dạng.',
  //   id: 'constants.defineMessages.439996808',
  // },
  // fieldPassNotFormat: {
  //   defaultMessage: '{field} không trùng nhau.',
  //   id: 'constants.defineMessages.80dcc7bb',
  // },
});

export const messageContentType = defineMessages({
  realtime: {
    defaultMessage: 'Dữ liệu trực tuyến',
    id: 'screens.presentations.ContentConfigSlide.1480446057',
  },
  histories: {
    defaultMessage: 'Dữ liệu lịch sử',
    id: 'screens.presentations.ContentConfigSlide.43299210',
  },
  images: {
    defaultMessage: 'Hình ảnh',
    id: 'screens.presentations.ContentConfigSlide.1115456805',
  },
  text: {
    defaultMessage: 'Văn bản',
    id: 'screens.presentations.ContentConfigSlide.2056201202',
  },
});
