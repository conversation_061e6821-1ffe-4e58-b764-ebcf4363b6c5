import { cn } from '@/utils/tailwind';
import { PropsWithChildren } from 'react';
import {
  Button,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from 'ui-components';

type AlertModalProps = {
  title: string | React.ReactElement;
  description: string | React.ReactElement;
  cancel?: {
    title: any;
    onClick?: () => void;
    className?: string;
    variant?:
      | 'gray'
      | 'success'
      | 'primary'
      | 'secondary'
      | 'neutral'
      | 'warning'
      | 'danger'
      | null
      | undefined;
    [key: string]: any;
  };
  confirm: {
    title: any;
    onClick?: () => void;
    className?: string;
    variant?:
      | 'gray'
      | 'success'
      | 'primary'
      | 'secondary'
      | 'neutral'
      | 'warning'
      | 'danger'
      | null
      | undefined;
    type?: 'submit' | 'button';
    [key: string]: any;
    disable?: boolean;
  };
  image?: React.ReactNode;
};

const AlertModal = ({
  title,
  description,
  cancel,
  confirm,
  image,
  children,
}: AlertModalProps & PropsWithChildren) => {
  return (
    <>
      <DialogHeader className="p-5 space-y-0">
        <>{image}</>
        <DialogTitle className="text-center text-lg text-gray-800 font-semibold">
          {title}
        </DialogTitle>
        <DialogDescription className="text-center !mt-2 text-sm font-normal text-gray-700">
          {description}
        </DialogDescription>
        <>{children}</>
      </DialogHeader>
      <DialogFooter className="border-t border-gray-200 p-[14px] w-full flex flex-row gap-3">
        {cancel && (
          <Button
            onClick={cancel.onClick}
            className={cn(
              'flex-1 text-primary-500 text-sm font-normal',
              cancel.className,
            )}
            variant={cancel.variant ?? 'gray'}
            {...(({ title, ...rest }) => rest)(cancel)}
          >
            {cancel.title!}
          </Button>
        )}
        <Button
          className={cn('flex-1 text-sm font-normal', confirm.className)}
          variant={confirm.variant ?? 'primary'}
          type={confirm.type ?? 'button'}
          disabled={confirm.disable}
          {...(({ title, ...rest }) => rest)(confirm)}
        >
          {confirm.title!}
        </Button>
      </DialogFooter>
    </>
  );
};
export { AlertModal };
