'use client';

import { cn } from '@/utils/tailwind';
import { RefObject } from 'react';
import { FormattedMessage } from 'react-intl';

type Props = {
  currentTag: number;
  onChangeTag: (index: number) => void;
  ref?: RefObject<HTMLDivElement | null>;
};

export default function TagConfigSlide({
  currentTag,
  onChangeTag,
  ref,
  ...props
}: Props & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div {...props} ref={ref} className={cn('sticky top-0 z-10 w-full p-4 bg-gray-100', props.className)}>
      <div className="flex flex-row gap-3 p-2 rounded-[30px] bg-white w-full">
        {dataTag.map((tag, index: number) => (
          <div
            key={tag.value}
            className={cn(
              'px-3 py-2 rounded-[20px] flex-1 min-w-0 flex items-center justify-center hover:bg-gray-100 cursor-pointer text-gray-700 hover:text-gray-800 transition-all duration-200 ease-in-out',
              { 'text-white hover:text-white bg-primary-500 hover:bg-primary-500': currentTag === index },
            )}
            onClick={() => {
              if (index === currentTag) return;
              onChangeTag(index);
            }}
          >
            <span className="font-medium text-sm text-current text-center">{tag.title}</span>
          </div>
        ))}
      </div>
    </div>
  );
}

enum Tag {
  Header = 'header',
  Content = 'content',
  Footer = 'footer',
}

const dataTag = [
  {
    value: Tag.Header,
    title: <FormattedMessage defaultMessage="Đầu trang" id="screens.presentations.ConfigSlide.440955834" />,
  },
  {
    value: Tag.Content,
    title: <FormattedMessage defaultMessage="Nội dung" id="screens.presentations.ConfigSlide.768903020" />,
  },
  {
    value: Tag.Footer,
    title: <FormattedMessage defaultMessage="Chân trang" id="screens.presentations.ConfigSlide.672150323" />,
  },
];
