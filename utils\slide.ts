import { DataHistory } from '@/api/type';
import { ContentLayout, DATE_FORMAT, GridView } from '@/constants';
import {
  initContentHistory,
  initContentRealtime,
  initValueFooter,
  initValueHeader,
  initValueSlide,
} from '@/constants/slide';
import { IDataStation, IMeasuring } from '@/types';
import {
  EContentType,
  EStatus,
  ETimeRange,
  ETypeTimeRange,
  HistoriesContent,
  HistoryMetadata,
  IConfigLayoutData,
  IContentSlides,
  ImageContent,
  ImagesMetadata,
  ISlide,
  RealtimeContent,
  RealTimeMetadata,
  SlideConfigDto,
  TextContent,
  TextMetadata,
  VideoContent,
  VideoMetadata,
} from '@/types/slide';
import dayjs from 'dayjs';
import { IntlShape } from 'react-intl';
import { paginateArray } from '.';
import { DateMeasureData, evaluateDateAcrossPages, ThresholdOptions } from './evaluationUtils';
import { generateMockDataForHistory } from './mockDataGenerator';

export const prepareDataSlide = (slide: ISlide): SlideConfigDto => {
  const baseSlide = {
    id: slide.id,
    ledBoardId: slide.ledBoardId,
    title: slide.title,
    show: slide.show,
    header: {
      show: slide.header.show,
      title: slide.header.title,
      sub_title: slide.header.sub_title,
      showOptions: slide.header.showOptions,
      fontSize: slide.header.fontSize,
    },
    footer: {
      show: slide.footer.show,
      type: slide.footer.type,
      content: slide.footer.type === 'banner-text' ? slide.footer?.bannerText?.content : undefined,
      config: {
        background: typeof slide.footer.config?.background === 'string' ? slide.footer.config?.background : '#ffffff',
        color: typeof slide.footer.config?.color === 'string' ? slide.footer.config?.color : '#000000',
      },
      fontSize: slide.footer.fontSize || { auto: true, config: { content: 16 } },
    },
    content: {
      show: slide.content.show,
      type: slide.content.type,
      metadata: null as RealTimeMetadata | VideoMetadata | ImagesMetadata | HistoryMetadata | TextMetadata | null,
    },
    order: slide.order,
  };

  if (slide.content.type === EContentType.realtime && slide.content.metadata) {
    const metaRealtime = slide.content.metadata as RealtimeContent;
    baseSlide.content.metadata = {
      measure: metaRealtime?.measure,
      station: metaRealtime?.station,
      stationType: metaRealtime?.stationType,
      cameras: metaRealtime?.camera.isShow ? metaRealtime.camera.list || [] : undefined,
      showOptions: metaRealtime?.showOptions,
      layout: {
        template: (slide.content.metadata as RealtimeContent)?.layout.template,
        config: {
          view: (slide.content.metadata as RealtimeContent)?.layout[
            (slide.content.metadata as RealtimeContent).layout.template
          ].view,
          settings: (slide.content.metadata as RealtimeContent)?.layout[
            (slide.content.metadata as RealtimeContent).layout.template
          ][
            (slide.content.metadata as RealtimeContent).layout[
              (slide.content.metadata as RealtimeContent).layout.template
            ].view
          ],
          fontSize: (slide.content.metadata as RealtimeContent)?.layout[
            (slide.content.metadata as RealtimeContent).layout.template
          ].fontSize,
        },
      },
    };
  }

  if (slide.content.type === EContentType.histories && slide.content.metadata) {
    const metadata = slide.content.metadata as HistoriesContent;
    baseSlide.content.metadata = {
      measure: metadata?.measure,
      station: metadata?.station,
      stationType: metadata?.stationType,
      calc: metadata.calc,
      timeRange: metadata.timeRange,
      typeTimeRange: metadata.typeTimeRange,
      fontSize: metadata.fontSize,
      config: metadata.config,
      layout: {
        view: metadata?.layout.view,
        settings: metadata?.layout[metadata.layout.view],
      },
    } as HistoryMetadata;
  }

  if (slide.content.type === EContentType.video && slide.content.metadata) {
    baseSlide.content.metadata = {
      video: (slide.content.metadata as VideoContent)?.video,
    } as VideoMetadata;
  }

  if (slide.content.type === EContentType.images && slide.content.metadata) {
    const images = (slide.content.metadata as ImageContent)?.images.map((img, index) => {
      if (img) {
        return 'order' in img
          ? img
          : {
              order: index,
              url: img,
            };
      }
      return null;
    });
    baseSlide.content.metadata = {
      images: images,
      config: (slide.content.metadata as ImageContent)?.config,
      mode: (slide.content.metadata as ImageContent)?.mode,
    } as ImagesMetadata;
  }

  if (slide.content.type === EContentType.text && slide.content.metadata) {
    baseSlide.content.metadata = {
      config: (slide.content.metadata as TextContent)?.config,
      content: (slide.content.metadata as TextContent)?.content,
      fontSize: (slide.content.metadata as TextContent)?.fontSize,
    };
  }

  return baseSlide;
};

export const prepareData = (slideData: ISlide[]): SlideConfigDto[] => {
  const body = slideData.map((slide, index) => {
    const data = prepareDataSlide(slide);
    return {
      ...data,
      order: index,
    };
  });
  return body;
};

export const convertData = (slide: SlideConfigDto): ISlide => {
  return {
    id: slide.id ?? '',
    title: slide?.title || initValueSlide.title,
    show: slide?.show,
    order: slide?.order,
    header: {
      show: slide?.header?.show || initValueHeader.show,
      title: slide?.header?.title || initValueHeader.title,
      sub_title: slide?.header?.sub_title || initValueHeader.sub_title,
      showOptions: slide?.header?.showOptions || initValueHeader.showOptions,
      fontSize: slide?.header?.fontSize || initValueHeader.fontSize,
    },
    content: {
      show: slide?.content.show,
      type: slide?.content.type || initValueSlide.content.type,
      metadata: convertMetadata(slide?.content),
    },
    footer: {
      show: slide?.footer?.show || initValueFooter.show,
      type: slide?.footer?.type || initValueFooter.type,
      bannerText:
        slide?.footer?.type === 'banner-text' ? { content: slide?.footer.content } : initValueFooter.bannerText,
      timestamp: initValueFooter.timestamp,
      config: slide?.footer?.config || initValueFooter.config,
      fontSize: slide?.footer?.fontSize || initValueFooter.fontSize,
    },
  };
};

export const convertMetadata = (content: SlideConfigDto['content']): IContentSlides['metadata'] => {
  if (!content.type) {
    return undefined;
  }
  switch (content.type) {
    case EContentType.realtime: {
      const metadata = content?.metadata as RealTimeMetadata;
      const template = metadata?.layout?.template || ContentLayout.grid;
      const view = metadata?.layout?.config.view || GridView.general;
      const settings = metadata?.layout?.config?.settings as
        | IConfigLayoutData
        | {
            desktop: IConfigLayoutData;
            tablet: IConfigLayoutData;
            mobile: IConfigLayoutData;
          };

      const layout = {
        template,
        grid:
          template === ContentLayout.grid
            ? {
                view,
                fontSize: metadata?.layout?.config.fontSize || initContentRealtime?.layout.grid.fontSize,
                general:
                  view === GridView.general
                    ? {
                        column: (settings as IConfigLayoutData)?.column || 3,
                        row: (settings as IConfigLayoutData)?.row || 3,
                        cameraPerPage: (settings as IConfigLayoutData)?.cameraPerPage || 3,
                      }
                    : initContentRealtime?.layout.grid.general,
                custom:
                  view === GridView.custom
                    ? 'desktop' in settings
                      ? settings
                      : initContentRealtime?.layout.table.custom
                    : initContentRealtime?.layout.grid.custom,
              }
            : initContentRealtime?.layout.grid,
        table:
          template === ContentLayout.table
            ? {
                view,
                fontSize: metadata?.layout?.config.fontSize || initContentRealtime?.layout.table.fontSize,
                general:
                  view === GridView.general
                    ? {
                        column: (settings as IConfigLayoutData)?.column || 7,
                        row: (settings as IConfigLayoutData)?.row || 1,
                        cameraPerPage: (settings as IConfigLayoutData)?.cameraPerPage || 3,
                      }
                    : initContentRealtime?.layout.table.general,
                custom:
                  view === GridView.custom
                    ? 'desktop' in settings
                      ? settings
                      : initContentRealtime?.layout.table.custom
                    : initContentRealtime?.layout.table.custom,
              }
            : initContentRealtime?.layout.table,
      };
      return {
        stationType: metadata?.stationType,
        station: metadata?.station,
        measure: metadata?.measure,
        camera: {
          isShow: Boolean(metadata?.cameras?.length),
          list: metadata?.cameras || [],
        },
        showOptions: metadata?.showOptions,
        layout,
      } as RealtimeContent;
    }

    case EContentType.histories: {
      const metadata = content?.metadata as HistoryMetadata;
      const view = metadata.layout.view;
      const settings = metadata?.layout.settings;
      const layout = {
        view,
        general:
          view === GridView.general
            ? {
                column: (settings as IConfigLayoutData)?.column || 8,
                row: (settings as IConfigLayoutData)?.row || 7,
              }
            : initContentHistory?.layout.general,
        custom:
          view === GridView.custom
            ? 'desktop' in settings
              ? settings
              : initContentHistory?.layout.custom
            : initContentHistory?.layout.custom,
      };
      return {
        stationType: metadata?.stationType,
        station: metadata?.station,
        measure: metadata?.measure,
        calc: metadata.calc,
        config: metadata.config,
        timeRange: metadata.timeRange,
        typeTimeRange: metadata.typeTimeRange,
        fontSize: metadata.fontSize,
        layout: layout,
      } as HistoriesContent;
    }

    case EContentType.video: {
      const metadata = content?.metadata as VideoContent;
      return {
        video: metadata?.video,
      } as VideoContent;
    }

    case EContentType.images: {
      const metadata = content?.metadata as ImagesMetadata;
      return {
        images: metadata.images,
        config: metadata.config,
        mode: metadata.mode,
      };
    }

    case EContentType.text: {
      const metadata = content?.metadata as TextMetadata;
      return {
        config: metadata.config,
        content: metadata.content,
        fontSize: metadata.fontSize,
      } as TextContent;
    }
    default:
      return undefined;
  }
};

/**
 * Generates a list of dates based on the specified time range and type
 * @param timeRange - The time range (7, 15, or 30 days)
 * @param typeTimeRange - The type of time range (from yesterday or today)
 * @param dateFormat - The format to use for dates
 * @returns Array of formatted dates sorted in descending order (newest first)
 */
export const generateDateList = (
  timeRange: keyof typeof ETimeRange,
  typeTimeRange: keyof typeof ETypeTimeRange,
  dateFormat = DATE_FORMAT,
): string[] => {
  // Extract the number from the time range (e.g., "7 days" -> 7)
  const timeRangeNumber = Number(timeRange.split(' ')[0]);

  // Determine the end date based on typeTimeRange
  // If 'yesterday', use yesterday as the end date
  // If 'today', use today as the end date
  const endDate = typeTimeRange === 'yesterday' ? dayjs().subtract(1, 'day') : dayjs();

  // Generate an array of dates
  const dates = Array.from({ length: timeRangeNumber }, (_, i) => {
    // Calculate each date by subtracting days from the end date
    // For example, if timeRangeNumber is 7 and we're at index 0, we want endDate - 6
    // If we're at index 6, we want endDate - 0 (the end date itself)
    return endDate
      .clone()
      .subtract(timeRangeNumber - 1 - i, 'day')
      .format(dateFormat);
  });

  // Sort dates in descending order (newest first)
  return dates.sort((a, b) => dayjs(b, dateFormat).valueOf() - dayjs(a, dateFormat).valueOf());
};

/**
 * Gets the threshold status for a measuring item
 * @param dataItem - The measuring item with value
 * @param options - Configuration options for warnings
 * @returns The threshold status (EXCEEDED, EXCEEDED_PREPARING, or GOOD)
 */
export const getThreshold = (
  dataItem: IMeasuring & { value: number | null },
  options: {
    isWarnExceeded: boolean;
    isWarnApproaching: boolean;
  },
): EStatus => {
  // Use the enhanced evaluateThreshold function from evaluationUtils
  const { evaluateThreshold } = require('./evaluationUtils');
  const evaluation = evaluateThreshold(dataItem, options);
  return evaluation.status;
};

/**
 * Enhanced threshold checking function that applies threshold colors based on configuration
 * @param dataItem - The measuring item with value
 * @param options - Configuration options for warnings
 * @returns The threshold status with detailed information
 */
export const evaluateThreshold = (
  dataItem: IMeasuring & { value: number | null },
  options: {
    isWarnExceeded: boolean;
    isWarnApproaching: boolean;
  },
): {
  status: EStatus;
  details: {
    exceededMin?: boolean;
    exceededMax?: boolean;
    approachingMin?: boolean;
    approachingMax?: boolean;
  };
} => {
  const details = {
    exceededMin: false,
    exceededMax: false,
    approachingMin: false,
    approachingMax: false,
  };

  // If warnings are disabled, return GOOD with no details
  if (!options?.isWarnExceeded && !options?.isWarnApproaching) {
    return { status: EStatus.Good, details };
  }

  // Check for exceeded threshold
  if (options.isWarnExceeded) {
    if (dataItem?.minLimit !== null && dataItem?.value !== null && dataItem?.value < dataItem?.minLimit) {
      details.exceededMin = true;
    }
    if (dataItem?.maxLimit !== null && dataItem?.value !== null && dataItem?.value > dataItem?.maxLimit) {
      details.exceededMax = true;
    }
  }

  // Check for approaching threshold
  if (options.isWarnApproaching) {
    if (dataItem?.minTend !== null && dataItem?.value !== null && dataItem?.value < dataItem?.minTend) {
      details.approachingMin = true;
    }
    if (dataItem?.maxTend !== null && dataItem?.value !== null && dataItem?.value > dataItem?.maxTend) {
      details.approachingMax = true;
    }
  }

  // Determine overall status
  let status: EStatus = EStatus.Good;

  if (details.exceededMin || details.exceededMax) {
    status = EStatus.Exceeded;
  } else if (details.approachingMin || details.approachingMax) {
    status = EStatus.ExceededPreparing;
  }

  return { status, details };
};

/**
 * Formats the limit text for a measuring item
 * @param dataItem - The measuring item
 * @returns Formatted limit text
 */
export const getLimitText = (dataItem: IMeasuring): string => {
  if (dataItem.maxLimit !== null && dataItem.minLimit !== null) {
    return `${dataItem.minLimit} -> ${dataItem.maxLimit}`;
  }

  if (dataItem.minLimit !== null) {
    return `≥ ${dataItem.minLimit}`;
  }

  if (dataItem.maxLimit !== null) {
    return `≤ ${dataItem.maxLimit}`;
  }

  return '---';
};

// Import the mock data generator functions

/**
 * Type definition for a page in the history view
 */
export interface HistoryPage {
  header: Array<{ type: string; value: string | IMeasuring }>;
  body: Array<{
    values: Array<string | { measure: string; value: string | number; status: EStatus }>;
    isLimit?: boolean;
    hasStandard?: boolean;
    isCompliant?: boolean; // Added to support cross-page evaluation
  }>;
}

/**
 * Builds pages for historical data display based on configuration
 * Optimized for performance with memoization and efficient data processing
 *
 * @param params - Configuration parameters
 * @returns Array of pages with header and body data
 */
// Cache for mock data generation
const mockDataCache = new Map<string, any>();

export const buildPages = ({
  row,
  column,
  dateFormat = DATE_FORMAT,
  intl,
  dataStations,
  decimalFormat,
  dataContentSlide,
  realData,
}: {
  row: number;
  column: number;
  dateFormat?: string;
  intl: IntlShape;
  dataStations: IDataStation;
  decimalFormat: number;
  dataContentSlide: HistoryMetadata;
  realData?: DataHistory[];
}): HistoryPage[] => {
  // Extract configuration options - use destructuring for better performance
  const { timeRange, typeTimeRange, measure, config } = dataContentSlide;
  const { isShowColumnEvaluate: isShowStandard, isShowLimitThreshold: isShowLimited } = config.showOptions;
  const warningConfig = {
    isWarnApproaching: config.warningColor.isThresholdApproaching,
    isWarnExceeded: config.warningColor.isThresholdExceeded,
  };

  // Get measuring list from data stations - create a Map for O(1) lookups
  const measuringList = dataStations.measuringList;
  const measuringMap = new Map(measuringList.map((item) => [item.key, item]));

  // Step 1: Calculate pagination parameters - only calculate once
  const paginationConfig = calculatePaginationConfig(row, column, isShowStandard, isShowLimited);

  // Step 2: Generate date list - only calculate once
  const dateList = generateDateList(timeRange, typeTimeRange, dateFormat);

  // Step 3: Paginate dates and measures - only calculate once
  const datePages = paginateDates(dateList, paginationConfig.dataRowCount, isShowLimited);
  const measurePages = paginateMeasureParameters(measure, column, isShowStandard);

  // Get all measure keys for cross-page evaluation - calculate once and reuse
  const allMeasureKeys = [...new Set(measure)].filter((key) => key !== '---');

  // Step 4: Generate mock data with caching
  // Create cache key for mock data based on core parameters
  const mockDataCacheKey = JSON.stringify({
    timeRange,
    typeTimeRange,
    measure: [...measure].sort(), // Sort for consistent key
    decimalFormat,
    measuringListKeys: measuringList.map((item) => item.key).sort(),
  });

  let allMeasureData: DateMeasureData;
  const isCache = mockDataCache.has(mockDataCacheKey);

  // Priority: Use real data if available, then cache, then generate mock data
  if (realData && realData.length > 0) {
    // Transform real DataHistory data to the expected format
    const transformedData: DateMeasureData = {};
    realData.forEach((historyItem) => {
      const date = dayjs(historyItem.date).format(DATE_FORMAT);
      if (!transformedData[date]) {
        transformedData[date] = [];
      }
      Object.entries(historyItem.measuringLogs).forEach(([measureKey, measureData]) => {
        const measuringItem = measuringMap.get(measureKey);
        if (measuringItem) {
          const evaluation = evaluateThreshold({ ...measuringItem, value: measureData.value }, warningConfig);
          transformedData[date].push({
            measure: measureKey,
            value: measureData.value,
            status: evaluation.status,
          });
        }
      });
    });
    allMeasureData = transformedData;
    console.debug(
      `Using real data for histories: ${realData.length} entries transformed to ${
        Object.keys(transformedData).length
      } dates`,
    );
  } else if (isCache) {
    // Use cached mock data
    allMeasureData = mockDataCache.get(mockDataCacheKey);
    console.debug(`Using cached mock data for histories: ${Object.keys(allMeasureData).length} dates`);
  } else {
    // Generate new mock data
    allMeasureData = generateMockDataForHistory(dateList, allMeasureKeys, measuringList, decimalFormat, warningConfig);
    console.debug(`Generated new mock data for histories: ${Object.keys(allMeasureData).length} dates`);

    // Cache the mock data
    mockDataCache.set(mockDataCacheKey, allMeasureData);

    // Keep cache size reasonable (limit to 30 entries)
    if (mockDataCache.size > 30) {
      const firstKey = mockDataCache.keys().next().value;
      mockDataCache.delete(firstKey ?? '');
    }
  }

  // Step 5: Build pages with optimized structure
  const result = buildPagesStructureOptimized(
    datePages,
    measurePages,
    allMeasureData,
    measuringList,
    measuringMap,
    paginationConfig,
    isShowStandard,
    isShowLimited,
    intl,
    allMeasureKeys,
    warningConfig,
  );

  return result;
};

/**
 * Calculates pagination configuration based on row and column settings
 * @param row - Number of rows per page
 * @param column - Number of columns per page
 * @param isShowStandard - Whether to show evaluation column
 * @param isShowLimited - Whether to show limit threshold row
 * @returns Configuration object for pagination
 */
function calculatePaginationConfig(row: number, column: number, isShowStandard: boolean, isShowLimited: boolean) {
  // Ensure row and column are positive integers
  const safeRow = Math.max(1, Math.floor(row));
  const safeColumn = Math.max(2, Math.floor(column)); // Minimum 2 columns (time + at least one measure)

  // Calculate how many measure parameters can fit per page
  let measurePerPage = safeColumn - 1; // Subtract 1 for the time column
  if (isShowStandard) measurePerPage -= 1; // Subtract 1 for the evaluation column if shown

  // Ensure at least one measure parameter can be displayed
  measurePerPage = Math.max(1, measurePerPage);

  // Calculate available rows for data (subtract 1 if showing limit threshold)
  const dataRowCount = isShowLimited ? safeRow - 1 : safeRow;

  return {
    measurePerPage,
    dataRowCount: Math.max(1, dataRowCount), // Ensure at least one row for data
  };
}

/**
 * Paginates dates based on row configuration and adjusts for limit threshold display
 * @param dateList - List of dates to paginate
 * @param rowCount - Number of rows per page
 * @param isShowLimited - Whether to show limit threshold row
 * @returns Array of date pages
 */
export const paginateDates = (dateList: string[], rowCount: number, isShowLimited: boolean): string[][] => {
  // Ensure we have at least one row for data
  // Note: We don't need to subtract 1 for limit threshold here because rowCount already accounts for it
  // The calculatePaginationConfig function already handles this adjustment
  const effectiveRows = Math.max(1, rowCount);

  // Use the paginateArray utility to split the dates into pages
  return paginateArray(dateList, effectiveRows);
};

/**
 * Paginates measure parameters based on column configuration
 * @param measureList - List of measure parameters to paginate
 * @param column - Total number of columns available
 * @param isShowStandard - Whether to show evaluation column
 * @returns Array of measure parameter pages
 */
export const paginateMeasureParameters = (
  measureList: string[],
  column: number,
  isShowStandard: boolean,
): string[][] => {
  // Ensure column is at least 2 (time column + at least one measure)
  const safeColumn = Math.max(2, Math.floor(column));

  // Calculate how many measure parameters can fit per page
  let measurePerPage = safeColumn - 1; // Subtract 1 for the time column

  // If showing evaluation column, we need one less column for measures
  if (isShowStandard) {
    measurePerPage = Math.max(1, measurePerPage - 1); // Ensure at least one measure parameter
  }

  // Handle empty measure list
  if (!measureList || measureList.length === 0) {
    return [[]];
  }

  // Use the paginateArray utility to split the measures into pages
  return paginateArray(measureList, measurePerPage);
};

/**
 * Optimized version of buildPagesStructure that uses memoization and efficient data processing
 * This function implements the page ordering logic required by requirements 2.1, 2.2, and 2.3
 * while optimizing performance through caching and reduced redundant calculations
 *
 * @param datePages - Array of date pages
 * @param measurePages - Array of measure parameter pages
 * @param allMeasureData - All measure data for all dates
 * @param measuringList - List of all measuring items with metadata
 * @param measuringMap - Map of measuring items for O(1) lookups
 * @param paginationConfig - Configuration for pagination
 * @param isShowStandard - Whether to show evaluation column
 * @param isShowLimited - Whether to show limit threshold row
 * @param intl - Internationalization object for translations
 * @param allMeasureKeys - All measure keys across all pages (for evaluation)
 * @returns Array of pages with header and body data
 */
function buildPagesStructureOptimized(
  datePages: string[][],
  measurePages: string[][],
  allMeasureData: DateMeasureData,
  measuringList: IMeasuring[],
  measuringMap: Map<string, IMeasuring>,
  paginationConfig: { measurePerPage: number; dataRowCount: number },
  isShowStandard: boolean,
  isShowLimited: boolean,
  intl: IntlShape,
  allMeasureKeys: string[],
  warningConfig: ThresholdOptions,
): HistoryPage[] {
  // Pre-calculate evaluation results for all dates to avoid redundant calculations
  // This is a significant performance optimization for requirement 3.2
  const evaluationCache = new Map<string, boolean>();

  // Pre-calculate all date evaluations once
  if (isShowStandard) {
    for (const datePage of datePages) {
      for (const date of datePage) {
        if (date) {
          // Cache evaluation result for this date
          evaluationCache.set(date, evaluateDateAcrossPages(date, allMeasureData, allMeasureKeys).isCompliant);
        }
      }
    }
  }

  // Pre-calculate limit texts for all measuring items to avoid redundant calculations
  const limitTextCache = new Map<string, string>();
  for (const item of measuringList) {
    limitTextCache.set(item.key, getLimitText(item));
  }

  // Create a cache for header rows to avoid recreating them for each page
  const headerRowCache = new Map<string, Array<{ type: string; value: string | IMeasuring }>>();

  // Initialize result array with estimated capacity to avoid resizing
  const totalPages = datePages.length * measurePages.length;
  const pages: HistoryPage[] = new Array(totalPages);
  let pageIndex = 0;

  // For each date page (outer loop)
  for (let datePageIndex = 0; datePageIndex < datePages.length; datePageIndex++) {
    const datePage = datePages[datePageIndex];

    // For each measure page (inner loop) - this ensures all measure pages for a time period
    // are shown before moving to the next time period (Requirements 2.1, 2.2, 2.3)
    for (let measurePageIndex = 0; measurePageIndex < measurePages.length; measurePageIndex++) {
      // Get measure group for this page and pad with placeholders if needed
      const measureGroup = [...measurePages[measurePageIndex]];
      while (measureGroup.length < paginationConfig.measurePerPage) {
        measureGroup.push('---');
      }

      // Create a unique key for this measure group to use with the header cache
      const measureGroupKey = measureGroup.join('|');

      // Check if we already have a header row for this measure group
      let header = headerRowCache.get(measureGroupKey);
      if (!header) {
        // Create header row if not in cache
        header = createHeaderRowOptimized(measureGroup, measuringMap, isShowStandard, intl);
        // Store in cache for future use
        headerRowCache.set(measureGroupKey, header);
      }

      // Create body rows
      const body = [];

      // Add data rows
      for (let rowIndex = 0; rowIndex < paginationConfig.dataRowCount; rowIndex++) {
        const date = datePage[rowIndex] || '';

        // Generate body row with date and measure values
        const bodyRow = createBodyRowOptimized(
          date,
          measureGroup,
          allMeasureData,
          isShowStandard,
          measuringMap,
          evaluationCache,
          allMeasureKeys,
          warningConfig,
        );

        // If we have a valid date and evaluation is enabled, add the compliance status
        if (date && isShowStandard) {
          bodyRow.isCompliant = evaluationCache.get(date);
        }

        body.push(bodyRow);
      }

      // Add limit threshold row if configured
      if (isShowLimited) {
        const limitRow = createLimitRowOptimized(measureGroup, measuringMap, limitTextCache, isShowStandard, intl);
        body.push(limitRow);
      }

      // Add page to result array
      pages[pageIndex++] = { header, body };
    }
  }

  // Trim any unused slots if our estimate was off
  if (pageIndex < totalPages) {
    pages.length = pageIndex;
  }

  return pages;
}

/**
 * Optimized version of createHeaderRow that uses a Map for O(1) lookups
 *
 * @param measureGroup - Array of measure parameter keys to include in the header
 * @param measuringMap - Map of measuring items for O(1) lookups
 * @param isShowStandard - Whether to show the evaluation column
 * @param intl - Internationalization object for translations
 * @returns Array of header column objects with type and value
 */
function createHeaderRowOptimized(
  measureGroup: string[],
  measuringMap: Map<string, IMeasuring>,
  isShowStandard: boolean,
  intl: IntlShape,
): Array<{ type: string; value: string | IMeasuring }> {
  // Start with the time column
  const headerRow: Array<{ type: string; value: string | IMeasuring }> = [
    {
      type: 'time',
      value: intl.formatMessage({
        defaultMessage: 'Thời gian',
        id: 'screens.presentations.components.TableHistories.885104111',
      }),
    },
  ];

  // Add measure parameter columns with O(1) lookups
  const measureColumns: Array<{ type: string; value: string | IMeasuring }> = measureGroup.map((measureKey) => {
    // Use Map for O(1) lookup instead of find() which is O(n)
    const measuringItem = measuringMap.get(measureKey);

    // If the measuring item is found, use it; otherwise, use a placeholder
    return {
      type: 'measure',
      value: measuringItem || '---',
    };
  });

  // Add measure columns to the header row
  headerRow.push(...measureColumns);

  // Add evaluation column if enabled
  if (isShowStandard) {
    headerRow.push({
      type: 'standard',
      value: intl.formatMessage({
        defaultMessage: 'Đánh giá',
        id: 'screens.presentations.components.TableHistories.1625784854',
      }),
    });
  }

  return headerRow;
}

/**
 * Optimized version of createBodyRow that uses a Map for O(1) lookups
 *
 * @param date - The date for this row
 * @param measureGroup - Array of measure parameter keys to include in the row
 * @param allMeasureData - All measure data for all dates
 * @param isShowStandard - Whether to show the evaluation column
 * @param measuringMap - Map of measuring items for O(1) lookups
 * @returns Row object with values and standard flag
 */
function createBodyRowOptimized(
  date: string,
  measureGroup: string[],
  allMeasureData: DateMeasureData,
  isShowStandard: boolean,
  measuringMap: Map<string, IMeasuring>,
  evaluationCache?: Map<string, boolean>,
  allMeasureKeys?: string[],
  warningConfig?: ThresholdOptions,
): {
  values: Array<string | { measure: string; value: string | number; status: EStatus }>;
  hasStandard?: boolean;
  isCompliant?: boolean;
} {
  // If no date (empty row), return placeholder row
  if (!date) {
    const emptyValues = new Array(1 + measureGroup.length + (isShowStandard ? 1 : 0));
    emptyValues[0] = '---';

    for (let i = 0; i < measureGroup.length; i++) {
      emptyValues[i + 1] = '---';
    }

    if (isShowStandard) {
      emptyValues[emptyValues.length - 1] = '';
    }

    return {
      values: emptyValues,
      hasStandard: false,
    };
  }

  // Get all measure data for this date
  const dateMeasures = allMeasureData[date] || [];
  const dateMeasureMap = new Map(dateMeasures.map((m) => [m.measure, m]));

  // Pre-allocate array
  const rowValues = new Array(1 + measureGroup.length + (isShowStandard ? 1 : 0));
  rowValues[0] = date;

  // Create measure values for this page
  for (let i = 0; i < measureGroup.length; i++) {
    const measureKey = measureGroup[i];

    if (measureKey === '---') {
      rowValues[i + 1] = '---';
      continue;
    }

    const measureData = dateMeasureMap.get(measureKey);
    const measuringItem = measuringMap.get(measureKey);

    if (!measureData || !measuringItem) {
      rowValues[i + 1] = '---';
      continue;
    }

    // Re-calculate threshold to ensure accuracy
    const evaluation = evaluateThreshold(
      {
        ...measuringItem,
        value: measureData.value,
      },
      warningConfig || {
        isWarnExceeded: true,
        isWarnApproaching: true,
      },
    );

    rowValues[i + 1] = {
      measure: measureKey,
      value: measureData.value,
      status: evaluation.status,
    };
  }

  if (isShowStandard) {
    rowValues[rowValues.length - 1] = '';
  }

  // Calculate hasStandard based on ALL measures for this date (across all pages)
  let hasStandard = false;
  if (date && allMeasureKeys && allMeasureKeys.length > 0) {
    let allMeasuresGood = true;
    let hasValidMeasures = false;

    // Check all measures for this date, not just the ones in current page
    for (const measureKey of allMeasureKeys) {
      const measureData = dateMeasureMap.get(measureKey);
      const measuringItem = measuringMap.get(measureKey);

      if (!measureData || !measuringItem) {
        allMeasuresGood = false; // Missing data means not all good
        continue;
      }

      hasValidMeasures = true;

      // Re-calculate threshold for this measure
      const evaluation = evaluateThreshold(
        {
          ...measuringItem,
          value: measureData.value,
        },
        warningConfig || {
          isWarnExceeded: true,
          isWarnApproaching: true,
        },
      );

      // If any measure is not GOOD, set flag to false
      if (evaluation.status === EStatus.Exceeded) {
        allMeasuresGood = false;
        break; // Early exit for performance
      }
    }

    hasStandard = hasValidMeasures && allMeasuresGood;
  }

  // Get compliance status
  let isCompliant = false;
  if (isShowStandard && date) {
    if (evaluationCache && evaluationCache.has(date)) {
      isCompliant = evaluationCache.get(date)!;
    } else if (allMeasureKeys) {
      isCompliant = evaluateDateAcrossPages(date, allMeasureData, allMeasureKeys).isCompliant;
    }
  }

  return {
    values: rowValues,
    hasStandard,
    isCompliant: isShowStandard ? isCompliant : undefined,
  };
}

/**
 * Optimized version of createLimitRow that uses Maps for O(1) lookups
 *
 * @param measureGroup - Array of measure parameter keys to include in the row
 * @param measuringMap - Map of measuring items for O(1) lookups
 * @param limitTextCache - Cache of pre-calculated limit texts
 * @param isShowStandard - Whether to show the evaluation column
 * @param intl - Internationalization object for translations
 * @returns Row object with values and isLimit flag
 */
function createLimitRowOptimized(
  measureGroup: string[],
  measuringMap: Map<string, IMeasuring>,
  limitTextCache: Map<string, string>,
  isShowStandard: boolean,
  intl: IntlShape,
): {
  values: Array<string | { measure: string; value: string | number; status: EStatus }>;
  isLimit: boolean;
} {
  // Pre-allocate array with correct size for better performance
  const limitRow = new Array(1 + measureGroup.length + (isShowStandard ? 1 : 0));

  // Set the "Limit" label in the first column
  limitRow[0] = intl.formatMessage({
    defaultMessage: 'Giới hạn',
    id: 'screens.presentations.components.TableHistories.785718693',
  });

  // Map each measure key to its corresponding limit text with O(1) lookups
  for (let i = 0; i < measureGroup.length; i++) {
    const measureKey = measureGroup[i];

    // Use the pre-calculated limit text from cache if available
    if (limitTextCache.has(measureKey)) {
      limitRow[i + 1] = limitTextCache.get(measureKey)!;
    } else {
      // If not in cache, calculate it
      const dataItem = measuringMap.get(measureKey);
      limitRow[i + 1] = dataItem ? getLimitText(dataItem) : '---';
    }
  }

  // Add an empty cell for the evaluation column if it's enabled
  if (isShowStandard) {
    limitRow[limitRow.length - 1] = '';
  }

  // Return the limit row with the isLimit flag set to true
  return { values: limitRow, isLimit: true };
}
