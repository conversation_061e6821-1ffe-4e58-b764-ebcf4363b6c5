/**
 * Logging and debugging utilities for iframe error handling
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  category: string;
  message: string;
  data?: any;
  error?: Error;
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableStorage: boolean;
  maxStorageEntries: number;
  categories: string[];
}

class Logger {
  private config: LoggerConfig;
  private logs: LogEntry[] = [];

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: LogLevel.INFO,
      enableConsole: true,
      enableStorage: true,
      maxStorageEntries: 1000,
      categories: ['iframe', 'css', 'scaling', 'error'],
      ...config,
    };
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.level;
  }

  private addLogEntry(entry: LogEntry): void {
    if (this.config.enableStorage) {
      this.logs.push(entry);

      // Maintain max storage limit
      if (this.logs.length > this.config.maxStorageEntries) {
        this.logs = this.logs.slice(-this.config.maxStorageEntries);
      }
    }

    if (this.config.enableConsole && this.shouldLog(entry.level)) {
      this.logToConsole(entry);
    }
  }

  private logToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString();
    const prefix = `[${timestamp}] [${entry.category.toUpperCase()}]`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(prefix, entry.message, entry.data || '');
        break;
      case LogLevel.INFO:
        console.info(prefix, entry.message, entry.data || '');
        break;
      case LogLevel.WARN:
        console.warn(prefix, entry.message, entry.data || '');
        if (entry.error) console.warn('Error details:', entry.error);
        break;
      case LogLevel.ERROR:
        console.error(prefix, entry.message, entry.data || '');
        if (entry.error) console.error('Error details:', entry.error);
        break;
    }
  }

  debug(category: string, message: string, data?: any): void {
    this.addLogEntry({
      timestamp: new Date(),
      level: LogLevel.DEBUG,
      category,
      message,
      data,
    });
  }

  info(category: string, message: string, data?: any): void {
    this.addLogEntry({
      timestamp: new Date(),
      level: LogLevel.INFO,
      category,
      message,
      data,
    });
  }

  warn(category: string, message: string, data?: any, error?: Error): void {
    this.addLogEntry({
      timestamp: new Date(),
      level: LogLevel.WARN,
      category,
      message,
      data,
      error,
    });
  }

  error(category: string, message: string, data?: any, error?: Error): void {
    this.addLogEntry({
      timestamp: new Date(),
      level: LogLevel.ERROR,
      category,
      message,
      data,
      error,
    });
  }

  // Get logs for debugging
  getLogs(category?: string, level?: LogLevel): LogEntry[] {
    let filteredLogs = this.logs;

    if (category) {
      filteredLogs = filteredLogs.filter((log) => log.category === category);
    }

    if (level !== undefined) {
      filteredLogs = filteredLogs.filter((log) => log.level >= level);
    }

    return filteredLogs;
  }

  // Clear logs
  clearLogs(): void {
    this.logs = [];
  }

  // Export logs as JSON for debugging
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  // Get summary of errors
  getErrorSummary(): { [category: string]: number } {
    const summary: { [category: string]: number } = {};

    this.logs
      .filter((log) => log.level === LogLevel.ERROR)
      .forEach((log) => {
        summary[log.category] = (summary[log.category] || 0) + 1;
      });

    return summary;
  }
}

// Create default logger instance
export const iframeLogger = new Logger({
  level: LogLevel.DEBUG,
  enableConsole: true,
  enableStorage: true,
  maxStorageEntries: 500,
  categories: ['iframe', 'css', 'scaling', 'error', 'performance'],
});

// Specialized logging functions for iframe operations
export const logIframeOperation = {
  initialization: (data: any) => {
    iframeLogger.info('iframe', 'Iframe initialization started', data);
  },

  ready: (data: any) => {
    iframeLogger.info('iframe', 'Iframe ready', data);
  },

  error: (message: string, error: Error, data?: any) => {
    iframeLogger.error('iframe', message, data, error);
  },

  retry: (attempt: number, maxRetries: number, reason: string) => {
    iframeLogger.warn('iframe', `Retry attempt ${attempt}/${maxRetries}: ${reason}`, {
      attempt,
      maxRetries,
      reason,
    });
  },

  timeout: (operation: string, duration: number) => {
    iframeLogger.error('iframe', `Operation timeout: ${operation}`, { duration });
  },
};

export const logCSSOperation = {
  injectionStart: (config: any) => {
    iframeLogger.info('css', 'CSS injection started', config);
  },

  injectionComplete: (result: any) => {
    iframeLogger.info('css', 'CSS injection completed', result);
  },

  injectionError: (error: Error, data?: any) => {
    iframeLogger.error('css', 'CSS injection failed', data, error);
  },

  fallbackApplied: (reason: string) => {
    iframeLogger.warn('css', `Fallback CSS applied: ${reason}`);
  },

  stylesheetLoad: (url: string, success: boolean) => {
    if (success) {
      iframeLogger.debug('css', `Stylesheet loaded: ${url}`);
    } else {
      iframeLogger.warn('css', `Stylesheet failed to load: ${url}`);
    }
  },
};

export const logScalingOperation = {
  calculation: (input: any, output: any) => {
    iframeLogger.debug('scaling', 'Scale calculation', { input, output });
  },

  invalidDimensions: (dimensions: any) => {
    iframeLogger.warn('scaling', 'Invalid dimensions detected', dimensions);
  },

  boundsExceeded: (scale: number, bounds: { min: number; max: number }) => {
    iframeLogger.warn('scaling', 'Scale bounds exceeded', { scale, bounds });
  },

  applied: (transform: any) => {
    iframeLogger.debug('scaling', 'Transform applied', transform);
  },
};

export const logPerformance = {
  start: (operation: string) => {
    const startTime = performance.now();
    iframeLogger.debug('performance', `${operation} started`, { startTime });
    return startTime;
  },

  end: (operation: string, startTime: number) => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    iframeLogger.debug('performance', `${operation} completed`, {
      startTime,
      endTime,
      duration: `${duration.toFixed(2)}ms`,
    });
    return duration;
  },

  measure: (operation: string, fn: () => any) => {
    const startTime = logPerformance.start(operation);
    try {
      const result = fn();
      logPerformance.end(operation, startTime);
      return result;
    } catch (error) {
      logPerformance.end(operation, startTime);
      iframeLogger.error('performance', `${operation} failed`, { error });
      throw error;
    }
  },
};

// Debug utilities
export const debugUtils = {
  // Get current iframe state for debugging
  getIframeDebugInfo: (iframe: HTMLIFrameElement | null) => {
    if (!iframe) return { error: 'No iframe reference' };

    try {
      const doc = iframe.contentDocument;
      const styles = doc ? Array.from(doc.querySelectorAll('style, link[rel="stylesheet"]')) : [];

      return {
        src: iframe.src,
        readyState: doc?.readyState,
        hasDocument: !!doc,
        stylesCount: styles.length,
        dimensions: {
          width: iframe.style.width,
          height: iframe.style.height,
          transform: iframe.style.transform,
        },
        styles: styles.map((el) => ({
          type: el.tagName.toLowerCase(),
          id: el.id,
          href: el.tagName === 'LINK' ? (el as HTMLLinkElement).href : undefined,
          content: el.tagName === 'STYLE' ? (el as HTMLStyleElement).textContent?.substring(0, 100) + '...' : undefined,
        })),
      };
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  // Export all logs for debugging
  exportDebugData: () => {
    return {
      logs: iframeLogger.exportLogs(),
      errorSummary: iframeLogger.getErrorSummary(),
      timestamp: new Date().toISOString(),
    };
  },

  // Clear all debug data
  clearDebugData: () => {
    iframeLogger.clearLogs();
  },
};
