import { cn } from '@/utils/tailwind';
import React from 'react';

type TextAreaFieldProps = Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'onChange'> & {
  onChange?: (value: string) => void;
};

export const TextAreaField = React.forwardRef<HTMLTextAreaElement, TextAreaFieldProps>(
  ({ placeholder, rows, value, onChange, maxLength, className, style, disabled, ...props }, ref) => {
    const textAreaRef = React.useRef<HTMLTextAreaElement>(null);
    React.useImperativeHandle(ref, () => textAreaRef.current!, []);
    return (
      <textarea
        {...props}
        ref={textAreaRef}
        rows={rows}
        placeholder={placeholder}
        maxLength={maxLength}
        value={value}
        onChange={(e) => onChange && onChange(e.target.value)}
        style={style}
        disabled={disabled}
        className={cn(
          'outline-0 focus-visible:outline-0',
          'rounded-lg resize-none border border-gray-200 text-gray-800',
          'w-full h-full bg-white focus-visible:outline-0 py-3 px-4 placeholder-gray-500 text-sm',
          'hover:ring-2 active:ring-2 transition-all duration-300 ease-in',
          'placeholder:text-gray-500 disabled:placeholder:text-gray-400 focus:text-gray-800',
          'disabled:text-gray-700 disabled:opacity-50 disabled:bg-gray-50 disabled:border-gray-200 disabled:ring-0 disabled:hover:ring-0',
          className,
        )}
      />
    );
  },
);

TextAreaField.displayName = 'TextAreaField';
