import { useLedConfigStores } from '@/stores';
import { SlideConfigDto } from '@/types/slide';
import { PropsWithChildren, useMemo } from 'react';
import HeaderSession from './HeaderSession';
import FooterSession from './FooterSession';
import { cn } from '@/utils/tailwind';

type WrapperProps = {
  slide: SlideConfigDto;
  mode?: 'config' | 'preview';
};
export default function WrapperSessionPresent({ children, slide, mode = 'config' }: PropsWithChildren<WrapperProps>) {
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  if (!dataConfig) return null;

  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );
  return (
    <div
      className="flex flex-col gap-[2px] justify-center items-center h-full w-full"
      style={{ backgroundColor: defaultConfig.slideConfig?.borderColor, color: defaultConfig.slideConfig?.color }}
    >
      {slide.header.show && <HeaderSession slide={slide} mode={mode} />}
      <div
        className={cn('w-full flex-1 min-h-0 opacity-100')}
        style={{
          backgroundColor: defaultConfig.slideConfig?.background,
        }}
      >
        <div className={cn('w-full h-full opacity-100', { 'opacity-0': !slide.content.show })}>{children}</div>
      </div>
      {slide.footer.show && <FooterSession slide={slide} mode={mode} />}
    </div>
  );
}
