import envConfig from '@/constants/env';
import axios from 'axios';
import { headers } from 'next/headers';
import { PropsWithChildren, Suspense } from 'react';
import LoadingIndicator from './loading';
import { notFound } from 'next/navigation';
import { AuthLedProvider } from '@/components/layouts/AuthLedProvider';
import { ILedDetailData } from '@/types';
import { Metadata } from 'next';

const getData = async (led_key: string) => {
  const headersList = await headers();
  const host = headersList.get('host') || headersList.get('x-forwarded-host') || '';
  const protocol = process.env.NODE_ENV === 'development' ? 'http' : 'https';
  const origin = `${protocol}://${host}`;
  const response = await axios.get(`${envConfig.LED_SERVICE_ENDPOINT}/led-config/verify-config/${led_key}`, {
    withCredentials: true,
    headers: {
      'Content-Type': 'application/json',
      'x-domain': origin,
    },
  });
  return response;
};

export const metadata: Metadata = {
  title: 'iLotusLand for Public',
  description: 'iLotusLand for Public',
  icons: {
    icon: '/images/branding/logo-ill-public.svg',
  },
};

export default async function LedLayout({
  children,
  params,
}: PropsWithChildren<{ params: Promise<{ led_key: string }> }>) {
  try {
    const { led_key } = await params;
    const response = await getData(led_key);
    const result = response.data as {
      data: { isAuthenticated: boolean; isConfigured: boolean; isShared: boolean; ledDetail: ILedDetailData };
      message: string;
    };

    if (result.data) {
      return (
        <Suspense fallback={<LoadingIndicator />}>
          <AuthLedProvider initData={result.data}>
            <div className="h-screen w-screen">{children}</div>
          </AuthLedProvider>
        </Suspense>
      );
    }
  } catch (error) {
    return notFound();
  }
}
