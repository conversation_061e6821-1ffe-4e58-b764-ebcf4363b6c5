import { AutoAspectRatio } from '@/components/ui/AutoAspectRatio';
import { EFontSupport } from '@/constants';
import { usePagination } from '@/hooks/usePagination';
import { usePrevNextButtons } from '@/hooks/usePrevNextButtons';
import { useLedConfigStores, useSlideConfigStores } from '@/stores';
import { ISlide } from '@/types/slide';
import { cn } from '@/utils/tailwind';
import { CaretLeftIcon, CaretRightIcon } from '@phosphor-icons/react';
import { EmblaCarouselType } from 'embla-carousel';
import { useCallback, useMemo, useState } from 'react';
import PreviewPresentation from './PreviewPresentation';

type PropsContentSlider = {
  dataSlide: ISlide;
  isTargeted: boolean; // Add selectedIndex prop
};

export default function ContentSlider({ dataSlide, isTargeted }: PropsContentSlider) {
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const config = useSlideConfigStores((store) => store.config);
  const [emblaApi, setEmblaApi] = useState<EmblaCarouselType | null>(null);
  const [isIframeReady, setIsIframeReady] = useState(false);
  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );

  const { height, width } = config;

  // Callback khi iframe sẵn sàng
  const handleIframeReady = useCallback(() => {
    setIsIframeReady(true);
  }, []);

  return (
    <div className="flex flex-col justify-center items-center gap-[20px] h-full w-full">
      <div className="flex-1 min-h-0 w-full flex items-center justify-center relative">
        <AutoAspectRatio height={height} width={width} onIframeReady={handleIframeReady}>
          <PreviewPresentation
            dataSlide={dataSlide}
            setEmblaApi={setEmblaApi}
            isIframeReady={isIframeReady}
            isTargeted={isTargeted} // Pass selectedIndex
            className={cn(
              'flex flex-col justify-center items-center w-full h-full',
              { 'font-inter': defaultConfig.slideConfig?.font_family === EFontSupport.Inter },
              { 'font-montserrat': defaultConfig.slideConfig?.font_family === EFontSupport.Montserrat },
              { 'font-tektur': defaultConfig.slideConfig?.font_family === EFontSupport.Tektur },
              { 'font-jetbrains-mono': defaultConfig.slideConfig?.font_family === EFontSupport['JetBrains Mono'] },
              { 'font-be-vietnam-pro': defaultConfig.slideConfig?.font_family === EFontSupport['Be Vietnam Pro'] },
            )}
          />
        </AutoAspectRatio>
      </div>
      <div className="flex-shrink-0 w-full flex justify-center">
        <ControlSlide emblaApi={emblaApi!} />
      </div>
    </div>
  );
}

const ControlSlide = ({ emblaApi }: { emblaApi?: EmblaCarouselType }) => {
  const { prevBtnDisabled, nextBtnDisabled, onPrevButtonClick, onNextButtonClick } = usePrevNextButtons(emblaApi);
  const { scrollSnaps, selectedIndex } = usePagination(emblaApi);

  return (
    <div className="w-full flex flex-row items-center justify-center gap-3">
      <CaretLeftIcon
        size={24}
        className={cn('flex-shrink-0 text-gray-700 cursor-pointer', {
          'text-gray-400 cursor-not-allowed': prevBtnDisabled,
        })}
        onClick={onPrevButtonClick}
      />
      <div className="font-medium text-base text-gray-700">
        {selectedIndex + 1}/{Math.max(scrollSnaps.length, 1)}
      </div>
      <CaretRightIcon
        size={24}
        className={cn('flex-shrink-0 text-gray-700 cursor-pointer', {
          'text-gray-400 cursor-not-allowed': nextBtnDisabled,
        })}
        onClick={onNextButtonClick}
      />
    </div>
  );
};
