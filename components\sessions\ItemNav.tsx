'use client';

import { AsideItemProps } from '@/constants/nav';
import { cn } from '@/utils/tailwind';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import IconCommon from './IconLayout';
import { CaretRightIcon } from '@phosphor-icons/react';
import { Tooltip, TooltipContent, TooltipTrigger } from 'ui-components';
import { extractAfterSegments } from '@/utils';

const ItemNav = ({
  itemAside,
  isToggle,
  hiddenCaret = true,
  isModule,
}: {
  itemAside: AsideItemProps;
  isToggle: boolean;
  hiddenCaret?: boolean;
  isModule?: boolean;
}) => {
  const params = useParams();
  const { led_key } = params as { led_key: string };
  const pathName = usePathname();
  const pathItem: string = typeof itemAside.path === 'string' ? itemAside.path : itemAside.path?.pathname!;
  const segments = extractAfterSegments(pathName);

  const root = `/led/${led_key}`;
  const isActive = isModule ? pathName.startsWith(root + pathItem) : segments === pathItem;

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className="relative group" data-active={isActive}>
          <div
            className={cn(
              'absolute order-1 top-0 left-0 h-full w-full rounded-lg overflow-hidden group-hover:bg-gray-100 bg-transparent',
              { 'group-hover:bg-primary-50 bg-primary-50 text-primary-500': isModule && isActive },
              { 'bg-primary-500 text-white group-hover:bg-primary-500': isActive && !isModule },
              { 'w-11 h-11': isToggle },
            )}
          />
          <Link
            href={root + itemAside.path}
            className={cn(
              'w-max min-w-full flex flex-row p-2.5 gap-2.5 items-center text-gray-700 cursor-pointer rounded-lg relative overflow-hidden z-20',
              { 'text-primary-500 ': isModule && isActive },
              { 'text-white': isActive && !isModule },
            )}
            title={''}
          >
            <IconCommon name={itemAside.icon} className="text-current flex-shrink-0" size={isToggle ? 24 : 20} />
            <div
              className={cn(
                'flex-1 min-w-0 w-full overflow-hidden text-sm font-normal text-gray-800 visible opacity-100 transition-opacity duration-300',
                isToggle && 'invisible opacity-0 transition-opacity duration-300',
                isActive && 'text-current',
              )}
            >
              {itemAside.name}
            </div>
            {hiddenCaret && <CaretRightIcon className="text-current flex-shrink-0" size={20} weight="regular" />}
          </Link>
        </div>
      </TooltipTrigger>
      <TooltipContent
        className="px-3 py-2 text-sm font-normal text-gray-100 bg-gray-500 rounded-lg shadow"
        side="right"
        align="center"
      >
        {itemAside.name}
      </TooltipContent>
    </Tooltip>
  );
};
export default ItemNav;
