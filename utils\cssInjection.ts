/**
 * Comprehensive CSS injection utilities for iframe components
 * Handles external stylesheets, inline styles, and dynamic CSS variables
 */

export interface CSSInjectionConfig {
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  includeInlineStyles?: boolean;
  includeCSSVariables?: boolean;
  preserveOrder?: boolean;
}

export interface CSSInjectionResult {
  success: boolean;
  injectedCount: number;
  failedCount: number;
  errors: Error[];
}

const DEFAULT_CONFIG: Required<CSSInjectionConfig> = {
  timeout: 5000,
  retryAttempts: 3,
  retryDelay: 1000,
  includeInlineStyles: true,
  includeCSSVariables: true,
  preserveOrder: true,
};

/**
 * Creates a promise that resolves when a stylesheet is loaded or rejects on error
 */
function createStylesheetLoadPromise(link: HTMLLinkElement, timeout: number): Promise<void> {
  return new Promise((resolve, reject) => {
    const timeoutId = setTimeout(() => {
      reject(new Error(`Stylesheet load timeout: ${link.href}`));
    }, timeout);

    const cleanup = () => {
      clearTimeout(timeoutId);
      link.removeEventListener('load', onLoad);
      link.removeEventListener('error', onError);
    };

    const onLoad = () => {
      cleanup();
      resolve();
    };

    const onError = () => {
      cleanup();
      reject(new Error(`Failed to load stylesheet: ${link.href}`));
    };

    // Check if already loaded
    if (link.sheet) {
      cleanup();
      resolve();
      return;
    }

    link.addEventListener('load', onLoad);
    link.addEventListener('error', onError);
  });
}

/**
 * Clones external stylesheets from the parent document to iframe
 */
async function cloneExternalStylesheets(
  iframeDocument: Document,
  config: Required<CSSInjectionConfig>,
): Promise<{ success: number; failed: Error[] }> {
  const stylesheets = Array.from(document.querySelectorAll('link[rel="stylesheet"]')) as HTMLLinkElement[];

  const results = { success: 0, failed: [] as Error[] };

  if (config.preserveOrder) {
    // Process stylesheets sequentially to preserve order
    for (const stylesheet of stylesheets) {
      try {
        await cloneSingleStylesheet(iframeDocument, stylesheet, config);
        results.success++;
      } catch (error) {
        results.failed.push(error as Error);
      }
    }
  } else {
    // Process stylesheets in parallel for better performance
    const promises = stylesheets.map((stylesheet) =>
      cloneSingleStylesheet(iframeDocument, stylesheet, config)
        .then(() => ({ success: true, error: null }))
        .catch((error) => ({ success: false, error: error as Error })),
    );

    const settled = await Promise.all(promises);
    settled.forEach((result) => {
      if (result.success) {
        results.success++;
      } else if (result.error) {
        results.failed.push(result.error);
      }
    });
  }

  return results;
}

/**
 * Clones a single stylesheet with retry logic
 */
async function cloneSingleStylesheet(
  iframeDocument: Document,
  originalLink: HTMLLinkElement,
  config: Required<CSSInjectionConfig>,
): Promise<void> {
  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= config.retryAttempts; attempt++) {
    try {
      const clonedLink = originalLink.cloneNode(true) as HTMLLinkElement;

      // Ensure proper attributes
      clonedLink.rel = 'stylesheet';
      clonedLink.type = 'text/css';

      // Remove any existing id to avoid conflicts
      if (clonedLink.id) {
        clonedLink.id = `iframe-${clonedLink.id}`;
      }

      iframeDocument.head.appendChild(clonedLink);

      await createStylesheetLoadPromise(clonedLink, config.timeout);
      return; // Success, exit retry loop
    } catch (error) {
      lastError = error as Error;

      if (attempt < config.retryAttempts) {
        await new Promise((resolve) => setTimeout(resolve, config.retryDelay));
      }
    }
  }

  throw lastError || new Error('Unknown error during stylesheet cloning');
}

/**
 * Clones inline styles from the parent document
 */
function cloneInlineStyles(iframeDocument: Document): number {
  const inlineStyles = Array.from(document.querySelectorAll('style'));
  let clonedCount = 0;

  inlineStyles.forEach((style, index) => {
    try {
      const clonedStyle = style.cloneNode(true) as HTMLStyleElement;

      // Add identifier to avoid conflicts
      if (clonedStyle.id) {
        clonedStyle.id = `iframe-${clonedStyle.id}`;
      } else {
        clonedStyle.id = `iframe-inline-style-${index}`;
      }

      iframeDocument.head.appendChild(clonedStyle);
      clonedCount++;
    } catch (error) {
      console.warn('Failed to clone inline style:', error);
    }
  });

  return clonedCount;
}

/**
 * Extracts and injects CSS custom properties (variables) from the parent document
 */
function injectCSSVariables(iframeDocument: Document): void {
  const rootStyles = getComputedStyle(document.documentElement);
  const cssVariables: string[] = [];

  // Extract CSS custom properties from the root element
  for (let i = 0; i < rootStyles.length; i++) {
    const property = rootStyles[i];
    if (property.startsWith('--')) {
      const value = rootStyles.getPropertyValue(property);
      cssVariables.push(`${property}: ${value};`);
    }
  }

  if (cssVariables.length > 0) {
    const variableStyle = iframeDocument.createElement('style');
    variableStyle.id = 'iframe-css-variables';
    variableStyle.textContent = `:root { ${cssVariables.join(' ')} }`;

    // Insert at the beginning to ensure variables are available for other styles
    iframeDocument.head.insertBefore(variableStyle, iframeDocument.head.firstChild);
  }
}

/**
 * Injects Tailwind CSS base styles and utilities
 */
function injectTailwindBase(iframeDocument: Document): void {
  const tailwindBase = iframeDocument.createElement('style');
  tailwindBase.id = 'iframe-tailwind-base';
  tailwindBase.textContent = `
    /* Tailwind CSS base reset for iframe */
    *, ::before, ::after {
      box-sizing: border-box;
      border-width: 0;
      border-style: solid;
      border-color: #e5e7eb;
    }
    
    ::before, ::after {
      --tw-content: '';
    }
    
    html {
      line-height: 1.5;
      -webkit-text-size-adjust: 100%;
      -moz-tab-size: 4;
      tab-size: 4;
      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
      font-feature-settings: normal;
      font-variation-settings: normal;
    }
    
    body {
      margin: 0;
      line-height: inherit;
    }
  `;

  iframeDocument.head.appendChild(tailwindBase);
}

/**
 * Main function to inject all CSS into iframe document
 */
export async function injectAllCSS(
  iframeDocument: Document,
  config: Partial<CSSInjectionConfig> = {},
): Promise<CSSInjectionResult> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const result: CSSInjectionResult = {
    success: true,
    injectedCount: 0,
    failedCount: 0,
    errors: [],
  };

  try {
    // 1. Inject CSS variables first (highest priority)
    if (finalConfig.includeCSSVariables) {
      injectCSSVariables(iframeDocument);
      result.injectedCount++;
    }

    // 2. Inject Tailwind base styles
    injectTailwindBase(iframeDocument);
    result.injectedCount++;

    // 3. Clone external stylesheets
    const stylesheetResults = await cloneExternalStylesheets(iframeDocument, finalConfig);
    result.injectedCount += stylesheetResults.success;
    result.failedCount += stylesheetResults.failed.length;
    result.errors.push(...stylesheetResults.failed);

    // 4. Clone inline styles
    if (finalConfig.includeInlineStyles) {
      const inlineCount = cloneInlineStyles(iframeDocument);
      result.injectedCount += inlineCount;
    }

    // Set overall success based on whether critical CSS was injected
    result.success = result.injectedCount > 0 && result.failedCount < result.injectedCount;
  } catch (error) {
    result.success = false;
    result.errors.push(error as Error);
  }

  return result;
}

/**
 * Creates iframe-specific styles for proper rendering
 */
export function createIframeSpecificStyles(slideConfig?: {
  background?: string;
  color?: string;
  borderColor?: string;
}): string {
  return `
    /* Iframe-specific styles */
    body {
      margin: 0;
      padding: 0;
      width: 100vw;
      height: 100vh;
      overflow: hidden;
      background: ${slideConfig?.background || 'transparent'};
      color: ${slideConfig?.color || 'inherit'};
    }
    
    .frame-root, .frame-content {
      width: 100vw;
      height: 100vh;
    }
    
    /* Embla carousel styles for iframe */
    .embla__container {
      display: flex;
      user-select: none;
      -webkit-touch-callout: none;
      -khtml-user-select: none;
      -webkit-tap-highlight-color: transparent;
    }
    
    .embla__slide {
      position: relative;
      min-width: 0;
      flex-shrink: 0;
    }
    
    /* Table styles for iframe */
    table {
      table-layout: auto;
      color: ${slideConfig?.color || 'inherit'};
      background-color: ${slideConfig?.background || 'transparent'};
      border-collapse: collapse;
      border-spacing: 0;
    }
    
    tr th:not(:last-child), tr td:not(:last-child) {
      border-right: 1px solid ${slideConfig?.borderColor || '#e5e7eb'};
    }
    
    thead tr {
      background-color: ${slideConfig?.color ? `${slideConfig.color}40` : 'rgba(0,0,0,0.1)'};
    }
    
    tbody tr {
      background-color: ${slideConfig?.background || 'transparent'};
    }
    
    tbody tr:nth-child(even) {
      background-color: ${slideConfig?.color ? `${slideConfig.color}20` : 'rgba(0,0,0,0.05)'};
    }
    
    thead tr {
      border-bottom: 1px solid ${slideConfig?.borderColor || '#e5e7eb'};
    }

    tbody tr:not(:last-child) {
      border-bottom: 1px solid ${slideConfig?.borderColor || '#e5e7eb'};
    }
  `;
}
