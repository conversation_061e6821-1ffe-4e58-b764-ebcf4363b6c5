'use client';

import { AsideItemProps, navLedConfig } from '@/constants/nav';
import { useAppStores } from '@/stores/appStores';
import { cn } from '@/utils/tailwind';
import { ListIcon } from '@phosphor-icons/react';
import ItemNav from './ItemNav';
import Image from 'next/image';

export default function LeftNav() {
  const { toggle, setToggle } = useAppStores((store) => store);
  return (
    <aside
      className={cn(
        'flex flex-col w-[250px] border-r border-gray-200 transition-all duration-300 overflow-hidden z-10',
        {
          'w-[68px]': toggle,
        },
      )}
    >
      <div
        className={cn(
          'h-[68px] w-full bg-white flex-shrink-0 px-5 py-3 flex flex-row items-center',
          toggle && 'h-[112px] p-3 flex-col items-start',
        )}
      >
        <div
          className={cn('flex flex-row gap-2 flex-1 min-w-[165px] items-center', {
            'pl-[6px]': toggle,
          })}
        >
          <Image
            src={`/images/branding/logo-ill-public.svg`}
            alt="logo"
            width={32}
            height={32}
            priority
            quality={75}
            loading="eager"
            className="h-8 w-8 flex-shrink-0 object-contain"
          />
          <div
            className={cn(
              'flex-1 min-w-[125px] text-sm font-semibold text-gray-800 visible opacity-100 transition-opacity duration-700',
              toggle && 'invisible opacity-0',
            )}
          >
            iLotusLand <br />
            for Public
          </div>
        </div>
        <div
          className="flex items-center justify-center h-11 w-11 cursor-pointer flex-shrink-0"
          onClick={() => setToggle(!toggle)}
        >
          <ListIcon size={24} className="text-gray-700" weight="regular" />
        </div>
      </div>
      <div className="flex-1 min-h-0">
        <div className="flex flex-col gap-3 p-3">
          {navLedConfig.map((itemAside: AsideItemProps, indexItem: number) => (
            <ItemNav key={indexItem} itemAside={itemAside} isToggle={toggle} isModule />
          ))}
        </div>
      </div>
    </aside>
  );
}
