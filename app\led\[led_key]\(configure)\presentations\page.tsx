'use client';

import { useCreateClient } from '@/api/useCreateClient';
import { InputPanelSizeGroup } from '@/components/ui/InputPanelSize';
import { dataScreenOptions } from '@/constants';
import { initValueSlide } from '@/constants/slide';
import { usePagination } from '@/hooks/usePagination';
import { usePreventNavigation } from '@/hooks/usePreventNavigation';
import ConfigSlide from '@/screens/presentations/ConfigSlide';
import ContentSlider from '@/screens/presentations/ContentSlider';
import { useLedConfigStores, useLedStores, useSlideConfigStores } from '@/stores';
import { EContentType, ImageContent, ISlide, VideoMetadata } from '@/types/slide';
import { convertData, prepareData } from '@/utils/slide';
import {
  DndContext,
  closestCenter,
  PointerSensor,
  KeyboardSensor,
  useSensor,
  useSensors,
  MeasuringStrategy,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
  sortableKeyboardCoordinates,
  arrayMove,
} from '@dnd-kit/sortable';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { PlusIcon } from '@phosphor-icons/react';
import Fade from 'embla-carousel-fade';
import useEmblaCarousel from 'embla-carousel-react';
import { use, useCallback, useEffect, useMemo, useState } from 'react';
import { defineMessages, FormattedMessage, useIntl } from 'react-intl';
import { Button, Form, Separator, Spin, toast } from 'ui-components';
import CardSlider from '@/screens/presentations/CardSlider';
import Preview from '@/screens/presentations/present/Preview';

type TFormData = {
  width: number;
  height: number;
  presentations: ISlide[];
};

const message = defineMessages({
  resizeError: {
    defaultMessage: 'Kích thước không hợp lệ. Vui lòng nhập giá trị từ 320 px đến 3840 px.',
    id: 'app.led.[led_key].(configure).presentations.page.1682010608',
  },
  submitSuccess: {
    defaultMessage: 'Đã lưu cấu hình trang trình chiếu thành công.',
    id: 'app.led.[led_key].(configure).presentations.page.129890065',
  },
  submitError: {
    defaultMessage: 'Không thể lưu cấu hình trang trình chiếu. Vui lòng thử lại sau.',
    id: 'app.led.[led_key].(configure).presentations.page.1103570150',
  },
  saveError: {
    defaultMessage: 'Không thể chuyển sang cấu hình giá trị mặc định. Vui lòng thử lại sau.',
    id: 'app.led.[led_key].(configure).presentations.page.1962099229',
  },
  dragEndError: {
    defaultMessage: 'Không thể sắp xếp trang trình chiếu. Vui lòng thử lại sau.',
    id: 'app.led.[led_key].(configure).presentations.page.dragEndError',
  },
});

export default function LedPresentations({ params }: { params: Promise<{ led_key: string }> }) {
  const dataLedDetail = useLedStores((store) => store.dataLedDetail);
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const setConfig = useSlideConfigStores((store) => store.setConfig);
  const { led_key } = use(params) as { led_key: string };
  const intl = useIntl();
  const { ledConfig } = useCreateClient();
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: false, watchDrag: false, containScroll: false }, [Fade()]);
  const { selectedIndex, onDotButtonClick } = usePagination(emblaApi);
  const { mutateAsync: mutatePresentations, isPending } = ledConfig.configurePresentations({
    invalidateQueries: [
      {
        enable: true,
      },
      {
        enable: true,
        queryKey: ['LedServiceClient', 'getLedConfig', led_key],
      },
    ],
  });
  const locale = intl.locale as 'vi' | 'en';
  const [form] = Form.useForm<TFormData>();
  const [formChange, setFormChange] = useState(false);
  const [typeSubmit, setTypeSubmit] = useState<'save' | 'submit'>('submit');
  const measuringConfig = {
    droppable: {
      strategy: MeasuringStrategy.Always,
    },
  };
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  usePreventNavigation({
    isDirty: formChange,
  });

  const initFormValue: TFormData = useMemo(() => {
    const strLocal = localStorage.getItem(`config-${led_key}`) ?? '';
    const config = (strLocal?.length ? JSON.parse(strLocal) : { height: 1080, width: 1920 }) as {
      height: number;
      width: number;
    };
    return {
      width: Number(config?.width || 1920) || 1920,
      height: Number(config?.height || 1080) || 1080,
      presentations: Array.from({ length: Math.max(dataConfig?.presentations.length || 0, 1) }, (_, index) => {
        const slide = dataConfig?.presentations[index];
        if (!slide)
          return {
            ...initValueSlide,
            id: Math.random().toString(36).substring(2, 9),
          };
        return convertData(slide);
      }).sort((a, b) => a.order - b.order),
    };
  }, []);

  const handleSubmit = useCallback(
    async (values: TFormData) => {
      try {
        const formData = new FormData();
        const body = prepareData(values.presentations);

        body.forEach((slide, slideIndex) => {
          // Handle multiple video slides
          if (slide.content.type === EContentType.video) {
            const video = (slide.content?.metadata as VideoMetadata)?.video;
            if (video instanceof File) {
              formData.append(`videos[${slideIndex}]`, video);
            }
          }
        });
        // Handle multiple image slides
        const metaImages = body.find((slide) => slide.content.type === EContentType.images);
        if (metaImages?.content.metadata && 'images' in metaImages?.content.metadata) {
          const metadata = metaImages.content.metadata as ImageContent;
          metadata.images?.forEach((image, index) => {
            if (image && image.url instanceof File) {
              formData.append(`images[${index}]`, image.url);
            }
          });
        }

        formData.append('body', JSON.stringify(body));
        await mutatePresentations([led_key, formData]);
        setFormChange(false);
        toast({
          type: 'success',
          title: intl.formatMessage(message.submitSuccess),
          options: {
            position: 'top-center',
          },
        });
      } catch (error) {
        console.log({ error });
        toast({
          type: 'error',
          title:
            typeSubmit === 'submit' ? intl.formatMessage(message.submitError) : intl.formatMessage(message.saveError),
          options: {
            position: 'top-center',
          },
        });
      }
    },
    [form],
  );

  const recalculateOrder = useCallback((values: any[]) => {
    return values.map((item, index) => ({
      ...item,
      order: index,
    }));
  }, []);

  const handleDuplicate = useCallback(
    (index: number) => {
      const presentations = form.getFieldValue('presentations') as ISlide[];
      const itemToDuplicate = presentations[index];
      if (itemToDuplicate) {
        const cloned = JSON.parse(JSON.stringify(itemToDuplicate));
        const newItem = [...presentations.slice(0, index + 1), cloned, ...presentations.slice(index + 1)];
        const recalculated = recalculateOrder(newItem);
        form.setFieldsValue({
          presentations: recalculated,
        });
        setTimeout(() => {
          onDotButtonClick(index + 1);
        }, 100);
      }
    },
    [form, onDotButtonClick],
  );

  const presentations = (Form.useWatch('presentations', form) as ISlide[]) ?? [];

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (!over || active.id === over.id) {
        return;
      }

      try {
        const sourceIndex = presentations.findIndex((slide) => slide.id === active.id);
        const targetIndex = presentations.findIndex((slide) => slide.id === over.id);

        if (sourceIndex !== -1 && targetIndex !== -1) {
          const reorderedPresentations = arrayMove(presentations, sourceIndex, targetIndex);
          const recalculated = recalculateOrder(reorderedPresentations);

          form.setFieldsValue({
            presentations: recalculated,
          });

          // Update selected index if needed
          if (selectedIndex === sourceIndex) {
            onDotButtonClick(targetIndex);
          } else if (selectedIndex === targetIndex) {
            onDotButtonClick(sourceIndex > targetIndex ? selectedIndex + 1 : selectedIndex - 1);
          } else if (
            selectedIndex > Math.min(sourceIndex, targetIndex) &&
            selectedIndex <= Math.max(sourceIndex, targetIndex)
          ) {
            onDotButtonClick(sourceIndex > targetIndex ? selectedIndex + 1 : selectedIndex - 1);
          }

          setFormChange(true);
        }
      } catch (error) {
        console.error('Drag end error:', error);
        toast({
          type: 'error',
          title: intl.formatMessage(message.dragEndError),
          options: {
            position: 'top-center',
          },
        });
      }
    },
    [form, presentations, onDotButtonClick, selectedIndex],
  );

  useEffect(() => {
    form.setFieldsValue(initFormValue);
    setConfig({ width: initFormValue.width, height: initFormValue.height });
  }, [initFormValue, form, setConfig]);

  return (
    <Form
      form={form}
      initialValues={initFormValue}
      onFinish={(values) => {
        setTypeSubmit('submit');
        handleSubmit(values);
      }}
      onKeyDown={(event) => {
        if (event.key === 'Enter') {
          event.preventDefault();
        }
      }}
      onFieldsChange={(_, allFields) => {
        const isChange = allFields.some((field) => {
          const fieldName = field.name[0];
          return fieldName !== 'width' && fieldName !== 'height' && field.touched;
        });
        setFormChange(isChange);
      }}
      className="flex flex-col h-full w-full"
    >
      <Spin loading={isPending} className="flex flex-col h-full w-full">
        <div className="h-[68px] flex-shrink-0 border-b border-gray-200 py-4 pl-5 pr-4 flex flex-row justify-between items-center">
          <div className="flex flex-col">
            <span className="font-semibold text-lg text-gray-700">{dataLedDetail?.name[locale]}</span>
            <span className="font-normal text-xs text-gray-600">
              <FormattedMessage
                defaultMessage="Thiết kế trang trình chiếu"
                id="components.sessions.HeaderConfig.2096504904"
              />
            </span>
          </div>
          <div className="flex flex-row gap-3 items-center h-full">
            <Form.Field name="width" trigger="onBlur">
              {(controlWidth) => {
                return (
                  <Form.Field name="height" trigger="onBlur">
                    {(controlHeight) => {
                      return (
                        <InputPanelSizeGroup
                          value={[controlWidth.value, controlHeight.value]}
                          onBlur={(values) => {
                            const [width, height] = values;
                            controlWidth.onBlur(width);
                            controlHeight.onBlur(height);
                            setConfig({ width: +width, height: +height });
                            localStorage.setItem(
                              `config-${led_key}`,
                              JSON.stringify({ width: width.toString(), height: height.toString() }),
                            );
                          }}
                          initValue={[1920, 1080]}
                          selectConfig={{
                            selectOptions: dataScreenOptions,
                          }}
                        />
                      );
                    }}
                  </Form.Field>
                );
              }}
            </Form.Field>
            <Separator orientation="vertical" className="h-11 border-l border-gray-200" />
            <Preview presentations={presentations} />
            <Button type="submit" disabled={isPending}>
              <FormattedMessage
                defaultMessage="Lưu cấu hình"
                id="app.led.[led_key].(configure).presentations.page.1043319462"
              />
            </Button>
          </div>
        </div>
        <div className="flex flex-row flex-1 min-h-0 bg-gray-100">
          <Form.List name="presentations">
            {(fields, { add, remove }) => {
              return (
                <div className="w-[200px] flex-shrink-0 h-fit max-h-full flex flex-col gap-4 py-4 px-0">
                  <DndContext
                    sensors={sensors}
                    measuring={measuringConfig}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                    modifiers={[restrictToVerticalAxis]}
                    autoScroll={false}
                  >
                    <SortableContext items={presentations.map((p) => p.id)} strategy={verticalListSortingStrategy}>
                      <div className="flex-1 min-h-0 h-fit overflow-x-hidden overflow-y-auto">
                        <div className="flex flex-col gap-4 px-4 py-0 h-full w-full">
                          {presentations.map((slide, index) => {
                            return (
                              <Form.Field name={[index, 'show']} key={'slide' + slide.id}>
                                {(control) => {
                                  return (
                                    <CardSlider
                                      id={slide.id}
                                      focusedIndex={selectedIndex}
                                      onChangeItem={onDotButtonClick}
                                      name={index}
                                      onRemove={remove}
                                      isDeleted={fields.length > 1}
                                      onUpdateToggle={() => control.onChange(!control.value)}
                                      isHidden={!control.value}
                                      onDuplicate={() => handleDuplicate(index)}
                                    />
                                  );
                                }}
                              </Form.Field>
                            );
                          })}
                        </div>
                      </div>
                    </SortableContext>
                  </DndContext>

                  <div className="flex-shrink-0 px-4 py-0">
                    <Button
                      variant="neutral"
                      type="button"
                      className="w-full text-primary-500 flex-shrink-0"
                      onClick={(event) => {
                        event.stopPropagation();
                        const slide: ISlide = {
                          ...initValueSlide,
                          id: Math.random().toString(36).substring(2, 9),
                          order: fields.length,
                          title: {
                            vi: `Trang số ${fields.length + 1}`,
                            en: `Slide ${fields.length + 1}`,
                          },
                          content: {
                            show: true,
                            type: null,
                            metadata: undefined,
                          },
                        };
                        add(slide);
                        setTimeout(() => {
                          onDotButtonClick(fields.length);
                        }, 0);
                      }}
                    >
                      <PlusIcon size={20} weight="regular" className="text-current flex-shrink-0" />
                      <span className="font-medium text-sm text-current">
                        <FormattedMessage
                          defaultMessage="Thêm trang"
                          id="app.led.[led_key].(configure).presentations.page.452164499"
                        />
                      </span>
                    </Button>
                  </div>
                </div>
              );
            }}
          </Form.List>

          <Form.Field name="presentations" shouldUpdate>
            {(control) => {
              return (
                <div className="flex-1 min-w-0 h-full bg-gray-200 flex items-center justify-center p-10">
                  <div ref={emblaRef} className="h-full w-full overflow-hidden">
                    <div className="embla__container flex h-full w-full">
                      {((control.value as ISlide[]) || []).map((slide, index) => {
                        if (slide)
                          return (
                            <div key={index} className="embla__slide flex-[0_0_100%] min-w-0 h-full">
                              <ContentSlider dataSlide={slide} isTargeted={selectedIndex === index} />
                            </div>
                          );

                        return null;
                      })}
                    </div>
                  </div>
                </div>
              );
            }}
          </Form.Field>

          <Form.Field name="presentations" shouldUpdate>
            {(control) => {
              const presentations = (control.value as ISlide[]) ?? [];
              const currentSlide = presentations[selectedIndex];
              if (!currentSlide) return null;
              return (
                <ConfigSlide
                  dataSlide={currentSlide}
                  focusedIndex={selectedIndex}
                  form={form}
                  onChangeType={setTypeSubmit}
                />
              );
            }}
          </Form.Field>
        </div>
      </Spin>
    </Form>
  );
}
