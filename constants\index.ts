import { OptionItemType } from 'ui-components';

export const routePath = {
  led: '/led',
  ledDetail: (id: string) => `/led/${id}`,
  ledConfig: (id: string) => `/led/${id}/config`,
  ledConfigSlide: (id: string) => `/led/${id}/config/slide-shows`,
  ledConfigDefault: (id: string) => `/led/${id}/config/default-data`,
  ledAuth: (id: string) => `/led/${id}/auth`,
  auth: '/led/auth',
};

export const segmentsPath = {
  ledConfig: '/config',
  ledConfigSlideShows: '/config/slide-shows',
  ledConfigDefaultData: '/config/default-data',
  ledPresentations: '/presentations',
};

export const LANGUEGES_VI = 'vi';
export const LANGUEGES_EN = 'en';
export const NEXT_LOCALE = 'configLng';

export const CONFIG_LANGUAGE = Object.freeze({
  locales: [LANGUEGES_VI, LANGUEGES_EN],
  defaultLocale: LANGUEGES_VI,
  cookieName: NEXT_LOCALE,
});

export const LANGUEGES_OPTIONS = [
  {
    value: LANGUEGES_EN,
    label: 'English',
    image: '/images/languages/en-flag.svg',
  },
  {
    value: LANGUEGES_VI,
    label: 'Tiếng Việt',
    image: '/images/languages/vi-flag.svg',
  },
];

export enum EFontSupport {
  Inter = 'inter',
  Montserrat = 'montserrat',
  Tektur = 'tektur',
  'JetBrains Mono' = 'jetbrains-mono',
  'Be Vietnam Pro' = 'be-vietnam-pro',
}

export const fontOptions = [
  {
    title: 'Inter',
    value: EFontSupport.Inter,
  },
  {
    title: 'Montserrat',
    value: EFontSupport.Montserrat,
  },
  {
    title: 'Tektur',
    value: EFontSupport.Tektur,
  },
  {
    title: 'JetBrains Mono',
    value: EFontSupport['JetBrains Mono'],
  },
  {
    title: 'Be Vietnam Pro',
    value: EFontSupport['Be Vietnam Pro'],
  },
];

export const dataTypeObject = Object.freeze({
  realtime: 'realtime',
  histories: 'histories',
  images: 'images',
  video: 'video',
  text: 'text',
});

export const footerType = Object.freeze({
  timestamp: 'timestamp',
  bannerText: 'banner-text',
});

export const ContentLayout = Object.freeze({
  grid: 'grid',
  table: 'table',
});

export type dataType = 'realtime' | 'histories' | 'images' | 'video' | 'text';

export const dataScreenOptions: OptionItemType[] = [
  {
    title: '1920x1080',
    value: '1920x1080',
    prefix: 'MonitorIcon',
  },
  {
    title: '1280x960',
    value: '1280x960',
    prefix: 'MonitorIcon',
  },
  {
    title: '1280x720',
    value: '1280x720',
    prefix: 'MonitorIcon',
  },
  {
    title: '1080x1080',
    value: '1080x1080',
    prefix: 'MonitorIcon',
  },
  {
    title: '1024x768',
    value: '1024x768',
    prefix: 'MonitorIcon',
  },
  {
    title: '768x1024',
    value: '768x1024',
    prefix: 'DeviceTabletSpeakerIcon',
  },
  {
    title: '640x320',
    value: '640x320',
    prefix: 'DeviceTabletSpeakerIcon',
  },
  {
    title: '390x844',
    value: '390x844',
    prefix: 'DeviceMobileSpeakerIcon',
  },
  {
    title: '320x320',
    value: '320x320',
    prefix: 'SquaresFourIcon',
  },
];

export enum GridView {
  general = 'general',
  custom = 'custom',
}

export const DATETIME_FORMAT = 'HH:mm DD/MM/YYYY';
export const DATE_FORMAT = 'DD/MM/YYYY';
