import { cn } from '@/utils/tailwind';
import { PropsWithChildren } from 'react';

export default function GroupBox({
  children,
  className,
  isVertical = true,
}: PropsWithChildren & {
  className?: string;
  isVertical?: boolean;
}) {
  return (
    <div
      className={cn(
        'flex border border-gray-200 rounded-xl overflow-hidden',
        {
          'flex-col [&_>:not(:last-child)]:border-b [&_>:not(:last-child)]:border-gray-200': isVertical,
        },
        {
          'flex-row [&_>:not(:first-child)]:border-l [&_>:not(:first-child)]:border-gray-200': !isVertical,
        },
        className,
      )}
    >
      {children}
    </div>
  );
}
