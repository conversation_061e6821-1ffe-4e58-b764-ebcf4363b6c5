import React, { useMemo } from 'react';
import { Options, useResponsiveFontSize } from '@/hooks/useResponsiveFontSize';

type ResponsiveTextProps<T extends keyof React.JSX.IntrinsicElements> = {
  children: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
  as?: T;
  value?: number;
  priority?: 0 | 1 | 2 | 3 | 4 | 5 | 6;
  options?: Options;
} & Omit<React.JSX.IntrinsicElements[T], 'children' | 'style' | 'className'>;

export const PRIORITY_SCALE = {
  0: 2.5,
  1: 2.0,
  2: 1.6,
  3: 1.2,
  4: 1.0,
  5: 0.8,
  6: 0.6,
} as const;

export function ResponsiveTextWithOverride<T extends keyof React.JSX.IntrinsicElements = 'span'>({
  children,
  style,
  className,
  as,
  value,
  priority,
  options,
  ...props
}: ResponsiveTextProps<T>) {
  const restOptions = { isAuto: true, ...options };
  const responsiveFontSize = useResponsiveFontSize(restOptions);
  const scale = useMemo(() => (priority ? PRIORITY_SCALE[priority] : null), [priority]);

  const fontSize = useMemo(() => {
    if (restOptions.isAuto && scale) return Math.round(responsiveFontSize * scale);
    if (value !== null && value !== undefined) return value;
    return Math.round(responsiveFontSize);
  }, [responsiveFontSize, scale, value, restOptions.isAuto]);

  const Component = as ?? ('span' as T);

  return (
    <Component {...(props as any)} style={{ fontSize, ...style }} className={className}>
      {children}
    </Component>
  );
}
