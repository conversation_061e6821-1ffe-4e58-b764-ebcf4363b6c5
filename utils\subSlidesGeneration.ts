import { DataHistory } from '@/api/type';
import { DATE_FORMAT } from '@/constants';
import { IDataStation } from '@/types';
import { ContentType, HistoryMetadata, ISubSlide, RealTimeMetadata, SlideConfigDto } from '@/types/slide';
import { IntlShape } from 'react-intl';
import {
  createError,
  errorHandler,
  ErrorType,
  safeExecuteSync,
  validateApiData,
  validateWithError,
} from './errorHandling';
import { buildPages, HistoryPage } from './slide';

// Performance optimization: Enhanced cache for buildPages results
interface BuildPagesCache {
  key: string;
  result: HistoryPage[];
  timestamp: number;
  memorySize: number;
  accessCount: number;
  lastAccessed: number;
}

// Enhanced cache configuration with adaptive settings
const CACHE_CONFIG = {
  maxSize: 100, // Increased maximum number of cached entries
  maxAge: 10 * 60 * 1000, // Increased to 10 minutes in milliseconds
  maxMemoryMB: 200, // Increased maximum memory usage in MB
  cleanupInterval: 30 * 1000, // More frequent cleanup - 30 seconds
  compressionThreshold: 50, // Compress results larger than 50MB
  preloadThreshold: 3, // Preload next subSlides when within 3 slides
  adaptiveCleanup: true, // Enable adaptive cleanup based on usage patterns
};

// Global cache storage with enhanced tracking
const buildPagesCache = new Map<string, BuildPagesCache>();
let cacheCleanupTimer: NodeJS.Timeout | null = null;

// Memory usage tracking
let totalCacheMemoryMB = 0;

// Performance metrics tracking
interface PerformanceMetrics {
  cacheHits: number;
  cacheMisses: number;
  totalGenerations: number;
  averageGenerationTime: number;
  memoryPeakUsage: number;
}

const performanceMetrics: PerformanceMetrics = {
  cacheHits: 0,
  cacheMisses: 0,
  totalGenerations: 0,
  averageGenerationTime: 0,
  memoryPeakUsage: 0,
};

// Memory pool for reusing objects
class MemoryPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (item: T) => void;
  private maxSize: number;

  constructor(createFn: () => T, resetFn: (item: T) => void, maxSize: number = 50) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    this.maxSize = maxSize;
  }

  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }

  release(item: T): void {
    if (this.pool.length < this.maxSize) {
      this.resetFn(item);
      this.pool.push(item);
    }
  }

  clear(): void {
    this.pool.length = 0;
  }
}

// Memory pools for common objects
const subSlidePool = new MemoryPool<ISubSlide>(
  () => ({
    id: '',
    content: {},
    show: true,
    duration: 5000,
  }),
  (item) => {
    item.id = '';
    item.content = {};
    item.show = true;
    item.duration = 5000;
  },
);

// Compression utilities for large cache entries
const compressionUtils = {
  /**
   * Simple compression for cache entries using JSON stringification with reduced precision
   */
  compress: (data: HistoryPage[]): string => {
    try {
      // Reduce precision of numeric values to save space
      const compressed = JSON.stringify(data, (key, value) => {
        if (typeof value === 'number' && !Number.isInteger(value)) {
          return Math.round(value * 100) / 100; // Round to 2 decimal places
        }
        return value;
      });
      return compressed;
    } catch {
      return JSON.stringify(data);
    }
  },

  /**
   * Decompress cache entries
   */
  decompress: (compressed: string): HistoryPage[] => {
    try {
      return JSON.parse(compressed);
    } catch {
      return [];
    }
  },

  /**
   * Check if data should be compressed based on size
   */
  shouldCompress: (data: HistoryPage[]): boolean => {
    try {
      const jsonString = JSON.stringify(data);
      const sizeInMB = (jsonString.length * 2) / (1024 * 1024);
      return sizeInMB > CACHE_CONFIG.compressionThreshold / 1024; // Convert KB to MB
    } catch {
      return false;
    }
  },
};

/**
 * Initialize cache cleanup timer
 */
const initializeCacheCleanup = () => {
  if (cacheCleanupTimer) {
    return;
  }

  cacheCleanupTimer = setInterval(() => {
    cleanupCache();
  }, CACHE_CONFIG.cleanupInterval);

  // Cleanup on process exit (Node.js environment)
  if (typeof process !== 'undefined' && process.on) {
    process.on('exit', () => {
      if (cacheCleanupTimer) {
        clearInterval(cacheCleanupTimer);
      }
    });
  }
};

/**
 * Enhanced cleanup with adaptive algorithms and LRU eviction
 */
const cleanupCache = () => {
  const now = Date.now();
  const entriesToDelete: string[] = [];

  // Find expired entries
  for (const [key, entry] of buildPagesCache.entries()) {
    if (now - entry.timestamp > CACHE_CONFIG.maxAge) {
      entriesToDelete.push(key);
    }
  }

  // Delete expired entries
  for (const key of entriesToDelete) {
    const entry = buildPagesCache.get(key);
    if (entry) {
      totalCacheMemoryMB -= entry.memorySize;
      buildPagesCache.delete(key);
    }
  }

  // Adaptive cleanup based on usage patterns
  if (CACHE_CONFIG.adaptiveCleanup && totalCacheMemoryMB > CACHE_CONFIG.maxMemoryMB * 0.8) {
    // Use LRU (Least Recently Used) algorithm for smarter eviction
    const sortedEntries = Array.from(buildPagesCache.entries()).sort(([, a], [, b]) => {
      // Prioritize by access frequency and recency
      const aScore = a.accessCount * 0.3 + (now - a.lastAccessed) * 0.7;
      const bScore = b.accessCount * 0.3 + (now - b.lastAccessed) * 0.7;
      return bScore - aScore; // Higher score = less likely to be evicted
    });

    // Remove least valuable entries first
    while (totalCacheMemoryMB > CACHE_CONFIG.maxMemoryMB && sortedEntries.length > 0) {
      const [key, entry] = sortedEntries.pop()!;
      totalCacheMemoryMB -= entry.memorySize;
      buildPagesCache.delete(key);
    }
  } else if (totalCacheMemoryMB > CACHE_CONFIG.maxMemoryMB) {
    // Fallback to timestamp-based cleanup
    const sortedEntries = Array.from(buildPagesCache.entries()).sort(([, a], [, b]) => a.timestamp - b.timestamp);

    while (totalCacheMemoryMB > CACHE_CONFIG.maxMemoryMB && sortedEntries.length > 0) {
      const [key, entry] = sortedEntries.shift()!;
      totalCacheMemoryMB -= entry.memorySize;
      buildPagesCache.delete(key);
    }
  }

  // If still over size limit, remove oldest entries
  while (buildPagesCache.size > CACHE_CONFIG.maxSize) {
    const oldestKey = buildPagesCache.keys().next().value;
    const entry = buildPagesCache.get(oldestKey ?? '');
    if (entry) {
      totalCacheMemoryMB -= entry.memorySize;
      buildPagesCache.delete(oldestKey ?? '');
    }
  }
};

/**
 * Generate cache key for buildPages results
 */
const generateCacheKey = (
  metadata: HistoryMetadata,
  layoutSettings: any,
  hasRealData: boolean,
  realDataHash?: string,
): string => {
  const keyParts = [
    metadata.station,
    metadata.stationType,
    metadata.timeRange,
    metadata.typeTimeRange,
    JSON.stringify(metadata.measure ? metadata.measure.sort() : []), // Ensure array for sort
    JSON.stringify(layoutSettings ?? {}), // Ensure object for stringify
    hasRealData ? 'real' : 'mock',
    realDataHash ?? 'no-data',
  ];

  return keyParts.join('|');
};

/**
 * Estimate memory size of buildPages result
 */
const estimateMemorySize = (pages: HistoryPage[]): number => {
  try {
    const jsonString = JSON.stringify(pages);
    // Rough estimation: 2 bytes per character in UTF-16
    const sizeInBytes = jsonString.length * 2;
    return sizeInBytes / (1024 * 1024); // Convert to MB
  } catch {
    // Fallback estimation based on page count
    return pages.length * 0.1; // Assume 0.1MB per page
  }
};

/**
 * Generate hash for real data to use in cache key
 */
const generateDataHash = (realData?: DataHistory[]): string => {
  if (!realData || realData.length === 0) {
    return 'empty';
  }

  // Create a simple hash based on data length, first/last item's date, and a hash of measuringLogs keys
  const firstItem = realData[0];
  const lastItem = realData[realData.length - 1];

  const getMeasuringLogsHash = (data: DataHistory) => {
    if (!data.measuringLogs) return '';
    return Object.keys(data.measuringLogs).sort().join(',');
  };

  return `${realData.length}-${firstItem?.date || 'no-date'}-${getMeasuringLogsHash(firstItem)}-${
    lastItem?.date || 'no-date'
  }-${getMeasuringLogsHash(lastItem)}`;
};

/**
 * Main function to generate subSlides for a slide based on its content type and metadata
 * @param slide - The slide configuration DTO
 * @param intl - Internationalization instance for formatting
 * @param dataStations - Station data for histories processing
 * @param realData - Optional real DataHistory data for histories type
 * @returns Array of subSlides or empty array if no subSlides should be generated
 */
export const generateSubSlides = (
  slide: SlideConfigDto,
  intl?: IntlShape,
  dataStations?: IDataStation,
  realData?: DataHistory[],
): ISubSlide[] => {
  return safeExecuteSync(
    () => {
      const { content } = slide;

      // Validate slide structure
      validateWithError(!!slide, 'Slide configuration is required', { slide });
      validateWithError(!!content, 'Slide content is required', { slide });

      // Return empty array if content is not shown or missing required data
      if (!content.show || !content.type || !content.metadata) {
        return [];
      }

      // Validate content structure
      validateApiData(content, {
        show: 'boolean',
        type: 'string',
        metadata: 'object',
      });

      // Initialize cache cleanup on first use
      initializeCacheCleanup();

      const generateOperation = () => {
        switch (content.type) {
          case 'realtime':
            return generateRealtimeSubSlides(content.metadata as RealTimeMetadata);

          case 'histories':
            if (!intl || !dataStations) {
              throw createError.configuration('Cannot generate histories subSlides without required dependencies', {
                hasIntl: !!intl,
                hasDataStations: !!dataStations,
                slideId: slide.id,
              });
            }
            return generateHistoriesSubSlides(content.metadata as HistoryMetadata, intl, dataStations, realData);

          case 'text':
          case 'images':
          case 'video':
          default:
            // These content types don't have subSlides
            return [];
        }
      };

      // Measure performance of subSlides generation with error handling
      const { result, duration } = performanceUtils.measureGenerationTimeSync(
        generateOperation,
        `generateSubSlides for type ${content.type}`,
      );

      // Validate result
      validateWithError(Array.isArray(result), 'SubSlides generation must return an array', {
        slideId: slide.id,
        contentType: content.type,
        resultType: typeof result,
      });

      return result;
    },
    [], // Fallback to empty array on error
    ErrorType.DATA_PROCESSING_ERROR,
    { slideId: slide.id, contentType: slide.content?.type },
  );
};

/**
 * Generate subSlides for realtime content based on measure array and layout configuration
 * Creates paginated subSlides where each page contains measures based on layout row × column configuration
 * @param metadata - RealTime metadata containing measure, layout, and other configuration
 * @returns Array of subSlides, one for each page of measures based on layout pagination
 */
export const generateRealtimeSubSlides = (metadata: RealTimeMetadata): ISubSlide[] => {
  return safeExecuteSync(
    () => {
      // Validate metadata structure
      validateWithError(!!metadata, 'Realtime metadata is required', { metadata });

      const { measure, layout, station, stationType, showOptions, cameras } = metadata;

      // Validate required fields with detailed error messages
      validateWithError(!!measure && Array.isArray(measure), 'Measure array is required for realtime subSlides', {
        metadata,
        hasMeasure: !!measure,
        measureType: typeof measure,
      });

      validateWithError(!!layout, 'Layout configuration is required for realtime subSlides', {
        metadata,
        hasLayout: !!layout,
      });

      validateWithError(!!station && typeof station === 'string', 'Station is required for realtime subSlides', {
        metadata,
        station,
        stationType: typeof station,
      });

      validateWithError(
        !!stationType && typeof stationType === 'string',
        'Station type is required for realtime subSlides',
        {
          metadata,
          stationType,
          stationTypeType: typeof stationType,
        },
      );

      // Return empty array if no measure data
      if (measure.length === 0) {
        console.debug('Realtime subSlides generation skipped: empty measure array');
        return [];
      }

      // Filter out placeholder measures with error tracking
      const validMeasures = measure.filter((m) => {
        const isValid = m && typeof m === 'string' && m.trim() !== '' && m !== '---';
        if (!isValid) {
          console.debug('Filtered out invalid measure:', m);
        }
        return isValid;
      });

      if (validMeasures.length === 0) {
        throw createError.validation('No valid measures found after filtering', {
          originalMeasures: measure,
          filteredCount: validMeasures.length,
          station,
          stationType,
        });
      }

      // Get layout settings for pagination
      const layoutSettings = getRealtimeLayoutSettings(layout);
      const pageSize = layoutSettings.row * layoutSettings.column;
      const cameraPerPage = layoutSettings.cameraPerPage;

      console.debug(
        `Realtime subSlides generation: ${validMeasures.length} measures, ${layoutSettings.row}x${layoutSettings.column} layout, ${pageSize} items per page, ${cameraPerPage} cameras per page`,
      );

      // Calculate number of subSlides based on measures (this determines the total number of subSlides)
      const totalSubSlides = Math.ceil(validMeasures.length / pageSize);

      // Prepare cameras for distribution if they exist
      const validCameras =
        cameras && cameras.length > 0 ? cameras.filter((camera) => camera && camera.trim() !== '') : [];

      // If all measures fit in one page, return single subSlide with all measures
      if (validMeasures.length <= pageSize) {
        const hasCamera = validCameras.length > 0;
        const layoutComplexity = layout.template === 'table' ? 'complex' : 'medium';
        const optimalDuration = getOptimalRealtimeDuration(validMeasures.length, hasCamera, layoutComplexity);

        // For single subSlide, include cameras up to cameraPerPage limit
        const subSlideCameras = validCameras.slice(0, cameraPerPage);

        const content: any = {
          type: 'realtime' as ContentType,
          measure: validMeasures, // All measures in single subSlide
          station,
          stationType,
          layout,
          cameras: subSlideCameras,
        };

        // Only include showOptions if it's defined
        if (showOptions !== undefined) {
          content.showOptions = showOptions;
        }

        return [
          {
            id: `realtime-${station}-page-0`,
            content,
            show: true,
            duration: optimalDuration,
          },
        ];
      }

      // Create paginated subSlides based on layout configuration
      const subSlides: ISubSlide[] = [];
      for (let i = 0; i < validMeasures.length; i += pageSize) {
        const pageIndex = Math.floor(i / pageSize);
        const pageMeasures = validMeasures.slice(i, i + pageSize);

        // Distribute cameras across subSlides based on cameraPerPage
        // Each subSlide gets its portion of cameras based on the page index and cameraPerPage setting
        const cameraStartIndex = pageIndex * cameraPerPage;
        const cameraEndIndex = cameraStartIndex + cameraPerPage;
        const subSlideCameras = validCameras.slice(cameraStartIndex, cameraEndIndex);

        const hasCamera = subSlideCameras.length > 0;
        const layoutComplexity = layout.template === 'table' ? 'complex' : 'medium';
        const optimalDuration = getOptimalRealtimeDuration(pageMeasures.length, hasCamera, layoutComplexity);

        const content: any = {
          type: 'realtime' as ContentType,
          measure: pageMeasures, // Measures for this page
          station,
          stationType,
          layout,
          cameras: subSlideCameras, // Cameras distributed for this subSlide
        };

        // Only include showOptions if it's defined
        if (showOptions !== undefined) {
          content.showOptions = showOptions;
        }

        const subSlide: ISubSlide = {
          id: `realtime-${station}-page-${pageIndex}`,
          content,
          show: true,
          duration: optimalDuration,
        };

        // Validate generated subSlide
        validateWithError(!!subSlide.id, 'SubSlide must have an ID', { subSlide, pageIndex });
        validateWithError(!!subSlide.content, 'SubSlide must have content', { subSlide, pageIndex });

        subSlides.push(subSlide);
      }

      console.debug(`Generated ${subSlides.length} realtime subSlides from ${validMeasures.length} measures`);

      // Log camera distribution for debugging
      if (validCameras.length > 0) {
        console.debug(`Camera distribution: ${validCameras.length} total cameras, ${cameraPerPage} per page`);
        subSlides.forEach((subSlide, index) => {
          const subSlideContent = subSlide.content as any;
          console.debug(`SubSlide ${index}: ${subSlideContent.cameras?.length || 0} cameras assigned`);
        });
      }

      return subSlides;
    },
    [], // Fallback to empty array on error
    ErrorType.DATA_PROCESSING_ERROR,
    { metadata, hasMetadata: !!metadata },
  );
};

/**
 * Generate subSlides for histories content using buildPages function with real DataHistory data
 * This function processes historical data through the buildPages function to create paginated subSlides
 * @param metadata - History metadata containing measure, timeRange, and other configuration
 * @param intl - Internationalization instance for formatting
 * @param dataStations - Station data for processing
 * @param realData - Optional real DataHistory data (if not provided, buildPages will use mock data)
 * @returns Array of subSlides, one for each page generated by buildPages
 */
export const generateHistoriesSubSlides = (
  metadata: HistoryMetadata,
  intl: IntlShape,
  dataStations: IDataStation,
  realData?: DataHistory[],
): ISubSlide[] => {
  return safeExecuteSync(
    () => {
      // Validate inputs with detailed error messages
      validateWithError(!!metadata, 'History metadata is required', { metadata });
      validateWithError(!!intl, 'Internationalization instance is required', { hasIntl: !!intl });
      validateWithError(!!dataStations, 'Data stations are required', { hasDataStations: !!dataStations });

      // Validate dataStations structure
      if (dataStations) {
        validateWithError(
          !!dataStations.measuringList && Array.isArray(dataStations.measuringList),
          'Data stations must have a measuringList array',
          {
            hasDataStations: !!dataStations,
            hasMeasuringList: !!dataStations.measuringList,
            measuringListType: typeof dataStations.measuringList,
            measuringListLength: dataStations.measuringList?.length,
          },
        );

        console.debug('DataStations validation:', {
          measuringListCount: dataStations.measuringList?.length || 0,
          sampleMeasures: dataStations.measuringList?.slice(0, 3).map((m) => m.key) || [],
        });
      }

      const { measure, timeRange, typeTimeRange, layout, config, fontSize } = metadata;

      // Validate required configuration with comprehensive error handling
      const validation = validateHistoriesMetadata(metadata, intl, dataStations);
      if (!validation.isValid) {
        throw createError.validation('Histories subSlides generation failed validation', {
          errors: validation.errors,
          warnings: validation.warnings,
          metadata,
        });
      }

      // Get layout settings with error handling
      const layoutSettings = safeExecuteSync(
        () => getLayoutSettings(layout),
        { row: 7, column: 8 }, // Fallback settings
        ErrorType.CONFIGURATION_ERROR,
        { layout },
      );

      // Validate layout settings
      validateWithError(
        typeof layoutSettings.row === 'number' && layoutSettings.row > 0,
        'Layout row setting must be a positive number',
        { layoutSettings },
      );
      validateWithError(
        typeof layoutSettings.column === 'number' && layoutSettings.column > 0,
        'Layout column setting must be a positive number',
        { layoutSettings },
      );

      // Generate cache key for buildPages result with error handling
      const hasRealData = !!(realData && realData.length > 0);
      const realDataHash = safeExecuteSync(
        () => generateDataHash(realData),
        'fallback-hash',
        ErrorType.DATA_PROCESSING_ERROR,
        { hasRealData, realDataLength: realData?.length },
      );

      const cacheKey = safeExecuteSync(
        () => generateCacheKey(metadata, layoutSettings, hasRealData, realDataHash),
        `fallback-${Date.now()}`,
        ErrorType.CACHE_ERROR,
        { metadata, layoutSettings, hasRealData, realDataHash },
      );

      // Check cache first with enhanced tracking and error handling
      let cachedResult: BuildPagesCache | undefined;
      let pages: HistoryPage[];

      try {
        cachedResult = buildPagesCache.get(cacheKey);
      } catch (cacheError) {
        errorHandler.handleError(
          createError.cache('Failed to access cache', {
            cacheKey,
            error: cacheError,
          }),
        );
        cachedResult = undefined;
      }

      if (cachedResult && Date.now() - cachedResult.timestamp < CACHE_CONFIG.maxAge) {
        // Use cached result and update access tracking
        pages = cachedResult.result;
        cachedResult.accessCount++;
        cachedResult.lastAccessed = Date.now();
        performanceMetrics.cacheHits++;

        console.debug(
          `Using cached buildPages result for histories subSlides (${pages.length} pages, accessed ${cachedResult.accessCount} times)`,
        );
      } else {
        // Generate new result with performance tracking and error handling
        const generationStart = performance.now();
        performanceMetrics.cacheMisses++;
        performanceMetrics.totalGenerations++;

        // Prepare data content slide configuration for buildPages with validation
        const validMeasures = measure.filter((m) => m && m.trim() !== '' && m !== '---');
        validateWithError(validMeasures.length > 0, 'No valid measures found for histories generation', {
          originalMeasures: measure,
          validMeasures,
        });

        // Validate real data if provided
        if (hasRealData) {
          validateWithError(Array.isArray(realData), 'Real data must be an array', {
            realData,
            realDataType: typeof realData,
          });

          // Validate data structure of first item
          if (realData!.length > 0) {
            const firstItem = realData![0];
            validateApiData(firstItem, {
              date: 'string',
              measuringLogs: 'object',
            });
          }
        }

        // Check if measures exist in dataStations (warning only, not blocking)
        const availableMeasures = new Set(dataStations.measuringList.map((m) => m.key));
        const missingMeasures = validMeasures.filter((m) => !availableMeasures.has(m));
        if (missingMeasures.length > 0) {
          console.warn('Some measures not found in dataStations:', {
            missingMeasures,
            availableMeasures: Array.from(availableMeasures).slice(0, 10),
            station: metadata.station,
          });
        }

        const dataContentSlide: HistoryMetadata = {
          measure: validMeasures,
          timeRange,
          typeTimeRange,
          station: metadata.station,
          stationType: metadata.stationType,
          calc: 'average_day' as const,
          layout: metadata.layout,
          config,
          fontSize,
        };

        // Use buildPages function with comprehensive error handling
        pages = safeExecuteSync(
          () =>
            buildPages({
              row: layoutSettings.row || 7,
              column: layoutSettings.column || 8,
              dateFormat: DATE_FORMAT,
              intl,
              dataStations,
              decimalFormat: 2,
              dataContentSlide,
              realData, // Pass real DataHistory data if available
            }),
          [], // Fallback to empty array
          ErrorType.DATA_PROCESSING_ERROR,
          {
            layoutSettings,
            dataContentSlide,
            hasRealData,
            realDataLength: realData?.length,
          },
        );

        // Update performance metrics
        const generationTime = performance.now() - generationStart;
        performanceMetrics.averageGenerationTime =
          (performanceMetrics.averageGenerationTime * (performanceMetrics.totalGenerations - 1) + generationTime) /
          performanceMetrics.totalGenerations;

        // Validate buildPages result
        validateWithError(Array.isArray(pages), 'buildPages must return an array', {
          pages,
          pagesType: typeof pages,
        });

        if (pages.length === 0) {
          console.warn('buildPages returned no pages for histories subSlides generation', {
            station: metadata.station,
            timeRange,
            typeTimeRange,
            measureCount: validMeasures.length,
            layoutSettings,
            hasRealData,
            realDataLength: realData?.length,
          });
          return [];
        }

        // Log successful page generation
        console.debug(`buildPages successfully generated ${pages.length} pages for histories subSlides`, {
          station: metadata.station,
          pagesCount: pages.length,
          firstPageStructure: pages[0]
            ? {
                hasHeader: !!pages[0].header,
                hasBody: !!pages[0].body,
                headerLength: pages[0].header?.length,
                bodyLength: pages[0].body?.length,
              }
            : null,
        });

        // Cache the result with enhanced logic and error handling
        const memorySize = safeExecuteSync(
          () => estimateMemorySize(pages),
          1, // Fallback to 1MB estimate
          ErrorType.MEMORY_ERROR,
          { pagesLength: pages.length },
        );

        const now = Date.now();

        // Only cache if it doesn't exceed memory limits
        if (memorySize <= CACHE_CONFIG.maxMemoryMB * 0.3) {
          try {
            buildPagesCache.set(cacheKey, {
              key: cacheKey,
              result: pages,
              timestamp: now,
              memorySize,
              accessCount: 1,
              lastAccessed: now,
            });
            totalCacheMemoryMB += memorySize;

            // Update peak memory usage tracking
            if (totalCacheMemoryMB > performanceMetrics.memoryPeakUsage) {
              performanceMetrics.memoryPeakUsage = totalCacheMemoryMB;
            }

            // Trigger cleanup if needed
            if (buildPagesCache.size > CACHE_CONFIG.maxSize || totalCacheMemoryMB > CACHE_CONFIG.maxMemoryMB) {
              cleanupCache();
            }
          } catch (cacheError) {
            errorHandler.handleError(
              createError.cache('Failed to cache buildPages result', {
                cacheKey,
                memorySize,
                error: cacheError,
              }),
            );
          }
        } else {
          console.debug(`Skipping cache for large result: ${memorySize.toFixed(2)}MB`);
        }

        console.debug(`Generated ${pages.length} pages from buildPages for histories subSlides`);
      }

      // Convert each page to a subSlide with enhanced metadata and error handling
      return pages
        .map((page, index) => {
          return safeExecuteSync(
            () => {
              // Validate page structure
              validateWithError(!!page, 'Page data is required', { page, index });

              const pageComplexity = calculatePageComplexity(page);
              const optimalDuration = getOptimalHistoriesDuration(pageComplexity, measure.length);

              const subSlide: ISubSlide = {
                id: `histories-${metadata.station}-${timeRange}-${typeTimeRange}-page-${index}`,
                content: {
                  type: 'histories' as ContentType,
                  pageData: page,
                  pageIndex: index,
                  totalPages: pages.length,
                  metadata: {
                    measure: measure.filter((m) => m && m.trim() !== '' && m !== '---'),
                    timeRange,
                    typeTimeRange,
                    station: metadata.station,
                    stationType: metadata.stationType,
                    config,
                    layout,
                    fontSize,
                  },
                  // Additional context for rendering
                  renderingContext: {
                    hasRealData,
                    dataSource: hasRealData ? 'real' : 'mock',
                    generatedAt: new Date().toISOString(),
                    fromCache: !!cachedResult,
                  },
                },
                show: true,
                duration: optimalDuration,
              };

              // Validate generated subSlide
              validateWithError(!!subSlide.id, 'SubSlide must have an ID', { subSlide, index });
              validateWithError(!!subSlide.content, 'SubSlide must have content', { subSlide, index });

              return subSlide;
            },
            null, // Will be filtered out
            ErrorType.DATA_PROCESSING_ERROR,
            { page, index, station: metadata.station },
          );
        })
        .filter((subSlide): subSlide is ISubSlide => subSlide !== null); // Filter out failed generations
    },
    [], // Fallback to empty array on error
    ErrorType.DATA_PROCESSING_ERROR,
    { metadata, hasIntl: !!intl, hasDataStations: !!dataStations },
  );
};

/**
 * Utility function to check if a content type supports subSlides
 * @param contentType - The content type to check
 * @returns true if the content type supports subSlides, false otherwise
 */
export const supportsSubSlides = (contentType: ContentType): boolean => {
  return contentType === 'realtime' || contentType === 'histories';
};

/**
 * Utility function to validate realtime metadata for subSlides generation
 * @param metadata - RealTime metadata to validate
 * @returns Validation result with details
 */
export const validateRealtimeMetadata = (
  metadata: RealTimeMetadata,
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  validMeasuresCount: number;
} => {
  const errors: string[] = [];
  const warnings: string[] = [];
  let validMeasuresCount = 0;

  if (!metadata) {
    errors.push('Metadata is null or undefined');
    return { isValid: false, errors, warnings, validMeasuresCount };
  }

  if (!metadata.measure || !Array.isArray(metadata.measure)) {
    errors.push('Measure array is missing or invalid');
  } else {
    const validMeasures = metadata.measure.filter((m) => m && typeof m === 'string' && m.trim() !== '' && m !== '---');
    validMeasuresCount = validMeasures.length;

    if (validMeasuresCount === 0) {
      warnings.push('No valid measures found after filtering');
    }
  }

  if (!metadata.layout) {
    errors.push('Layout configuration is missing');
  } else {
    if (!metadata.layout.template) {
      warnings.push('Layout template is missing');
    }
    if (!metadata.layout.config) {
      warnings.push('Layout config is missing');
    }
  }

  if (!metadata.station) {
    errors.push('Station is missing');
  }

  if (!metadata.stationType) {
    errors.push('Station type is missing');
  }

  if (!metadata.showOptions) {
    warnings.push('Show options are missing');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    validMeasuresCount,
  };
};

/**
 * Utility function to get optimal duration for realtime subSlides based on content complexity
 * @param measureCount - Number of measures in the subSlide
 * @param hasCamera - Whether the subSlide includes camera data
 * @param layoutComplexity - Complexity of the layout (simple, medium, complex)
 * @returns Optimal duration in milliseconds
 */
export const getOptimalRealtimeDuration = (
  measureCount: number = 1,
  hasCamera: boolean = false,
  layoutComplexity: 'simple' | 'medium' | 'complex' = 'medium',
): number => {
  let baseDuration = 4000; // 4 seconds base

  // Adjust for measure count (though realtime subSlides typically have 1 measure)
  baseDuration += (measureCount - 1) * 1000;

  // Adjust for camera presence
  if (hasCamera) {
    baseDuration += 2000; // Extra 2 seconds for camera data
  }

  // Adjust for layout complexity
  switch (layoutComplexity) {
    case 'simple':
      baseDuration *= 0.8; // 20% faster
      break;
    case 'complex':
      baseDuration *= 1.3; // 30% slower
      break;
    default:
      // medium - no adjustment
      break;
  }

  // Ensure minimum and maximum bounds
  return Math.max(3000, Math.min(baseDuration, 10000));
};

/**
 * Utility function to validate histories metadata for subSlides generation
 * @param metadata - History metadata to validate
 * @param intl - Internationalization instance
 * @param dataStations - Station data
 * @returns Validation result with details
 */
export const validateHistoriesMetadata = (
  metadata: HistoryMetadata,
  intl?: IntlShape,
  dataStations?: IDataStation,
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!metadata) {
    errors.push('Metadata is null or undefined');
    return { isValid: false, errors, warnings };
  }

  // Check required fields
  if (!metadata.measure || !Array.isArray(metadata.measure) || metadata.measure.length === 0) {
    errors.push('Measure array is missing or empty');
  } else {
    const validMeasures = metadata.measure.filter((m) => m && m.trim() !== '' && m !== '---');
    if (validMeasures.length === 0) {
      errors.push('No valid measures found after filtering');
    }
  }

  if (!metadata.timeRange) {
    errors.push('Time range is missing');
  }

  if (!metadata.typeTimeRange) {
    errors.push('Type time range is missing');
  }

  if (!metadata.station) {
    errors.push('Station is missing');
  }

  if (!metadata.stationType) {
    errors.push('Station type is missing');
  }

  if (!metadata.layout) {
    errors.push('Layout configuration is missing');
  } else {
    if (!metadata.layout.view) {
      warnings.push('Layout view is missing');
    }
    if (!metadata.layout.settings) {
      warnings.push('Layout settings are missing');
    }
  }

  if (!metadata.config) {
    warnings.push('Config is missing');
  }

  if (!intl) {
    errors.push('Internationalization instance is required for histories generation');
  }

  if (!dataStations) {
    errors.push('Data stations are required for histories generation');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Utility function to get layout settings from complex layout structure
 * @param layout - Layout configuration
 * @returns Layout settings object
 */
export const getLayoutSettings = (layout: HistoryMetadata['layout']) => {
  if ('row' in layout.settings) {
    // Simple layout structure
    return layout.settings;
  } else {
    // Complex layout structure - use desktop as default
    return layout.settings.desktop || layout.settings.mobile || layout.settings.tablet || { row: 7, column: 8 };
  }
};

/**
 * Extract layout settings (row/column) from RealTimeMetadata layout configuration
 * @param layout - RealTime layout configuration
 * @returns Layout settings with row and column values
 */
export const getRealtimeLayoutSettings = (
  layout: RealTimeMetadata['layout'],
): { row: number; column: number; cameraPerPage: number } => {
  if (!layout || !layout.config || !layout.config.settings) {
    return { row: 3, column: 3, cameraPerPage: 3 }; // Default fallback for realtime
  }

  const settings = layout.config.settings;

  if ('row' in settings) {
    // Simple layout structure
    return {
      row: settings.row,
      column: settings.column,
      cameraPerPage: settings.cameraPerPage || 3,
    };
  } else {
    // Complex layout structure - use desktop as default, then mobile, then tablet
    const selectedSettings = settings.desktop ||
      settings.mobile ||
      settings.tablet || { row: 3, column: 3, cameraPerPage: 3 };
    return {
      row: selectedSettings.row,
      column: selectedSettings.column,
      cameraPerPage: selectedSettings.cameraPerPage || 3,
    };
  }
};

/**
 * Utility function to calculate page complexity for optimal duration
 * @param page - History page data
 * @returns Complexity score (simple, medium, complex)
 */
export const calculatePageComplexity = (page: HistoryPage): 'simple' | 'medium' | 'complex' => {
  if (!page || !page.body) {
    return 'simple';
  }

  const dataPointsCount = page.body.length;
  const headerComplexity = page.header ? page.header.length : 0;

  // Calculate complexity based on data points and header complexity
  const totalComplexity = dataPointsCount + headerComplexity;

  if (totalComplexity <= 10) {
    return 'simple';
  } else if (totalComplexity <= 30) {
    return 'medium';
  } else {
    return 'complex';
  }
};

/**
 * Utility function to get optimal duration for histories subSlides based on content complexity
 * @param complexity - Page complexity level
 * @param measureCount - Number of measures in the page
 * @returns Optimal duration in milliseconds
 */
export const getOptimalHistoriesDuration = (
  complexity: 'simple' | 'medium' | 'complex',
  measureCount: number = 1,
): number => {
  let baseDuration = 6000; // 6 seconds base for histories

  // Adjust for complexity
  switch (complexity) {
    case 'simple':
      baseDuration = 5000;
      break;
    case 'medium':
      baseDuration = 7000;
      break;
    case 'complex':
      baseDuration = 10000;
      break;
  }

  // Adjust for measure count
  baseDuration += Math.max(0, (measureCount - 3) * 500); // Add 500ms for each measure beyond 3

  // Ensure reasonable bounds
  return Math.max(4000, Math.min(baseDuration, 15000));
};

/**
 * Enhanced lazy loading generator for subSlides with preloading and memory management
 * This is useful for large presentations where not all subSlides need to be generated upfront
 */
export class LazySubSlidesGenerator {
  private slide: SlideConfigDto;
  private intl?: IntlShape;
  private dataStations?: IDataStation;
  private realData?: DataHistory[];
  private generatedSubSlides: Map<number, ISubSlide> = new Map();
  private totalCount: number = 0;
  private preloadQueue: Set<number> = new Set();
  private isPreloading: boolean = false;
  private memoryUsage: number = 0;
  private maxMemoryMB: number = 50; // 50MB limit for lazy generator

  constructor(slide: SlideConfigDto, intl?: IntlShape, dataStations?: IDataStation, realData?: DataHistory[]) {
    this.slide = slide;
    this.intl = intl;
    this.dataStations = dataStations;
    this.realData = realData;
    this.totalCount = this.calculateTotalCount();
  }

  private calculateTotalCount(): number {
    const { content } = this.slide;

    if (!content.show || !content.type || !content.metadata) {
      return 0;
    }

    switch (content.type) {
      case 'realtime': {
        const metadata = content.metadata as RealTimeMetadata;
        const validMeasures = metadata.measure?.filter((m) => m && m !== '---') || [];
        return validMeasures.length;
      }

      case 'histories': {
        if (!this.intl || !this.dataStations) {
          return 0;
        }
        // For histories, we need to check cache or estimate
        const metadata = content.metadata as HistoryMetadata;
        const layoutSettings = getLayoutSettings(metadata.layout);
        const hasRealData = !!(this.realData && this.realData.length > 0);
        const realDataHash = generateDataHash(this.realData);
        const cacheKey = generateCacheKey(metadata, layoutSettings, hasRealData, realDataHash);

        const cachedResult = buildPagesCache.get(cacheKey);
        if (cachedResult) {
          return cachedResult.result.length;
        }

        // Fallback: generate to get count (this will be cached)
        const subSlides = generateHistoriesSubSlides(metadata, this.intl, this.dataStations, this.realData);
        return subSlides.length;
      }

      default:
        return 0;
    }
  }

  /**
   * Get total count of subSlides without generating them all
   */
  getTotalCount(): number {
    return this.totalCount;
  }

  /**
   * Get a specific subSlide by index, generating it if not already generated
   * Includes intelligent preloading of nearby subSlides
   */
  getSubSlide(index: number): ISubSlide | null {
    if (index < 0 || index >= this.totalCount) {
      return null;
    }

    // Return cached subSlide if already generated
    if (this.generatedSubSlides.has(index)) {
      // Trigger preloading of nearby subSlides
      this.schedulePreload(index);
      return this.generatedSubSlides.get(index)!;
    }

    // Generate the specific subSlide
    const allSubSlides = this.generateAllSubSlides();
    if (index < allSubSlides.length) {
      const subSlide = allSubSlides[index];
      this.generatedSubSlides.set(index, subSlide);

      // Update memory usage tracking
      this.memoryUsage += this.estimateSubSlideMemory(subSlide);

      // Trigger memory cleanup if needed
      if (this.memoryUsage > this.maxMemoryMB) {
        this.cleanupMemory();
      }

      // Schedule preloading
      this.schedulePreload(index);

      return subSlide;
    }

    return null;
  }

  /**
   * Schedule preloading of nearby subSlides
   */
  private schedulePreload(currentIndex: number): void {
    if (this.isPreloading) return;

    // Add nearby indices to preload queue
    for (let i = 1; i <= CACHE_CONFIG.preloadThreshold; i++) {
      const nextIndex = currentIndex + i;
      const prevIndex = currentIndex - i;

      if (nextIndex < this.totalCount && !this.generatedSubSlides.has(nextIndex)) {
        this.preloadQueue.add(nextIndex);
      }

      if (prevIndex >= 0 && !this.generatedSubSlides.has(prevIndex)) {
        this.preloadQueue.add(prevIndex);
      }
    }

    // Start preloading if queue is not empty
    if (this.preloadQueue.size > 0) {
      this.startPreloading();
    }
  }

  /**
   * Start background preloading of queued subSlides
   */
  private startPreloading(): void {
    if (this.isPreloading || this.preloadQueue.size === 0) return;

    this.isPreloading = true;

    // Use requestIdleCallback for non-blocking preloading
    const preloadNext = () => {
      if (this.preloadQueue.size === 0) {
        this.isPreloading = false;
        return;
      }

      const index = this.preloadQueue.values().next().value;
      this.preloadQueue.delete(index!);

      // Check memory limits before preloading
      if (this.memoryUsage < this.maxMemoryMB * 0.8) {
        this.getSubSlide(index!); // This will generate and cache the subSlide
      }

      // Continue preloading
      if (typeof requestIdleCallback !== 'undefined') {
        requestIdleCallback(preloadNext, { timeout: 100 });
      } else {
        // Fallback for environments without requestIdleCallback
        setTimeout(preloadNext, 10);
      }
    };

    if (typeof requestIdleCallback !== 'undefined') {
      requestIdleCallback(preloadNext, { timeout: 100 });
    } else {
      setTimeout(preloadNext, 10);
    }
  }

  /**
   * Estimate memory usage of a subSlide
   */
  private estimateSubSlideMemory(subSlide: ISubSlide): number {
    try {
      const jsonString = JSON.stringify(subSlide);
      return (jsonString.length * 2) / (1024 * 1024); // Convert to MB
    } catch {
      return 0.1; // Fallback estimate
    }
  }

  /**
   * Clean up memory by removing least recently used subSlides
   */
  private cleanupMemory(): void {
    const targetMemory = this.maxMemoryMB * 0.7; // Clean up to 70% of limit

    // Sort by access order (would need to track access times)
    const entries = Array.from(this.generatedSubSlides.entries());

    // Remove oldest entries until under memory limit
    while (this.memoryUsage > targetMemory && entries.length > 0) {
      const [index, subSlide] = entries.shift()!;
      this.memoryUsage -= this.estimateSubSlideMemory(subSlide);
      this.generatedSubSlides.delete(index);
    }
  }

  /**
   * Get a range of subSlides, generating only what's needed
   */
  getSubSlidesRange(start: number, end: number): ISubSlide[] {
    const result: ISubSlide[] = [];

    for (let i = start; i <= end && i < this.totalCount; i++) {
      const subSlide = this.getSubSlide(i);
      if (subSlide) {
        result.push(subSlide);
      }
    }

    return result;
  }

  /**
   * Generate all subSlides (fallback for when lazy loading isn't beneficial)
   */
  private generateAllSubSlides(): ISubSlide[] {
    return generateSubSlides(this.slide, this.intl, this.dataStations, this.realData);
  }

  /**
   * Clear generated cache to free memory
   */
  clearCache(): void {
    this.generatedSubSlides.clear();
  }
}

/**
 * Enhanced performance monitoring utilities with comprehensive metrics
 */
export const performanceUtils = {
  /**
   * Measure execution time of subSlides generation
   */
  measureGenerationTime: async <T>(
    operation: () => T | Promise<T>,
    operationName: string = 'subSlides generation',
  ): Promise<{ result: T; duration: number; memoryUsed?: number }> => {
    const startTime = performance.now();
    const startMemory = (performance as any).memory?.usedJSHeapSize || 0;

    const result = await operation();

    const endTime = performance.now();
    const endMemory = (performance as any).memory?.usedJSHeapSize || 0;
    const duration = endTime - startTime;
    const memoryUsed = endMemory - startMemory;

    console.debug(`${operationName} took ${duration.toFixed(2)}ms, memory: ${(memoryUsed / 1024 / 1024).toFixed(2)}MB`);

    return { result, duration, memoryUsed };
  },

  /**
   * Measure synchronous execution time of subSlides generation
   */
  measureGenerationTimeSync: <T>(
    operation: () => T,
    operationName: string = 'subSlides generation (sync)',
  ): { result: T; duration: number; memoryUsed?: number } => {
    const startTime = performance.now();
    const startMemory = (performance as any).memory?.usedJSHeapSize || 0;

    const result = operation();

    const endTime = performance.now();
    const endMemory = (performance as any).memory?.usedJSHeapSize || 0;
    const duration = endTime - startTime;
    const memoryUsed = endMemory - startMemory;

    console.debug(`${operationName} took ${duration.toFixed(2)}ms, memory: ${(memoryUsed / 1024 / 1024).toFixed(2)}MB`);

    return { result, duration, memoryUsed };
  },

  /**
   * Get comprehensive cache statistics
   */
  getCacheStats: () => {
    const hitRate =
      performanceMetrics.cacheHits + performanceMetrics.cacheMisses > 0
        ? (performanceMetrics.cacheHits / (performanceMetrics.cacheHits + performanceMetrics.cacheMisses)) * 100
        : 0;

    return {
      size: buildPagesCache.size,
      memoryUsageMB: totalCacheMemoryMB,
      maxSize: CACHE_CONFIG.maxSize,
      maxMemoryMB: CACHE_CONFIG.maxMemoryMB,
      hitRate: hitRate.toFixed(2) + '%',
      totalHits: performanceMetrics.cacheHits,
      totalMisses: performanceMetrics.cacheMisses,
      totalGenerations: performanceMetrics.totalGenerations,
      averageGenerationTime: performanceMetrics.averageGenerationTime.toFixed(2) + 'ms',
      memoryPeakUsage: performanceMetrics.memoryPeakUsage.toFixed(2) + 'MB',
    };
  },

  /**
   * Clear all caches and reset metrics
   */
  clearAllCaches: () => {
    buildPagesCache.clear();
    totalCacheMemoryMB = 0;
    subSlidePool.clear();

    // Reset performance metrics
    performanceMetrics.cacheHits = 0;
    performanceMetrics.cacheMisses = 0;
    performanceMetrics.totalGenerations = 0;
    performanceMetrics.averageGenerationTime = 0;
    performanceMetrics.memoryPeakUsage = 0;

    console.debug('All subSlides generation caches and metrics cleared');
  },

  /**
   * Get memory usage estimate for a slide's subSlides with enhanced accuracy
   */
  estimateSlideMemoryUsage: (slide: SlideConfigDto): number => {
    const { content } = slide;

    if (!content.show || !content.type || !content.metadata) {
      return 0;
    }

    switch (content.type) {
      case 'realtime': {
        const metadata = content.metadata as RealTimeMetadata;
        const validMeasures = metadata.measure?.filter((m) => m && m !== '---') || [];
        const hasCamera = metadata.cameras && metadata.cameras.length > 0;
        const layoutComplexity = metadata.layout?.template === 'table' ? 1.5 : 1.0;

        // More accurate estimation based on content complexity
        const baseSize = 0.05; // 0.05MB per realtime subSlide
        const cameraMultiplier = hasCamera ? 1.3 : 1.0;

        return validMeasures.length * baseSize * layoutComplexity * cameraMultiplier;
      }

      case 'histories': {
        const metadata = content.metadata as HistoryMetadata;
        const measureCount = metadata.measure?.length || 0;
        const layoutSettings = getLayoutSettings(metadata.layout);
        const pageSize = (layoutSettings.row || 7) * (layoutSettings.column || 8);

        // Estimate based on page complexity and data density
        const baseSize = 0.2; // 0.2MB per histories subSlide
        const complexityMultiplier = pageSize > 50 ? 1.5 : 1.0;

        return measureCount * baseSize * complexityMultiplier;
      }

      default:
        return 0;
    }
  },

  /**
   * Profile memory usage across multiple slides
   */
  profileSlidesMemoryUsage: (
    slides: SlideConfigDto[],
  ): {
    totalEstimatedMB: number;
    slideBreakdown: Array<{ slideId: string; estimatedMB: number; contentType: string }>;
    recommendations: string[];
  } => {
    let totalEstimatedMB = 0;
    const slideBreakdown: Array<{ slideId: string; estimatedMB: number; contentType: string }> = [];
    const recommendations: string[] = [];

    slides.forEach((slide) => {
      const estimatedMB = performanceUtils.estimateSlideMemoryUsage(slide);
      totalEstimatedMB += estimatedMB;

      slideBreakdown.push({
        slideId: slide.id || 'unknown',
        estimatedMB,
        contentType: slide.content.type || 'unknown',
      });

      // Generate recommendations
      if (estimatedMB > 10) {
        recommendations.push(
          `Slide ${slide.id} has high memory usage (${estimatedMB.toFixed(2)}MB) - consider reducing data complexity`,
        );
      }
    });

    if (totalEstimatedMB > 100) {
      recommendations.push(
        'Total presentation memory usage is high - consider using lazy loading or reducing slide count',
      );
    }

    if (slideBreakdown.filter((s) => s.contentType === 'histories').length > 5) {
      recommendations.push('Many histories slides detected - consider caching optimization');
    }

    return {
      totalEstimatedMB,
      slideBreakdown,
      recommendations,
    };
  },

  /**
   * Optimize cache configuration based on usage patterns
   */
  optimizeCacheConfiguration: () => {
    const stats = performanceUtils.getCacheStats();
    const hitRate = parseFloat(stats.hitRate.replace('%', ''));

    // Adjust cache size based on hit rate
    if (hitRate < 50 && CACHE_CONFIG.maxSize < 200) {
      CACHE_CONFIG.maxSize = Math.min(200, CACHE_CONFIG.maxSize * 1.5);
      console.debug(`Increased cache size to ${CACHE_CONFIG.maxSize} due to low hit rate`);
    } else if (hitRate > 90 && CACHE_CONFIG.maxSize > 50) {
      CACHE_CONFIG.maxSize = Math.max(50, CACHE_CONFIG.maxSize * 0.8);
      console.debug(`Decreased cache size to ${CACHE_CONFIG.maxSize} due to high hit rate`);
    }

    // Adjust cleanup interval based on memory pressure
    if (totalCacheMemoryMB > CACHE_CONFIG.maxMemoryMB * 0.8) {
      CACHE_CONFIG.cleanupInterval = Math.max(15000, CACHE_CONFIG.cleanupInterval * 0.8);
      console.debug(`Increased cleanup frequency to ${CACHE_CONFIG.cleanupInterval}ms due to memory pressure`);
    }
  },
};

/**
 * Utility function to get the expected number of subSlides for a given slide
 * This is useful for UI progress indicators
 * @param slide - The slide configuration DTO
 * @param intl - Internationalization instance for formatting
 * @param dataStations - Station data for histories processing
 * @param realData - Optional real DataHistory data for histories type
 * @returns Expected number of subSlides
 */
export const getExpectedSubSlidesCount = (
  slide: SlideConfigDto,
  intl?: IntlShape,
  dataStations?: IDataStation,
  realData?: DataHistory[],
): number => {
  const { content } = slide;

  if (!content.show || !content.type || !content.metadata) {
    return 0;
  }

  switch (content.type) {
    case 'realtime': {
      const metadata = content.metadata as RealTimeMetadata;
      const validMeasures = metadata.measure?.filter((m) => m && m !== '---') || [];
      return validMeasures.length;
    }

    case 'histories': {
      if (!intl || !dataStations) {
        return 0;
      }

      // Try to get count from cache first
      const metadata = content.metadata as HistoryMetadata;
      const layoutSettings = getLayoutSettings(metadata.layout);
      const hasRealData = !!(realData && realData.length > 0);
      const realDataHash = generateDataHash(realData);
      const cacheKey = generateCacheKey(metadata, layoutSettings, hasRealData, realDataHash);

      const cachedResult = buildPagesCache.get(cacheKey);
      if (cachedResult && Date.now() - cachedResult.timestamp < CACHE_CONFIG.maxAge) {
        return cachedResult.result.length;
      }

      // Fallback: generate to get count (this will be cached for future use)
      const subSlides = generateHistoriesSubSlides(metadata, intl, dataStations, realData);
      return subSlides.length;
    }

    default:
      return 0;
  }
};
