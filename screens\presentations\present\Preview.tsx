import { useAppStores } from '@/stores/appStores';
import { PlayIcon } from '@phosphor-icons/react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button, toast } from 'ui-components';
import ContainerPresent from './ContainerPresent';
import { ISlide } from '@/types/slide';
import { useParams } from 'next/navigation';
import { useSlideConfigStores } from '@/stores';

type Props = {
  presentations: ISlide[];
};

export default function Preview({ presentations }: Props) {
  const { led_key } = useParams() as { led_key: string };
  const [isOpenPreview, setIsOpenPreview] = useState(false);
  const setIsPreviewing = useAppStores((store) => store.setIsPreviewing);
  const setConfig = useSlideConfigStores((store) => store.setConfig);
  const presentContainerRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement) {
        setIsOpenPreview(false);
        setIsPreviewing(false);
      }
    };

    if (isOpenPreview && presentContainerRef.current) {
      setIsPreviewing(true);
      presentContainerRef.current.requestFullscreen().catch((err) => {
        console.error(`Lỗi khi bật chế độ toàn màn hình: ${err.message} (${err.name})`);
        // Fallback to just showing the component if fullscreen fails
      });
    }
    const strLocal = localStorage.getItem(`config-${led_key}`) ?? '';
    const config = (strLocal?.length ? JSON.parse(strLocal) : { height: 1080, width: 1920 }) as {
      height: number;
      width: number;
    };

    setConfig({
      height: isOpenPreview
        ? window.document.fullscreenElement
          ? +window.document.fullscreenElement.clientHeight
          : +window.document.body.clientHeight
        : +config.height,
      width: isOpenPreview
        ? window.document.fullscreenElement
          ? +window.document.fullscreenElement.clientWidth
          : +window.document.body.clientWidth
        : +config.width,
    });

    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [isOpenPreview, led_key]);

  const handlerOpenPreview = useCallback(() => {
    const isNotPreview = presentations.every((slide) => !slide.show);
    if (isNotPreview) {
      return toast({
        type: 'error',
        title: 'Không thể chuyển sang chế độ trình chiếu. Vui lòng bật hiển thị ít nhất 1 trang.',
        options: {
          position: 'top-center',
        },
      });
    }

    return setIsOpenPreview(true);
  }, [presentations]);

  const handleClosePreview = useCallback(() => {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    }
    setIsOpenPreview(false);
    setIsPreviewing(false);
  }, [setIsPreviewing]);
  return (
    <>
      <Button type="button" icon variant="gray" onClick={handlerOpenPreview}>
        <PlayIcon size={20} weight="regular" className="text-current flex-shrink-0" />
      </Button>
      {isOpenPreview && (
        <ContainerPresent ref={presentContainerRef} presentations={presentations} onClose={handleClosePreview} />
      )}
    </>
  );
}
