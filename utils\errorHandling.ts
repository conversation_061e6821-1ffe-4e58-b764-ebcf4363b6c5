/**
 * Comprehensive error handling utilities for subSlides generation and presentation
 */

// Error types for different scenarios
export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DATA_PROCESSING_ERROR = 'DATA_PROCESSING_ERROR',
  CACHE_ERROR = 'CACHE_ERROR',
  MEMORY_ERROR = 'MEMORY_ERROR',
  API_ERROR = 'API_ERROR',
  RENDERING_ERROR = 'RENDERING_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

// Custom error class for subSlides operations
export class SubSlidesError extends Error {
  public readonly type: ErrorType;
  public readonly severity: ErrorSeverity;
  public readonly context: Record<string, any>;
  public readonly timestamp: Date;
  public readonly recoverable: boolean;

  constructor(
    message: string,
    type: ErrorType,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context: Record<string, any> = {},
    recoverable: boolean = true,
  ) {
    super(message);
    this.name = 'SubSlidesError';
    this.type = type;
    this.severity = severity;
    this.context = context;
    this.timestamp = new Date();
    this.recoverable = recoverable;

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, SubSlidesError);
    }
  }
}

// Error logging interface
interface ErrorLog {
  id: string;
  error: SubSlidesError;
  resolved: boolean;
  resolvedAt?: Date;
  resolution?: string;
}

// Global error tracking
const errorLogs: Map<string, ErrorLog> = new Map();
let errorCounter = 0;

/**
 * Error handler with logging and recovery mechanisms
 */
export class ErrorHandler {
  private static instance: ErrorHandler;
  private maxLogSize = 100;
  private logToConsole = true;
  private logToStorage = false;

  private constructor() {}
  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle an error with appropriate logging and recovery
   */
  public handleError(error: SubSlidesError): void {
    const errorId = `error_${++errorCounter}_${Date.now()}`;

    const errorLog: ErrorLog = {
      id: errorId,
      error,
      resolved: false,
    };

    // Add to error logs
    errorLogs.set(errorId, errorLog);

    // Clean up old logs if needed
    if (errorLogs.size > this.maxLogSize) {
      const oldestKey = errorLogs.keys().next().value;
      errorLogs.delete(oldestKey!);
    }

    // Log to console based on severity
    if (this.logToConsole) {
      this.logErrorToConsole(error);
    }

    // Log to storage if enabled
    if (this.logToStorage) {
      this.logErrorToStorage(errorLog);
    }

    // Trigger recovery if error is recoverable
    if (error.recoverable) {
      this.attemptRecovery(error);
    }
  }

  /**
   * Log error to console with appropriate level
   */
  private logErrorToConsole(error: SubSlidesError): void {
    const logMessage = `[${error.severity}] ${error.type}: ${error.message}`;
    const contextInfo = Object.keys(error.context).length > 0 ? error.context : '';

    switch (error.severity) {
      case ErrorSeverity.LOW:
        console.debug(logMessage, contextInfo);
        break;
      case ErrorSeverity.MEDIUM:
        console.warn(logMessage, contextInfo);
        break;
      case ErrorSeverity.HIGH:
        console.error(logMessage, contextInfo);
        break;
      case ErrorSeverity.CRITICAL:
        console.error('🚨 CRITICAL ERROR:', logMessage, contextInfo);
        break;
    }
  }

  /**
   * Log error to storage (localStorage or external service)
   */
  private logErrorToStorage(errorLog: ErrorLog): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const existingLogs = JSON.parse(localStorage.getItem('subSlides_error_logs') || '[]');
        existingLogs.push({
          id: errorLog.id,
          type: errorLog.error.type,
          severity: errorLog.error.severity,
          message: errorLog.error.message,
          context: errorLog.error.context,
          timestamp: errorLog.error.timestamp.toISOString(),
          recoverable: errorLog.error.recoverable,
        });

        // Keep only last 50 logs in storage
        if (existingLogs.length > 50) {
          existingLogs.splice(0, existingLogs.length - 50);
        }

        localStorage.setItem('subSlides_error_logs', JSON.stringify(existingLogs));
      }
    } catch (storageError) {
      console.warn('Failed to log error to storage:', storageError);
    }
  }

  /**
   * Attempt to recover from recoverable errors
   */
  private attemptRecovery(error: SubSlidesError): void {
    switch (error.type) {
      case ErrorType.CACHE_ERROR:
        this.recoverFromCacheError(error);
        break;
      case ErrorType.MEMORY_ERROR:
        this.recoverFromMemoryError(error);
        break;
      case ErrorType.DATA_PROCESSING_ERROR:
        this.recoverFromDataProcessingError(error);
        break;
      case ErrorType.API_ERROR:
        this.recoverFromApiError(error);
        break;
      default:
        console.debug(`No specific recovery mechanism for error type: ${error.type}`);
    }
  }

  /**
   * Recover from cache-related errors
   */
  private recoverFromCacheError(error: SubSlidesError): void {
    try {
      // Clear cache and retry
      if (typeof window !== 'undefined' && (window as any).performanceUtils) {
        (window as any).performanceUtils.clearAllCaches();
        console.debug('Cache cleared due to cache error, retrying operation');
      }
    } catch (recoveryError) {
      console.error('Failed to recover from cache error:', recoveryError);
    }
  }

  /**
   * Recover from memory-related errors
   */
  private recoverFromMemoryError(error: SubSlidesError): void {
    try {
      // Force garbage collection if available
      if (typeof window !== 'undefined' && (window as any).gc) {
        (window as any).gc();
      }

      // Clear caches to free memory
      if (typeof window !== 'undefined' && (window as any).performanceUtils) {
        (window as any).performanceUtils.clearAllCaches();
      }

      console.debug('Memory cleanup performed due to memory error');
    } catch (recoveryError) {
      console.error('Failed to recover from memory error:', recoveryError);
    }
  }

  /**
   * Recover from data processing errors
   */
  private recoverFromDataProcessingError(error: SubSlidesError): void {
    try {
      // Log the problematic data for debugging
      console.debug('Data processing error context:', error.context);

      // Could implement data sanitization or fallback data here
      console.debug('Consider implementing data sanitization for recovery');
    } catch (recoveryError) {
      console.error('Failed to recover from data processing error:', recoveryError);
    }
  }

  /**
   * Recover from API-related errors
   */
  private recoverFromApiError(error: SubSlidesError): void {
    try {
      // Could implement retry logic or fallback to cached data
      console.debug('API error occurred, consider implementing retry logic');
    } catch (recoveryError) {
      console.error('Failed to recover from API error:', recoveryError);
    }
  }

  /**
   * Get error statistics
   */
  public getErrorStats(): {
    totalErrors: number;
    errorsByType: Record<ErrorType, number>;
    errorsBySeverity: Record<ErrorSeverity, number>;
    recentErrors: ErrorLog[];
  } {
    const errorsByType: Record<ErrorType, number> = {} as Record<ErrorType, number>;
    const errorsBySeverity: Record<ErrorSeverity, number> = {} as Record<ErrorSeverity, number>;

    // Initialize counters
    Object.values(ErrorType).forEach((type) => {
      errorsByType[type] = 0;
    });
    Object.values(ErrorSeverity).forEach((severity) => {
      errorsBySeverity[severity] = 0;
    });

    // Count errors
    const recentErrors: ErrorLog[] = [];
    for (const errorLog of errorLogs.values()) {
      errorsByType[errorLog.error.type]++;
      errorsBySeverity[errorLog.error.severity]++;
      recentErrors.push(errorLog);
    }

    // Sort recent errors by timestamp (newest first)
    recentErrors.sort((a, b) => b.error.timestamp.getTime() - a.error.timestamp.getTime());

    return {
      totalErrors: errorLogs.size,
      errorsByType,
      errorsBySeverity,
      recentErrors: recentErrors.slice(0, 10), // Last 10 errors
    };
  }

  /**
   * Clear error logs
   */
  public clearErrorLogs(): void {
    errorLogs.clear();
    errorCounter = 0;

    // Clear storage logs too
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('subSlides_error_logs');
    }

    console.debug('Error logs cleared');
  }

  /**
   * Configure error handler
   */
  public configure(options: { maxLogSize?: number; logToConsole?: boolean; logToStorage?: boolean }): void {
    if (options.maxLogSize !== undefined) {
      this.maxLogSize = options.maxLogSize;
    }
    if (options.logToConsole !== undefined) {
      this.logToConsole = options.logToConsole;
    }
    if (options.logToStorage !== undefined) {
      this.logToStorage = options.logToStorage;
    }
  }
}

/**
 * Utility functions for creating specific error types
 */
export const createError = {
  validation: (message: string, context: Record<string, any> = {}) =>
    new SubSlidesError(message, ErrorType.VALIDATION_ERROR, ErrorSeverity.MEDIUM, context),

  dataProcessing: (message: string, context: Record<string, any> = {}) =>
    new SubSlidesError(message, ErrorType.DATA_PROCESSING_ERROR, ErrorSeverity.HIGH, context),

  cache: (message: string, context: Record<string, any> = {}) =>
    new SubSlidesError(message, ErrorType.CACHE_ERROR, ErrorSeverity.LOW, context),

  memory: (message: string, context: Record<string, any> = {}) =>
    new SubSlidesError(message, ErrorType.MEMORY_ERROR, ErrorSeverity.HIGH, context),

  api: (message: string, context: Record<string, any> = {}) =>
    new SubSlidesError(message, ErrorType.API_ERROR, ErrorSeverity.MEDIUM, context),

  rendering: (message: string, context: Record<string, any> = {}) =>
    new SubSlidesError(message, ErrorType.RENDERING_ERROR, ErrorSeverity.HIGH, context),

  configuration: (message: string, context: Record<string, any> = {}) =>
    new SubSlidesError(message, ErrorType.CONFIGURATION_ERROR, ErrorSeverity.MEDIUM, context),

  critical: (message: string, type: ErrorType, context: Record<string, any> = {}) =>
    new SubSlidesError(message, type, ErrorSeverity.CRITICAL, context, false),
};

/**
 * Safe execution wrapper that handles errors gracefully
 */
export const safeExecute = async <T>(
  operation: () => T | Promise<T>,
  fallback: T,
  errorType: ErrorType,
  context: Record<string, any> = {},
): Promise<T> => {
  try {
    return await operation();
  } catch (error) {
    const subSlidesError =
      error instanceof SubSlidesError
        ? error
        : new SubSlidesError(
            error instanceof Error ? error.message : 'Unknown error',
            errorType,
            ErrorSeverity.MEDIUM,
            { ...context, originalError: error },
          );

    ErrorHandler.getInstance().handleError(subSlidesError);
    return fallback;
  }
};

/**
 * Safe execution wrapper for synchronous operations
 */
export const safeExecuteSync = <T>(
  operation: () => T,
  fallback: T,
  errorType: ErrorType,
  context: Record<string, any> = {},
): T => {
  try {
    return operation();
  } catch (error) {
    const subSlidesError =
      error instanceof SubSlidesError
        ? error
        : new SubSlidesError(
            error instanceof Error ? error.message : 'Unknown error',
            errorType,
            ErrorSeverity.MEDIUM,
            { ...context, originalError: error },
          );

    ErrorHandler.getInstance().handleError(subSlidesError);
    return fallback;
  }
};

/**
 * Validation helper with error handling
 */
export const validateWithError = (condition: boolean, message: string, context: Record<string, any> = {}): void => {
  if (!condition) {
    throw createError.validation(message, context);
  }
};

/**
 * API data consistency checker
 */
export const validateApiData = (data: any, expectedStructure: Record<string, string>): boolean => {
  try {
    for (const [key, type] of Object.entries(expectedStructure)) {
      if (!(key in data)) {
        throw createError.api(`Missing required field: ${key}`, { data, expectedStructure });
      }

      if (typeof data[key] !== type && data[key] !== null && data[key] !== undefined) {
        throw createError.api(`Invalid type for field ${key}: expected ${type}, got ${typeof data[key]}`, {
          data,
          expectedStructure,
          actualValue: data[key],
        });
      }
    }
    return true;
  } catch (error) {
    if (error instanceof SubSlidesError) {
      ErrorHandler.getInstance().handleError(error);
    }
    return false;
  }
};

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();
