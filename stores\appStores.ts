import { create } from 'zustand';

type appStoreState = {
  toggle: boolean;
  isPreviewing: boolean;
};

type appStoreAction = {
  setToggle: (toggle: appStoreState['toggle']) => void;
  setIsPreviewing: (isPreviewing: appStoreState['isPreviewing']) => void;
};

const useAppStores = create<appStoreState & appStoreAction>((set) => ({
  toggle: true,
  isPreviewing: false,
  setToggle: (toggle: boolean) => set({ toggle }),
  setIsPreviewing: (isPreviewing: boolean) => set({ isPreviewing }),
}));

export { useAppStores };

