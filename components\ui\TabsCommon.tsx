import { cn } from '@/utils/tailwind';
import Link, { LinkProps } from 'next/link';
import { PropsWithChildren } from 'react';

type dataTag = {
  label: string | React.ReactNode;
  value: React.Key;
};

const ITag = ({
  isLink,
  children,
  ...props
}: PropsWithChildren &
  (LinkProps | React.HTMLAttributes<HTMLDivElement>) & {
    isLink?: boolean;
  }) => {
  if (isLink) {
    return (
      <Link {...(props as LinkProps)} prefetch={false}>
        {children}
      </Link>
    );
  }

  return <div {...(props as React.HTMLAttributes<HTMLDivElement>)}>{children}</div>;
};

const TagWithStateBorder = ({
  dataTag,
  actionTag,
  onSwitchTag,
  className,
  classNames,
  isLink,
  ...props
}: {
  dataTag: dataTag[];
  actionTag: React.Key;
  onSwitchTag?: (tag: React.Key) => void;
  className?: string;
  classNames?: {
    item: string;
  };
  isLink?: boolean;
}) => {
  return (
    <div className={cn('h-11 border-b justify-start items-start inline-flex', className)}>
      {dataTag.map((tag) => {
        const isActive = tag.value === actionTag;
        return (
          <ITag
            key={tag.value}
            className={cn(
              'h-full p-4 border-b-2 border-transparent justify-center items-center inline-flex cursor-pointer hover:bg-gray-100',
              { 'border-primary-500 hover:bg-white': isActive },
              classNames?.item,
            )}
            isLink={isLink}
            {...(isLink ? { href: tag.value as string } : { onClick: () => onSwitchTag?.(tag.value) })}
            {...props}
          >
            <div
              className={cn('text-center text-gray-700 text-sm font-semibold', {
                'text-primary-500': isActive,
              })}
            >
              {tag.label}
            </div>
          </ITag>
        );
      })}
    </div>
  );
};

const TagWithStateTransition = ({
  dataTag,
  actionTag,
  onSwitchTag,
  className,
  isLink,
  ...props
}: {
  dataTag: dataTag[];
  actionTag: React.Key;
  onSwitchTag?: (tag: React.Key) => void;
  className?: string;
  isLink?: boolean;
}) => {
  return (
    <div
      className={cn(
        'flex flex-row text-center rounded-xl bg-neutral-100 p-1 h-full w-max shadow-[0px_2px_6px_0px_rgba(0,0,0,0.10)_inset]',
        className,
      )}
    >
      {dataTag.map((tag) => {
        const isActive = tag.value === actionTag;
        return (
          <ITag
            key={tag.value}
            className={cn(
              'px-3 py-2 w-auto h-9 flex justify-center items-center',
              'text-sm text-gray-700 font-medium whitespace-nowrap block',
              'cursor-pointer',
              'transition-all',
              isActive && 'h-full w-full shadow-[0px_1px_4px_0px_rgba(0,0,0,0.20)] bg-white rounded-lg font-semibold',
            )}
            isLink={isLink}
            {...(isLink ? { href: tag.value as string } : { onClick: () => onSwitchTag?.(tag.value) })}
            {...props}
          >
            {tag.label}
          </ITag>
        );
      })}
    </div>
  );
};

const TabsCommon = {
  Border: TagWithStateBorder,
  Transition: TagWithStateTransition,
};

export { TabsCommon };
