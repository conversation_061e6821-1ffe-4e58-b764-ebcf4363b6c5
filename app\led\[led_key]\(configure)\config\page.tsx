'use client';

import { useCreateClient } from '@/api/useCreateClient';
import { InputPanelSize } from '@/components/ui/InputPanelSize';
import { useLedStores } from '@/stores';
import { cn } from '@/utils/tailwind';
import { FilesIcon, GlobeSimpleIcon, InfoIcon, LinkIcon, LockIcon } from '@phosphor-icons/react';
import Link from 'next/link';
import { use, useCallback, useEffect, useMemo, useState } from 'react';
import { defineMessages, FormattedMessage, useIntl } from 'react-intl';
import { Badge, Form, Switch, toast, Tooltip, TooltipContent, TooltipTrigger } from 'ui-components';

const messageDefined = defineMessages({
  messageCopySuccessURL: {
    defaultMessage: 'Đã sao chép URL chia sẻ bảng LED thành công.',
    id: 'app.led.[led_key].(configure).config.page.83529539',
  },
  messageCopySuccessEmbed: {
    defaultMessage: 'Đã sao chép mã nhúng bảng LED thành công.',
    id: 'app.led.[led_key].(configure).config.page.30093112',
  },
  messageCopySuccessPassword: {
    defaultMessage: 'Đã sao chép mật khẩu chia sẻ bảng LED thành công.',
    id: 'app.led.[led_key].(configure).config.page.268258777',
  },
  messageCopyTurnOffShare: {
    defaultMessage: 'Đã tắt chia sẻ bảng LED công khai thành công.',
    id: 'app.led.[led_key].(configure).config.page.1582789939',
  },
  messageCopyTurnOffShareFail: {
    defaultMessage: 'Không thể tắt chia sẻ bảng LED công khai. Vui lòng thử lại sau.',
    id: 'app.led.[led_key].(configure).config.page.637000790',
  },
  messageCopyTurnOnShare: {
    defaultMessage: 'Đã kích hoạt chia sẻ bảng LED công khai thành công.',
    id: 'app.led.[led_key].(configure).config.page.1327345319',
  },
  messageCopyTurnOnShareFail: {
    defaultMessage: 'Không thể kích hoạt chia sẻ bảng LED công khai. Vui lòng thử lại sau.',
    id: 'app.led.[led_key].(configure).config.page.1385638768',
  },
});

export default function LedConfig({ params }: { params: Promise<{ led_key: string }> }) {
  const [form] = Form.useForm();
  const intl = useIntl();
  const { led_key } = use(params) as { led_key: string };
  const { dataLedDetail, isPublic: isPublicLed } = useLedStores((store) => store);
  const { ledConfig } = useCreateClient();
  const { mutateAsync: mutateAsyncToggleShare, isPending: isPendingToggleShare } = ledConfig.toggleShareLedBoard({
    invalidateQueries: [
      {
        enable: true,
        queryKey: ['LedServiceClient', 'getLedConfig', led_key],
      },
    ],
  });

  const handleCopy = useCallback((value: string, message: string) => {
    navigator.clipboard
      .writeText(value)
      .then(() => {
        toast({
          type: 'success',
          title: message,
          options: {
            position: 'top-center',
          },
        });
      })
      .catch((err) => {
        console.log({ err });
        toast({
          type: 'error',
          title: 'Có lỗi xảy ra khi sao chép',
          options: {
            position: 'top-center',
          },
        });
      });
  }, []);

  const initFormValue = useMemo(() => {
    return {
      share: isPublicLed,
      password: dataLedDetail?.password ?? '',
      view_port: [800, 600],
    };
  }, [dataLedDetail, isPublicLed]);

  useEffect(() => {
    form.setFieldsValue(initFormValue);
  }, [initFormValue, form]);

  const [width, height] = (Form.useWatch('view_port', form) as [string, string]) || [800, 600];
  const share = Form.useWatch('share', form) as boolean;
  const str = useMemo(() => {
    const src = `${window.location.origin}/led/${led_key}`;
    return `<iframe width="${width ?? 800}" height="${
      height ?? 600
    }" src="${src}" title="bang LED" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>`;
  }, [led_key, width, height]);

  const onSubmit = async (values: { share: boolean }) => {
    const { share } = values;
    try {
      await mutateAsyncToggleShare(led_key);
      toast({
        type: 'success',
        title: share
          ? intl.formatMessage(messageDefined.messageCopyTurnOnShare)
          : intl.formatMessage(messageDefined.messageCopyTurnOffShare),
        options: {
          position: 'top-center',
        },
      });
    } catch (error) {
      toast({
        type: 'error',
        title: share
          ? intl.formatMessage(messageDefined.messageCopyTurnOnShareFail)
          : intl.formatMessage(messageDefined.messageCopyTurnOffShareFail),
        options: {
          position: 'top-center',
        },
      });
      form.setFieldsValue({ share: !share });
    }
  };

  return (
    <Form
      form={form}
      name="share-form"
      onFinish={onSubmit}
      initialValues={initFormValue}
      className="flex flex-col h-full items-center"
    >
      <div className="w-[600px] h-fit max-h-full rounded-3xl flex flex-col gap-4 bg-white">
        <div className="flex flex-row items-center justify-between flex-shrink-0 px-6 pt-6">
          <span className="font-semibold text-lg text-gray-700 flex-1 min-w-0">
            <FormattedMessage
              defaultMessage="Chia sẻ bảng LED"
              id="app.led.[led_key].(configure).config.page.729825786"
            />
          </span>
          {share ? (
            <Badge
              variant="success"
              className="flex flex-row gap-1 shadow-none items-center rounded-[30px] flex-shrink-0"
            >
              <GlobeSimpleIcon size={16} className="text-current flex-shrink-0" weight="regular" />
              <span className="text-current text-sm font-medium">
                <FormattedMessage
                  defaultMessage="Công khai"
                  id="app.led.[led_key].(configure).config.page.1026397723"
                />
              </span>
            </Badge>
          ) : (
            <Badge
              variant="warning"
              className="flex flex-row gap-1 shadow-none items-center rounded-[30px] flex-shrink-0"
            >
              <LockIcon size={16} className="text-current flex-shrink-0" weight="regular" />
              <span className="text-current text-sm font-medium">
                <FormattedMessage
                  defaultMessage="Hạn chế truy cập"
                  id="app.led.[led_key].(configure).config.page.733065223"
                />
              </span>
            </Badge>
          )}
        </div>
        <div className="flex-1 min-h-0 flex flex-col gap-4 overflow-auto px-6 pb-6">
          <div className={cn('flex flex-col rounded-2xl border border-gray-200', { 'border-primary-500': share })}>
            <div className="p-4 flex flex-row items-center gap-4 border-b border-gray-200">
              <div className="flex flex-col justify-center gap-1">
                <span className="font-medium text-base text-gray-700">
                  <FormattedMessage
                    defaultMessage="Chia sẻ bảng LED công khai"
                    id="app.led.[led_key].(configure).config.page.1826411627"
                  />
                </span>
                <span className="font-normal text-sm text-gray-500">
                  {share ? (
                    <FormattedMessage
                      defaultMessage="Mọi người khác sẽ có thể xem dữ liệu bảng LED này mà không cần đăng nhập hay yêu cầu quyền nào."
                      description=""
                      id="app.led.[led_key].(configure).config.page.1977774081"
                    />
                  ) : (
                    <FormattedMessage
                      defaultMessage="Chỉ những người được cấp mật khẩu bảo mật mới có thể truy cập và xem dữ liệu bảng LED này."
                      id="app.led.[led_key].(configure).config.page.1402581559"
                    />
                  )}
                </span>
              </div>
              <Form.Field name="share">
                {(control) => {
                  return (
                    <Switch
                      checked={control.value ?? false}
                      onCheckedChange={(check) => {
                        form.submit();
                        control.onChange(check);
                      }}
                      disabled={isPendingToggleShare}
                    />
                  );
                }}
              </Form.Field>
            </div>
            <div className="pt-3 pb-4 px-4 flex flex-col gap-4">
              <div className="flex flex-col gap-2">
                <span className="font-medium text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="URL chia sẻ bảng LED"
                    id="app.led.[led_key].(configure).config.page.1751753589"
                  />
                </span>
                <div className="h-11 w-full flex flex-row border border-gray-200 rounded-lg overflow-hidden">
                  <div className="flex-1 min-w-0 border-r border-gray-200 flex flex-row items-center gap-2 px-4 py-2 text-primary-500 bg-gray-50">
                    <LinkIcon size={16} className="text-current flex-shink-0" />
                    <Link
                      href={`${window.location.origin}/led/${led_key}`}
                      className="font-normal text-sm text-current underline flex-1 min-w-0 truncate"
                      target="_blank"
                      title=""
                    >
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="text-center">
                            {window.location.origin}/led/{led_key}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent
                          className="px-3 py-2 text-sm font-normal text-gray-100 bg-gray-700 rounded-lg shadow max-w-[426px]"
                          side="top"
                          align="start"
                        >
                          {window.location.origin}/led/{led_key}
                        </TooltipContent>
                      </Tooltip>
                    </Link>
                  </div>
                  <div
                    onClick={() =>
                      handleCopy(
                        `${window.location.origin}/led/${led_key}`,
                        intl.formatMessage(messageDefined.messageCopySuccessURL),
                      )
                    }
                    className="flex flex-row items-center gap-2 px-4 py-2 text-gray-700 flex-shrink-0 cursor-pointer hover:bg-gray-50"
                  >
                    <FilesIcon size={20} className="text-current flex-shink-0" />
                    <span className="text-current font-medium text-sm flex-1 min-w-0">
                      <FormattedMessage
                        defaultMessage="Sao chép"
                        id="app.led.[led_key].(configure).config.page.2023325899"
                      />
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <div className="flex flex-row gap-2 items-center">
                  <span className="font-medium text-sm text-gray-700">
                    <FormattedMessage
                      defaultMessage="Nhúng bảng LED"
                      id="app.led.[led_key].(configure).config.page.903185644"
                    />
                  </span>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon size={16} weight="regular" className="text-gray-500 flex-shrink-0 cursor-pointer" />
                    </TooltipTrigger>
                    <TooltipContent
                      className="p-2 text-gray-300 bg-gray-700 rounded-lg shadow font-normal text-xs max-w-[269px]"
                      side="right"
                      align="start"
                    >
                      <FormattedMessage
                        defaultMessage="Cho phép hiển thị dữ liệu bảng LED trên các nền tảng số khác thông qua mã nhúng."
                        id="app.led.[led_key].(configure).config.page.1085387155"
                      />
                    </TooltipContent>
                  </Tooltip>
                </div>
                <div
                  className={cn(
                    'flex flex-col border border-gray-200 rounded-xl overflow-hidden',
                    '[&_>:not(:first-child)]:border-t [&_>:not(:first-child)]:border-gray-200',
                  )}
                >
                  <div className="flex flex-row gap-[20px] py-2 pr-2 pl-3 items-center">
                    <span className="font-medium text-sm text-gray-700 flex-shrink-0 min-w-[140px]">
                      <FormattedMessage
                        defaultMessage="Kích thước bảng"
                        id="app.led.[led_key].(configure).config.page.2024700209"
                      />
                    </span>
                    <div className="flex flex-row flex-1 min-w-0">
                      <Form.Field name="view_port" trigger="onBlur">
                        <InputPanelSize initValue={[800, 600]} />
                      </Form.Field>
                    </div>
                  </div>
                  <div className="bg-gray-50 py-3 px-4 flex items-center justify-center">
                    <span className="font-normal text-sm text-gray-700 break-all">{str}</span>
                  </div>
                  <div
                    onClick={() => handleCopy(str, intl.formatMessage(messageDefined.messageCopySuccessEmbed))}
                    className="h-11 flex flex-row items-center justify-center gap-2 px-4 py-3 text-primary-500 cursor-pointer hover:bg-gray-50 font-medium text-sm"
                  >
                    <FormattedMessage
                      defaultMessage="Sao chép mã nhúng"
                      id="app.led.[led_key].(configure).config.page.2059658596"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          {!share && (
            <div className="flex flex-col rounded-2xl border border-gray-200 p-4 gap-2">
              <span className="font-medium text-sm text-gray-700">
                <FormattedMessage
                  defaultMessage="Mật khẩu bảo mật"
                  id="app.led.[led_key].(configure).config.page.1386742679"
                />
              </span>
              <Form.Field name="password">
                {(control) => {
                  return (
                    <div className="h-11 w-full flex flex-row border border-gray-200 rounded-lg overflow-hidden">
                      <div className="flex-1 min-w-0 border-r border-gray-200 flex flex-row items-center gap-2 px-4 py-2 text-gray-700 bg-gray-50">
                        <LockIcon size={16} className="text-current flex-shink-0" />
                        <span className="font-normal text-sm text-current">{control.value}</span>
                      </div>
                      <div
                        onClick={() =>
                          handleCopy(control.value, intl.formatMessage(messageDefined.messageCopySuccessPassword))
                        }
                        className="flex flex-row items-center gap-2 px-4 py-2 text-gray-700 flex-shrink-0 cursor-pointer hover:bg-gray-50"
                      >
                        <FilesIcon size={20} className="text-current flex-shink-0" />
                        <span className="text-current font-medium text-sm flex-1 min-w-0">
                          <FormattedMessage
                            defaultMessage="Sao chép"
                            id="app.led.[led_key].(configure).config.page.2023325899"
                          />
                        </span>
                      </div>
                    </div>
                  );
                }}
              </Form.Field>
            </div>
          )}
        </div>
      </div>
    </Form>
  );
}
