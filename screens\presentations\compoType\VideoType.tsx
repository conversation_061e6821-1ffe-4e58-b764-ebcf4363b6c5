import UploadFile from '@/components/ui/UploadFile';
import { ContentType, ISlide } from '@/types/slide';
import { useIntl } from 'react-intl';
import { Form, FormInstance } from 'ui-components';

type FormData = {
  width: number;
  height: number;
  presentations: ISlide[];
};
type PropsVideoType = {
  focusedIndex: number;
  form: FormInstance<FormData>;
  type: ContentType;
};

export const VideoType = ({ focusedIndex, form }: PropsVideoType) => {
  const intl = useIntl();
  return (
    <Form.Field<FormData> name={['presentations', focusedIndex, 'content', 'metadata', 'video']}>
      <UploadFile
        fileType="video"
        accept="video/mp4"
        maxSize={{ value: 25, unit: 'MB' }}
        fileTypeConfigs={{
          video: {
            accept: 'video/mp4',
            maxSize: { value: 25, unit: 'MB' },
            getText(opts) {
              if (opts.isUploading) {
                return {
                  label: 'Video',
                  description: intl.formatMessage({
                    defaultMessage: 'Đang tải lên video...',
                    id: 'screens.presentations.compoType.VideoType.1716374367',
                  }),
                };
              }
              if (opts.selectedFile || opts.previewUrl) {
                return {
                  label: 'Video',
                  description: intl.formatMessage({
                    defaultMessage: 'Đã tải lên video.',
                    id: 'screens.presentations.compoType.VideoType.305657034',
                  }),
                };
              }
              return {
                label: 'Video',
                description: intl.formatMessage({
                  defaultMessage: 'Bạn có thể tải lên 1 tệp tin video định dạng MP4, có dung lượng nhỏ hơn 25MB.',
                  id: 'screens.presentations.compoType.VideoType.343718065',
                }),
              };
            },
          },
        }}
      />
    </Form.Field>
  );
};
