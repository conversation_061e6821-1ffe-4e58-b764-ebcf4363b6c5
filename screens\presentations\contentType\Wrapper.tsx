import { dataTypeObject } from '@/constants';
import { SlideConfigDto } from '@/types/slide';
import { EmblaCarouselType } from 'embla-carousel';
import { useMemo } from 'react';
import ContentHistories from './ContentHistories';
import ContentImage from './ContentImage';
import ContentRealtime from './ContentRealtime';
import ContentText from './ContentText';
import ContentVideo from './ContentVideo';

type Props = {
  dataSlide: SlideConfigDto;
  isIframeReady: boolean;
  setEmblaApi: (emblaApi: EmblaCarouselType) => void;
  isTargeted: boolean;
};
export default function Wrapper({ dataSlide, isIframeReady, setEmblaApi, isTargeted }: Props) {
  return useMemo(() => {
    switch (dataSlide.content.type) {
      case dataTypeObject.realtime:
        return <ContentRealtime dataSlide={dataSlide} isIframeReady={isIframeReady} setEmblaApi={setEmblaApi} />;
      case dataTypeObject.histories:
        return <ContentHistories dataSlide={dataSlide} isIframeReady={isIframeReady} setEmblaApi={setEmblaApi} />;
      case dataTypeObject.video:
        return (
          <ContentVideo
            dataSlide={dataSlide}
            isIframeReady={isIframeReady}
            setEmblaApi={setEmblaApi}
            isTargeted={isTargeted}
          />
        );
      case dataTypeObject.images:
        return <ContentImage dataSlide={dataSlide} isIframeReady={isIframeReady} setEmblaApi={setEmblaApi} />;
      case dataTypeObject.text:
        return <ContentText dataSlide={dataSlide} isIframeReady={isIframeReady} setEmblaApi={setEmblaApi} />;
      default:
        return <div>{dataSlide.content.type}</div>;
    }
  }, [dataSlide, isIframeReady, setEmblaApi, isTargeted]);
}
