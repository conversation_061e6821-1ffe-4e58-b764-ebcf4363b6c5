'use client';

import GroupBox from '@/components/sessions/GroupBox';
import InputLanguage from '@/components/ui/InputLanguage/InputLanguage';
import { usePagination } from '@/hooks/usePagination';
import { EContentType, HistoriesContent, ISlide, RealtimeContent } from '@/types/slide';
import { cn } from '@/utils/tailwind';
import AutoHeight from 'embla-carousel-auto-height';
import Fade from 'embla-carousel-fade';
import useEmblaCarousel from 'embla-carousel-react';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Form, FormInstance, Switch } from 'ui-components';
import ContentConfigSlide from './ContentConfigSlide';
import FooterConfigSlide from './FooterConfigSlide';
import HeaderConfigSlide from './HeaderConfigSlide';
import TagConfigSlide from './TagConfigSlide';
import { getGridSize, getTableSize, priorityGrid, priorityTable, priorityText } from './compoType/constants';
import { useSlideConfigStores } from '@/stores';
import { useResponsiveFontSize } from '@/hooks/useResponsiveFontSize';
import { PRIORITY_SCALE } from './components/ResponsiveTextWithOverride';

type FormData = {
  width: number;
  height: number;
  presentations: ISlide[];
};

enum Tag {
  desktop = 'desktop',
  tablet = 'tablet',
  mobile = 'mobile',
}

type PropsConfigSlide = {
  focusedIndex: number;
  form: FormInstance<FormData>;
  onChangeType: Dispatch<SetStateAction<'submit' | 'save'>>;
  dataSlide: ISlide;
};

export default function ConfigSlide({ focusedIndex, form, onChangeType, dataSlide }: PropsConfigSlide) {
  const intl = useIntl();
  const sidebarRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const tagSentinelRef = useRef<HTMLDivElement>(null);
  const slidesRef = useRef<(HTMLDivElement | null)[]>([]);
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true, watchDrag: false, containScroll: false }, [
    AutoHeight(),
    Fade(),
  ]);
  const config = useSlideConfigStores((store) => store.config);
  const responsiveFontSize = useResponsiveFontSize(config);
  const { selectedIndex, onDotButtonClick } = usePagination(emblaApi);

  const [isSticky, setIsSticky] = useState(false);

  const setSlideRef = (el: HTMLDivElement | null, index: number) => {
    slidesRef.current[index] = el;
  };

  const updateHeight = useCallback(() => {
    const slide = slidesRef.current[selectedIndex];
    if (slide && containerRef.current) {
      const height = slide.offsetHeight;
      containerRef.current.style.height = `${height}px`;
    }
  }, [selectedIndex]);

  useEffect(() => {
    const slide = slidesRef.current[selectedIndex];
    if (!slide || !containerRef.current) return;

    const observer = new ResizeObserver(() => updateHeight());
    observer.observe(slide);

    updateHeight();

    return () => observer.disconnect();
  }, [selectedIndex, updateHeight]);

  useEffect(() => {
    if (!emblaApi) return;

    const onSelect = () => {
      const index = emblaApi.selectedScrollSnap();
      onDotButtonClick(index);
    };

    emblaApi.on('select', onSelect);
    onSelect();

    return () => {
      emblaApi.off('select', onSelect);
    };
  }, [emblaApi]);

  useEffect(() => {
    onDotButtonClick(0);
  }, [focusedIndex]);

  useEffect(() => {
    if (!sidebarRef.current || !tagSentinelRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsSticky(!entry.isIntersecting);
      },
      {
        root: sidebarRef.current,
        threshold: [1],
      },
    );

    observer.observe(tagSentinelRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  const getDeviceFromSize = useCallback((width: number): Tag => {
    if (width <= 480) {
      return Tag.mobile;
    }
    if (width > 480 && width <= 960) {
      return Tag.tablet;
    }
    return Tag.desktop;
  }, []);

  const device = useMemo(() => getDeviceFromSize(config.width), [config.width]);

  const fontConfig = useMemo(() => {
    const getRealtimeConfig = () => {
      if (dataSlide?.content?.type === EContentType.realtime) {
        const metadata = dataSlide.content.metadata as RealtimeContent;
        const layout = metadata?.layout;
        const template = layout?.template;
        const view = layout?.[template]?.view;

        if (template && layout?.[template]) {
          let column = 3,
            row = 3;

          if (view === 'general') {
            column = layout[template].general?.column || (template === 'table' ? 7 : 3);
            row = layout[template].general?.row || (template === 'table' ? 1 : 3);
          } else if (view === 'custom') {
            column = layout[template].custom?.[device]?.column || (template === 'table' ? 7 : 3);
            row = layout[template].custom?.[device]?.row || (template === 'table' ? 1 : 3);
          }

          return { column, row, template };
        }
      }

      if (dataSlide?.content?.type === EContentType.histories) {
        const metadata = dataSlide.content.metadata as HistoriesContent;
        const layout = metadata?.layout;
        const view = layout?.view;

        if (view === 'general') {
          return { column: layout.general?.column || 8, row: layout.general?.row || 7, template: 'table' };
        } else {
          return {
            column: layout.custom?.[device]?.column || 8,
            row: layout.custom?.[device]?.row || 7,
            template: 'table',
          };
        }
      }
      return { column: 3, row: 3, template: 'grid' };
    };

    return getRealtimeConfig();
  }, [dataSlide?.content?.type, dataSlide?.content?.metadata, device]);

  const getSize = useCallback(() => {
    const { column, row, template } = fontConfig;
    return template === 'table' ? getTableSize(column, row) : getGridSize(column, row);
  }, [fontConfig.column, fontConfig.row, fontConfig.template]);

  const size = useMemo(() => {
    return getSize();
  }, [fontConfig.column, fontConfig.row, fontConfig.template]);

  const initFont = useMemo(() => {
    const scaleGrid = Object.keys(priorityGrid[size])
      .map((k) => {
        const key = k as keyof (typeof priorityGrid)[typeof size];
        const priorityValue = priorityGrid[size][key] as keyof typeof PRIORITY_SCALE;
        return { [key]: PRIORITY_SCALE[priorityValue] * responsiveFontSize };
      })
      .reduce((acc, cur) => ({ ...acc, ...cur }), {});

    const scaleTable = Object.keys(priorityTable[size])
      .map((k) => {
        const key = k as keyof (typeof priorityTable)[typeof size];
        const priorityValue = priorityTable[size][key] as keyof typeof PRIORITY_SCALE;
        return { [key]: PRIORITY_SCALE[priorityValue] * responsiveFontSize };
      })
      .reduce((acc, cur) => ({ ...acc, ...cur }), {});

    const scaleText = Object.keys(priorityText)
      .map((k) => {
        const key = k as keyof typeof priorityText;
        const priorityValue = priorityText[key] as keyof typeof PRIORITY_SCALE;
        return { [key]: PRIORITY_SCALE[priorityValue] * responsiveFontSize };
      })
      .reduce((acc, cur) => ({ ...acc, ...cur }), {});

    return {
      grid: scaleGrid,
      table: scaleTable,
      text: scaleText,
    };
  }, [responsiveFontSize, size]);

  const isAutoHeader = useMemo(() => dataSlide?.header?.fontSize?.auto, [dataSlide?.header?.fontSize?.auto]);
  const isAutoFooter = useMemo(() => dataSlide?.footer?.fontSize?.auto, [dataSlide?.footer?.fontSize?.auto]);

  useEffect(() => {
    if (isAutoHeader) {
      form.setFieldValue(['presentations', focusedIndex, 'header', 'fontSize', 'config'], initFont.text);
    }
    if (isAutoFooter) {
      form.setFieldValue(['presentations', focusedIndex, 'footer', 'fontSize', 'config'], initFont.text);
    }
  }, [form, focusedIndex, initFont.text, isAutoHeader, isAutoFooter]);

  return (
    <div
      ref={sidebarRef}
      className="w-[420px] flex-shrink-0 bg-gray-100 flex flex-col overflow-y-auto overflow-x-hidden"
    >
      <div className="h-auto w-full px-4 pt-4">
        <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
          <span className="font-semibold text-base text-gray-700">
            <FormattedMessage
              defaultMessage="Cấu hình trang trình chiếu"
              id="app.led.[led_key].(configure).presentations.page.126322537"
            />
          </span>
          <div className="flex flex-col gap-2">
            <span className="font-medium text-sm text-gray-700">
              <FormattedMessage
                defaultMessage="Tên trang trình chiếu"
                id="app.led.[led_key].(configure).presentations.page.533863926"
              />
            </span>
            <div className="h-full w-full">
              <Form.Field name={['presentations', focusedIndex, 'title']} valuePropName="value" trigger="onBlur">
                <InputLanguage
                  placeholder={intl.formatMessage({
                    defaultMessage: 'Nhập tên trang trình chiếu',
                    id: 'screens.presentations.ConfigSlide.1191347981',
                  })}
                  name="title"
                  defaultValue={{
                    vi: `Trang số ${focusedIndex + 1}`,
                    en: `Slide ${focusedIndex + 1}`,
                  }}
                />
              </Form.Field>
            </div>
          </div>
          <div className="flex flex-col gap-2">
            <span className="font-medium text-sm text-gray-700">
              <FormattedMessage
                defaultMessage="Bật/tắt hiển thị"
                id="app.led.[led_key].(configure).presentations.page.1873460688"
              />
            </span>
            <GroupBox>
              <div className="flex flex-row gap-3 p-3 items-center">
                <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Hiển thị đầu trang"
                    id="app.led.[led_key].(configure).presentations.page.1784818302"
                  />
                </span>
                <Form.Field
                  name={['presentations', focusedIndex, 'header', 'show']}
                  valuePropName="checked"
                  trigger="onCheckedChange"
                >
                  <Switch className="flex-shrink-0" />
                </Form.Field>
              </div>
              <div className="flex flex-row gap-3 p-3 items-center">
                <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Hiển thị nội dung"
                    id="app.led.[led_key].(configure).presentations.page.944461609"
                  />
                </span>
                <Form.Field
                  name={['presentations', focusedIndex, 'content', 'show']}
                  valuePropName="checked"
                  trigger="onCheckedChange"
                >
                  <Switch className="flex-shrink-0" />
                </Form.Field>
              </div>
              <div className="flex flex-row gap-3 p-3 items-center">
                <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Hiển thị chân trang"
                    id="app.led.[led_key].(configure).presentations.page.535929162"
                  />
                </span>
                <Form.Field
                  name={['presentations', focusedIndex, 'footer', 'show']}
                  valuePropName="checked"
                  trigger="onCheckedChange"
                >
                  <Switch className="flex-shrink-0" />
                </Form.Field>
              </div>
            </GroupBox>
          </div>
        </div>
      </div>

      <div ref={tagSentinelRef} className="h-[1px]" />
      <TagConfigSlide
        currentTag={selectedIndex}
        onChangeTag={onDotButtonClick}
        className={cn({
          'shadow-c2': isSticky,
        })}
      />

      <div className="h-auto w-full px-4 pb-4">
        <div className="flex flex-col w-full">
          <div className="relative w-full overflow-hidden" ref={containerRef}>
            <div ref={emblaRef} className="w-full">
              <div className="flex flex-row w-full">
                <div className="flex-1 min-w-full h-fit" tabIndex={0} ref={(el) => setSlideRef(el, 0)}>
                  <HeaderConfigSlide
                    dataHeader={dataSlide.header}
                    focusedIndex={focusedIndex}
                    form={form}
                    onChangeType={onChangeType}
                    initFont={initFont.text}
                  />
                </div>
                <div className="flex-1 min-w-full h-fit" tabIndex={0} ref={(el) => setSlideRef(el, 1)}>
                  <ContentConfigSlide
                    dataContent={dataSlide.content}
                    focusedIndex={focusedIndex}
                    form={form}
                    initFont={initFont}
                  />
                </div>
                <div className="flex-1 min-w-full h-fit" tabIndex={0} ref={(el) => setSlideRef(el, 2)}>
                  <FooterConfigSlide
                    dataFooter={dataSlide.footer}
                    focusedIndex={focusedIndex}
                    form={form}
                    initFont={initFont.text}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
