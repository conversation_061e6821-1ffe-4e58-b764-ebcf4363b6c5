import CircleProgressIcon from '@/components/ui/CircleProgressIcon';
import { capitalizeFirstLetter } from '@/utils';
import { ImageIcon, TrashIcon } from '@phosphor-icons/react';
import Image from 'next/image';
import { ChangeEvent, useRef, useState } from 'react';
import { defineMessages, FormattedMessage, useIntl } from 'react-intl';
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  toast,
} from 'ui-components';

type Props = {
  value?: string | File | null;
  onChange?: (value: File | null) => void;
  config?: {
    accept: string;
    maxSize: number | { value: number; unit: 'B' | 'KB' | 'MB' | 'GB' };
  };
};

const definedMessage = defineMessages({
  messageUploadFail: {
    defaultMessage: 'Không thể tải lên tệp tin. Vui lòng thử lại sau.',
    id: 'components.ui.UploadFile.1640628908',
  },
  messageUploadFailSize: {
    defaultMessage: '<PERSON>ui lòng chọn tệp tin có tổng dung lượng nhỏ hơn {maxSize}.',
    id: 'components.ui.UploadFile.2113771008',
  },
  messageUploadFailType: {
    defaultMessage: 'Định dạng tệp tin không hỗ trợ. Vui lòng chọn tệp tin khác.',
    id: 'components.ui.UploadFile.926063465',
  },
});

export default function ImageUpload({
  value,
  onChange: onFileChange,
  config = {
    accept: 'image/jpeg,image/png,image/svg+xml',
    maxSize: {
      value: 1,
      unit: 'MB',
    }, // Default 1MB
  },
}: Props) {
  const intl = useIntl();
  const [imageFile, setImageFile] = useState<File | null>(typeof value !== 'object' ? null : value);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isOpenModalDel, setIsOpenModalDel] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [isHover, setIsHover] = useState(false);
  const hasFile = Boolean(imageFile) || Boolean(value);

  // Helper: convert size to bytes
  function sizeToBytes(size: number | { value: number; unit: 'B' | 'KB' | 'MB' | 'GB' }) {
    if (typeof size === 'number') return size * 1024;
    const { value, unit } = size;
    switch (unit) {
      case 'B':
        return value;
      case 'KB':
        return value * 1024;
      case 'MB':
        return value * 1024 * 1024;
      case 'GB':
        return value * 1024 * 1024 * 1024;
      default:
        return value * 1024;
    }
  }
  // Helper: get display string for max size
  function maxSizeDisplay(size: number | { value: number; unit: 'B' | 'KB' | 'MB' | 'GB' }) {
    if (typeof size === 'number') return `${size}KB`;
    return `${size.value}${size.unit}`;
  }

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (config.accept && !config.accept.split(',').includes(file.type)) {
      toast({
        type: 'error',
        title: intl.formatMessage(definedMessage.messageUploadFailType),
        options: {
          position: 'top-center',
        },
      });
      return;
    }
    // Check file size
    if (config.maxSize && file.size > sizeToBytes(config.maxSize)) {
      toast({
        type: 'error',
        title: intl.formatMessage(definedMessage.messageUploadFailSize, { maxSize: maxSizeDisplay(config.maxSize) }),
        options: {
          position: 'top-center',
        },
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const totalSteps = 10;
      for (let step = 1; step <= totalSteps; step++) {
        await new Promise((resolve) => setTimeout(resolve, 50));
        setUploadProgress(Math.floor((step / totalSteps) * 100));
      }

      setImageFile(file);
      setIsHover(false);
      if (onFileChange) {
        onFileChange(file);
      }
    } catch (err) {
      toast({
        type: 'error',
        title: intl.formatMessage(definedMessage.messageUploadFail),
        options: {
          position: 'top-center',
        },
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const onDelete = () => {
    setImageFile(null);
    if (onFileChange) {
      onFileChange(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    setIsOpenModalDel(false);
  };

  const text = {
    label: intl.formatMessage({
      defaultMessage: 'hình ảnh',
      id: 'screens.presentations.components.ImageUpload.1180100933',
    }),
  } as const;
  return (
    <div
      className="h-full w-full flex items-center justify-center group bg-gray-100 hover:bg-gray-200 rounded-lg"
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
    >
      {isUploading ? (
        <CircleProgressIcon value={uploadProgress} />
      ) : hasFile ? (
        isHover ? (
          <Button
            variant="gray"
            className="flex-shrink-0 text-red-500 bg-white rounded-full group-hover:bg-white"
            type="button"
            icon
            onClick={() => {
              setIsOpenModalDel(true);
            }}
            disabled={isUploading}
          >
            <TrashIcon size={20} weight="regular" className="text-current" />
          </Button>
        ) : (
          <ImageIcon size={24} weight="regular" className="text-gray-700" />
        )
      ) : (
        <Button
          variant="gray"
          className="flex-shrink-0 bg-white text-primary-500 w-[68px] h-[36px] group-hover:bg-gray-100"
          type="button"
          onClick={handleButtonClick}
          disabled={isUploading}
        >
          <FormattedMessage defaultMessage="Chọn" id="components.ui.UploadFile.2340486" />
          <input
            hidden
            type="file"
            ref={fileInputRef}
            accept={config.accept}
            onChange={handleFileChange}
            disabled={isUploading}
          />
        </Button>
      )}

      <Dialog open={isOpenModalDel} onOpenChange={setIsOpenModalDel}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[420px] h-auto rounded-xl sm:rounded-xl">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <div className="flex flex-col">
              <div className="flex flex-col justify-center items-center p-5">
                <Image
                  src={`/images/commons/delete-image.svg`}
                  alt="deleted-image"
                  width={200}
                  height={200}
                  className="h-50 w-50 object-contain flex-shrink-0 aspect-square"
                  priority
                />
                <div className="flex-1 min-h-0 flex flex-col gap-1 items-center justify-center">
                  <span className="text-center font-semibold text-lg leading-[27px] text-gray-800">
                    {capitalizeFirstLetter(
                      intl.formatMessage(
                        {
                          defaultMessage: 'Xác nhận xóa {label}',
                          id: 'components.ui.UploadFile.62368309',
                        },
                        {
                          label: text.label,
                        },
                      ),
                    )}
                  </span>
                  <span className="text-center font-normal text-sm leading-[21px] text-gray-700">
                    {capitalizeFirstLetter(
                      intl.formatMessage(
                        {
                          defaultMessage:
                            'Bạn chắc chắn muốn xóa {label} này? Vui lòng xác nhận nếu bạn muốn tiếp tục.',
                          id: 'components.ui.UploadFile.136903079',
                        },
                        {
                          label: text.label,
                        },
                      ),
                    )}
                  </span>
                </div>
              </div>
              <div className="flex flex-row justify-end gap-3 p-[14px] border-t border-gray-200">
                <Button
                  type="button"
                  variant="gray"
                  className="flex-1 text-primary-500 text-sm font-normal"
                  onClick={() => {
                    setIsOpenModalDel(false);
                    setIsHover(false);
                  }}
                >
                  <FormattedMessage defaultMessage="Hủy bỏ" id="components.ui.UploadFile.780985299" />
                </Button>
                <Button type="button" variant="danger" className="flex-1 text-sm font-normal" onClick={onDelete}>
                  <FormattedMessage defaultMessage="Xác nhận" id="components.ui.UploadFile.559188735" />
                </Button>
              </div>
            </div>
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </div>
  );
}
