import { IMeasuring } from '@/types';
import { EStatus } from '@/types/slide';

/**
 * Interface for measure data with threshold information
 */
export interface MeasureData {
  measure: string;
  value: number;
  status: EStatus;
}

/**
 * Interface for date-specific measure data
 */
export interface DateMeasureData {
  [date: string]: MeasureData[];
}

/**
 * Interface for threshold configuration options
 */
export interface ThresholdOptions {
  isWarnExceeded: boolean;
  isWarnApproaching: boolean;
}

/**
 * Interface for threshold evaluation result
 */
export interface ThresholdEvaluation {
  status: EStatus;
  details: {
    exceededMin: boolean;
    exceededMax: boolean;
    approachingMin: boolean;
    approachingMax: boolean;
  };
  color?: string;
}

/**
 * Enhanced threshold checking function that applies threshold colors based on configuration
 * This function implements the requirements 5.3 and 5.4 for threshold warnings
 *
 * @param dataItem - The measuring item with value
 * @param options - Configuration options for warnings
 * @param colors - Optional color configuration for thresholds
 * @returns The threshold status with detailed information and color
 */
export const evaluateThreshold = (
  dataItem: IMeasuring & { value: number | null },
  options?: ThresholdOptions,
  colors?: {
    exceeded?: string;
    approaching?: string;
    good?: string;
  },
): ThresholdEvaluation => {
  const details = {
    exceededMin: false,
    exceededMax: false,
    approachingMin: false,
    approachingMax: false,
  };

  // If warnings are disabled, return GOOD with no details
  if (!options?.isWarnExceeded && !options?.isWarnApproaching) {
    return {
      status: EStatus.Good,
      details,
      color: colors?.good || 'transparent',
    };
  }

  // Check for exceeded threshold - implements requirement 5.3
  if (options.isWarnExceeded) {
    if (dataItem?.minLimit !== null && dataItem?.value !== null && dataItem?.value < dataItem?.minLimit) {
      details.exceededMin = true;
    }
    if (dataItem?.maxLimit !== null && dataItem?.value !== null && dataItem?.value > dataItem?.maxLimit) {
      details.exceededMax = true;
    }
  }

  // Check for approaching threshold - implements requirement 5.4
  if (options.isWarnApproaching) {
    if (dataItem?.minTend !== null && dataItem?.value !== null && dataItem?.value < dataItem?.minTend) {
      details.approachingMin = true;
    }
    if (dataItem?.maxTend !== null && dataItem?.value !== null && dataItem?.value > dataItem?.maxTend) {
      details.approachingMax = true;
    }
  }

  // Determine overall status and apply appropriate color
  // Priority: EXCEEDED > EXCEEDED_PREPARING > GOOD
  let status: EStatus = EStatus.Good;
  let color = colors?.good || 'transparent';

  if (details.exceededMin || details.exceededMax) {
    status = EStatus.Exceeded;
    color = colors?.exceeded || '#FF4D4F'; // Default red color for exceeded
  } else if (details.approachingMin || details.approachingMax) {
    status = EStatus.ExceededPreparing;
    color = colors?.approaching || '#FAAD14'; // Default yellow color for approaching
  }

  return { status, details, color };
};

/**
 * Evaluates all measure parameters for a date across all pages
 * This function ensures that evaluation considers all data for a time period across all pages
 * Implements requirements 3.1, 3.2, 3.3, and 3.4 for cross-page evaluation
 * Optimized for performance with early returns and Map lookups
 *
 * @param date - The date to evaluate
 * @param allMeasureData - All measure data for all dates
 * @param allMeasureKeys - All measure keys to consider (across all pages)
 * @returns Evaluation result with compliance status and details
 */
export const evaluateDateAcrossPages = (
  date: string,
  allMeasureData: DateMeasureData,
  allMeasureKeys: string[],
): {
  isCompliant: boolean;
  details: {
    exceededMeasures: string[];
    approachingMeasures: string[];
    goodMeasures: string[];
    missingMeasures: string[];
  };
} => {
  // Get all measure data for this date
  const dateMeasures = allMeasureData[date] || [];

  // Early return optimization: if no data for this date, return non-compliant
  if (dateMeasures.length === 0) {
    return {
      isCompliant: false,
      details: {
        exceededMeasures: [],
        approachingMeasures: [],
        goodMeasures: [],
        missingMeasures: [...allMeasureKeys],
      },
    };
  }

  // Create a Map for O(1) lookups instead of using find() which is O(n)
  const measureDataMap = new Map(dateMeasures.map((m) => [m.measure, m]));

  // Fast path: check for any exceeded or approaching measures first
  // This allows early return without processing all measures
  for (const measureKey of allMeasureKeys) {
    const measureData = measureDataMap.get(measureKey);

    // If measure is missing or exceeds thresholds, we know it's not compliant
    if (
      !measureData ||
      measureData.status === EStatus.Exceeded ||
      measureData.status === EStatus.ExceededPreparing
    ) {
      // Now build the full details for the return value
      const details = {
        exceededMeasures: [] as string[],
        approachingMeasures: [] as string[],
        goodMeasures: [] as string[],
        missingMeasures: [] as string[],
      };

      // Process all measures to build complete details
      for (const key of allMeasureKeys) {
        const data = measureDataMap.get(key);

        if (!data) {
          details.missingMeasures.push(key);
          continue;
        }

        switch (data.status) {
          case EStatus.Exceeded:
            details.exceededMeasures.push(key);
            break;
          case EStatus.ExceededPreparing:
            details.approachingMeasures.push(key);
            break;
          case EStatus.Good:
            details.goodMeasures.push(key);
            break;
        }
      }

      return {
        isCompliant: false,
        details,
      };
    }
  }

  // If we get here, all measures are GOOD
  return {
    isCompliant: true,
    details: {
      exceededMeasures: [],
      approachingMeasures: [],
      goodMeasures: [...allMeasureKeys],
      missingMeasures: [],
    },
  };
};

/**
 * Memoized version of evaluateDateAcrossPages for better performance
 * Uses a Map to cache results based on input parameters
 */
export const memoizedEvaluateDateAcrossPages = (() => {
  // Use Map for string keys
  const cache = new Map<
    string,
    {
      isCompliant: boolean;
      details: {
        exceededMeasures: string[];
        approachingMeasures: string[];
        goodMeasures: string[];
        missingMeasures: string[];
      };
    }
  >();

  return (date: string, allMeasureData: DateMeasureData, allMeasureKeys: string[]) => {
    // Create a cache key based on the date and a hash of the measure data
    const cacheKey = `${date}|${allMeasureKeys.join(',')}`;

    // Check if we have a cached result
    if (cache.has(cacheKey)) {
      return cache.get(cacheKey)!;
    }

    // Calculate the result
    const result = evaluateDateAcrossPages(date, allMeasureData, allMeasureKeys);

    // Cache the result
    cache.set(cacheKey, result);

    // Keep cache size reasonable
    if (cache.size > 1000) {
      // Remove oldest entry (first key)
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey!);
    }

    return result;
  };
})();

/**
 * Gets the threshold status for a measuring item
 * @param dataItem - The measuring item with value
 * @param options - Configuration options for warnings
 * @returns The threshold status (EXCEEDED, EXCEEDED_PREPARING, or GOOD)
 */
export const getThresholdStatus = (
  dataItem: IMeasuring & { value: number | null },
  options: ThresholdOptions,
): EStatus => {
  const evaluation = evaluateThreshold(dataItem, options);
  return evaluation.status;
};
