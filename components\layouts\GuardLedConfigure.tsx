'use client';

import { useLedConfigStores, useLedStores } from '@/stores';
import LedConfigDashboard from './LedConfigDashboard';
import { PropsWithChildren, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { routePath } from '@/constants';
import LoadingIndicator from '@/app/led/[led_key]/loading';
import { useQueryClient } from '@tanstack/react-query';

export default function GuardLedConfigure({ children }: PropsWithChildren) {
  const isLoginedConfig = useLedConfigStores((store) => store.isLoginedConfig);
  const { setRedirectTo } = useLedStores((store) => store);
  const router = useRouter();
  const params = useParams();
  const { led_key } = params as { led_key: string };
  const queryClient = useQueryClient();

  useEffect(() => {
    const fetching = async () => {
      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: ['LedServiceClient', 'getLedConfig', led_key],
          exact: true,
        }),
        queryClient.invalidateQueries({
          queryKey: ['LedServiceClient', 'getStationTypes'],
          exact: true,
        }),
      ]);
    };
    if (isLoginedConfig) {
      fetching();
    }
  }, [isLoginedConfig]);

  useEffect(() => {
    if (!isLoginedConfig) {
      setRedirectTo(routePath.ledConfig(led_key));
      router.push(routePath.ledAuth(led_key));
    }
  }, [isLoginedConfig]);

  if (!isLoginedConfig) return <LoadingIndicator />;

  return <LedConfigDashboard>{children}</LedConfigDashboard>;
}
