import { ColorPicker, IColor, useColor } from 'react-color-palette';
import 'react-color-palette/css';
import { Popover, PopoverTrigger, PopoverContent } from 'ui-components';
import { VariantProps, cva } from 'class-variance-authority';
import clsx from 'clsx';
import { CaretUpDownIcon } from '@phosphor-icons/react';
import { cn } from '@/utils/tailwind';

const comboboxVariants = cva(
  clsx(
    'font-normal text-sm leading-[21px] text-gray-700',
    'px-4 py-3 outline-none',
    'flex flex-row w-full items-center justify-between gap-2 hover:z-[2]',
    '[&>span]:line-clamp-1',
    'transition-all duration-300 ease-in bg-white',
    'hover:bg-gray-50',
  ),
  {
    variants: {
      variant: {
        default: clsx(''),
        success: clsx('border border-green-500'),
        error: clsx('border border-red-500 hover:ring-0 active:ring-0 data-[state=open]:border-red-500'),
      },
      size: {
        sm: clsx('h-10'),
        md: clsx('h-11'),
        lg: clsx('h-12'),
      },
    },
    defaultVariants: {
      size: 'md',
      variant: 'default',
    },
  },
);

export interface PaletteColorPickerProps extends VariantProps<typeof comboboxVariants> {
  className?: string;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  modalPopover?: boolean;
  readonly?: boolean;
}

export const PaletteColorPicker = (props: PaletteColorPickerProps) => {
  const { open, onOpenChange, disabled, modalPopover, value, variant, size, onChange, readonly } = props;
  const handleOpen = (open: boolean) => {
    onOpenChange?.(open);
  };
  const [color, setColor] = useColor(value || '#ffffff');

  return (
    <Popover open={open} onOpenChange={handleOpen} modal={modalPopover}>
      <PopoverTrigger asChild>
        <button
          role="combobox"
          aria-expanded={open}
          disabled={disabled || readonly}
          className={cn(
            comboboxVariants({ variant, size }),
            {
              'bg-gray-50 hover:bg-gray-50 hover:ring-0 active:ring-0 cursor-not-allowed': disabled,
            },
            { 'bg-white hover:bg-white hover:ring-0 active:ring-0 cursor-default': readonly },
          )}
        >
          <div className="flex flex-row gap-2 items-center">
            <div className={cn('w-5 h-5 rounded-full ring-[1px] ring-gray-200 flex-shrink-0')}>
              <div
                className="h-full w-full rounded-full "
                style={{
                  backgroundColor: color.hex,
                }}
              />
            </div>
            <span className="font-normal text-sm">{color.hex.toUpperCase()}</span>
          </div>
          <CaretUpDownIcon size={18} weight="regular" className="text-gray-700" />
        </button>
      </PopoverTrigger>
      <PopoverContent className="w-[360px] max-w-[360px] p-3 shadow-default overflow-hidden rounded-xl [&>.rcp_.rcp-body_.rcp-section_.rcp-fields]:flex-row [&>.rcp_.rcp-body_.rcp-section_.rcp-fields_.rcp-fields-floor]:flex [&>.rcp_.rcp-body_.rcp-section_.rcp-fields_.rcp-fields-floor]:flex-1">
        <ColorPicker
          hideInput={['hsv']}
          height={200}
          hideAlpha
          color={color}
          onChange={(color) => {
            setColor(color);
            onChange?.(color.hex);
          }}
        />
      </PopoverContent>
    </Popover>
  );
};
