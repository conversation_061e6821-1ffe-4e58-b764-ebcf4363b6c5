import { setCookie } from '@/actions/cookieActions';
import { LANGUEGES_OPTIONS, NEXT_LOCALE } from '@/constants';
import { useNavigationLoading } from '@/hooks/useNavigationLoading';
import { cn } from '@/utils/tailwind';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useState, useTransition } from 'react';
import { useIntl } from 'react-intl';
import { Popover, PopoverContent, PopoverTrigger, Spin } from 'ui-components';

export default function DropdownLanguage({ className }: { className?: string }) {
  const intl = useIntl();
  const lng = intl.locale;
  const currentLang = LANGUEGES_OPTIONS.find((lang) => lang.value === lng);
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const { isNavigating, startNavigation, stopNavigation } = useNavigationLoading();
  const [isPending, startTransition] = useTransition();

  const isLoading = isPending || isNavigating;

  const changeLocale = async (newLocale: string) => {
    if (newLocale === lng) return;

    startNavigation();
    setOpen(false);
    startTransition(async () => {
      await setCookie(NEXT_LOCALE, newLocale, {
        httpOnly: true,
        secure: true,
        maxAge: 14 * 24 * 3600,
        sameSite: 'none',
      });
      router.refresh();
      setTimeout(() => {
        stopNavigation();
      }, 500);
    });
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div
            className={cn(
              'min-h-[37px] h-full w-auto flex flex-row items-center gap-3 border bg-white border-solid py-2 px-[10px] rounded-lg cursor-pointer hover:bg-gray-100 text-gray-700',
              className,
            )}
          >
            <Image
              src={currentLang?.image as string}
              alt={currentLang?.label as string}
              width={24}
              height={24}
              priority
              quality={75}
              loading="eager"
              className="object-contain w-6 h-6 flex-shrink-0"
            />
            <span className="font-normal text-sm text-gray-700 flex-1">{currentLang?.label}</span>
          </div>
        </PopoverTrigger>
        <PopoverContent
          className="min-w-[42px] w-[180px] max-w-[200px] bg-white rounded-xl border border-gray-100 p-2"
          align="end"
          side="bottom"
          sideOffset={-40}
        >
          {LANGUEGES_OPTIONS.map((lang) => (
            <div
              key={lang.value}
              onClick={() => changeLocale(lang.value)}
              className={cn('cursor-pointer w-full flex flex-row gap-3 p-2 h-[37px] rounded-lg', {
                'bg-gray-100': currentLang?.value === lang.value,
              })}
            >
              <Image
                src={lang.image}
                alt={lang.label}
                width={24}
                height={24}
                priority
                quality={75}
                loading="eager"
                className="object-contain w-6 h-6 flex-shrink-0"
              />
              <span className="font-normal text-sm text-gray-700">{lang?.label}</span>
            </div>
          ))}
        </PopoverContent>
      </Popover>
      {isLoading && <Spin loading fullScreen className="fixed h-screen w-screen bg-black/20 z-[1000]" />}
    </>
  );
}
