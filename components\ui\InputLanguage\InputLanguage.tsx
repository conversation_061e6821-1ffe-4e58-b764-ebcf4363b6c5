import { cn } from '@/utils/tailwind';
import { FlagIcon, TranslateIcon, XIcon } from '@phosphor-icons/react';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
  Form,
  Input,
  Meta,
} from 'ui-components';
import { TextAreaField } from '../TextareaField';
import { ConfirmChangeLanguage } from './ConfirmChangeLanguage';
import { FormattedMessage, useIntl } from 'react-intl';
import { capitalizeFirstLetter } from '@/utils';
import { messageErrorCommon } from '@/constants/defineMessages';

/**
 * ví dụ khi sử dụng InputLanguage
 * <Form.Field
    name="name"
    rules={[
      {
        validator(rule, value, callback) {
          if (!(value.vi.length > 0)) {
            callback('Tên khu công nghiệp không được để trống.');
          }
          callback();
        },
      },
    ]}
  >
    {({ value, onChange }, meta) => (
      <InputLanguage
        placeholder="Nhập tên khu công nghiệp"
        name="name"
        meta={meta}
        value={value}
        onChange={onChange}
      />
    )}
  </Form.Field>
 */

type LanguageValue = {
  vi: string;
  en: string;
};

interface IInputElement {
  // Common properties from both input types that we need
  className?: string;
  id?: string;
  style?: React.CSSProperties;
  disabled?: boolean;
  placeholder?: string;
  maxLenght?: number;
  // Add other common properties as needed
}

interface TInputLanguage extends IInputElement {
  value?: LanguageValue;
  defaultValue?: LanguageValue;
  onChange?: (value: LanguageValue) => void;
  onBlur?: (value: LanguageValue) => void;
  placeholder: string;
  name: string;
  meta?: Meta;
  maxLengthRule?: number | false;
  type?: 'input' | 'textarea';
  locate?: { vi: string; en: string };
  locateNameSupport?: {
    vi: string;
    en: string;
  };
  maxHeight?: boolean;
  disabled?: boolean;
}

export default function InputLanguage({
  name,
  value,
  defaultValue,
  onChange,
  placeholder,
  meta,
  maxLengthRule = false,
  type = 'input',
  maxHeight,
  disabled,
  onBlur,
  maxLenght,
  ...props
}: TInputLanguage) {
  const [isOpenLang, setIsOpenLang] = useState(false);
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';
  const [langValue, setLangValue] = useState<LanguageValue>(value || { vi: '', en: '' });

  const isError = (meta?.touched || meta?.validated || meta?.validating) && meta?.errors.length > 0;

  useEffect(() => {
    if (value) {
      setLangValue(value);
    }
  }, [value]);

  const handleLanguageSubmit = (newValue: LanguageValue) => {
    setLangValue(newValue);
    setIsOpenLang(false);
    if (onBlur) {
      onBlur(newValue);
    }
    if (onChange) {
      onChange(newValue);
    }
  };

  const handleInputChange = (newViValue: string) => {
    const updatedLangValue = { ...langValue, vi: newViValue, en: newViValue };
    setLangValue(updatedLangValue);
    if (onChange) {
      onChange(updatedLangValue);
    }
  };

  const handleInputBlur = (newViValue: string) => {
    const updatedLangValue =
      newViValue.length > 0 || !defaultValue ? { ...langValue, vi: newViValue, en: newViValue } : defaultValue;
    setLangValue(updatedLangValue);
    if (onBlur) {
      onBlur(updatedLangValue);
    }
  };

  return (
    <div
      className={cn('flex flex-col gap-2 w-full', {
        'h-full': type === 'textarea' && maxHeight === true,
      })}
    >
      <div
        className={cn(
          'group w-full flex flex-row items-center border border-gray-200 rounded-lg h-full',
          '[&>*]:rounded-none [&>*:last-child]:rounded-e-lg [&>*:first-child]:rounded-s-lg',
          'transition-all duration-300 ease-in',
          'hover:ring-2 hover:ring-primary-300',
          { 'hover:ring-0': disabled },
          { 'border-red-500 hover:ring-0': isError },
        )}
      >
        {type === 'input' ? (
          <Input
            placeholder={placeholder}
            className={cn(
              'hover:ring-0 focus-visible:ring-0 active:ring-0 flex-1 min-w-0 border-0 h-[42px] pr-2',
              'border-transparent',
              { 'border-red-500 group-hover:border-red-500': isError },
            )}
            onSubmitValue={(value) => {
              const trimmedValue = value?.trim() ?? '';
              handleInputChange(trimmedValue);
            }}
            onChange={(e) => {
              const newValue = e.target.value;
              handleInputChange(newValue);
            }}
            onBlur={(e) => {
              const newValue = e.target.value;
              handleInputBlur(newValue);
            }}
            value={langValue[locale]}
            disabled={disabled}
            maxLength={maxLenght}
          />
        ) : (
          <TextAreaField
            placeholder={placeholder}
            className={cn(
              'hover:ring-0 focus-visible:ring-0 active:ring-0 flex-1 min-w-0 border-0 h-full',
              'border-transparent',
              {
                'border-red-500 group-hover:border-red-500 hover:ring-0 focus:ring-0 focus-visible:ring-0 target:ring-0':
                  isError,
              },
            )}
            rows={4}
            onChange={(val) => {
              const newValue = val;
              handleInputChange(newValue);
            }}
            onBlur={(e) => {
              const newValue = e.target.value;
              handleInputBlur(newValue);
            }}
            value={langValue[locale]}
            disabled={disabled}
            maxLength={maxLenght}
          />
        )}
        {!disabled && (
          <LanguageDialog
            name={name}
            isOpenLang={isOpenLang}
            setIsOpenLang={setIsOpenLang}
            langValue={langValue}
            handleLanguageSubmit={handleLanguageSubmit}
            maxLength={maxLenght!}
            type={type}
            maxHeight={maxHeight}
          />
        )}
      </div>
      {isError && <span className="text-red-500 text-sm not-italic font-normal">{meta.errors[0]}</span>}
    </div>
  );
}

type LanguageDialogProps = {
  name: string;
  isOpenLang: boolean;
  setIsOpenLang: (open: boolean) => void;
  langValue: LanguageValue;
  handleLanguageSubmit: (value: LanguageValue) => void;
  maxLength: number | false;
  type?: string;
  maxHeight?: boolean;
};
const LanguageDialog = ({
  name,
  isOpenLang,
  setIsOpenLang,
  langValue,
  handleLanguageSubmit,
  maxLength,
  type,
  maxHeight = false,
}: LanguageDialogProps) => (
  <Dialog
    onOpenChange={(open) => {
      if (!langValue.vi.length) {
        setIsOpenLang(false);
      } else {
        setIsOpenLang(open);
      }
    }}
    open={isOpenLang}
  >
    <DialogTrigger asChild>
      <Button
        size="md"
        icon
        variant="neutral"
        outline
        className={cn(
          'border-0 border-l border-gray-200 ring-0 outline-0 hover:bg-white active:bg-white active:ring-0 focus:ring-0 flex-shrink-0 w-[52px] h-[42px]',
          { 'h-[104px]': type === 'textarea' && maxHeight === false },
          { 'h-full': type === 'textarea' && maxHeight === true },
        )}
      >
        <TranslateIcon className="text-gray-700" size={16} />
      </Button>
    </DialogTrigger>
    <DialogPortal>
      <DialogOverlay />
      <DialogContent className={cn('p-0 border-none gap-0 max-w-[640px]')}>
        <DialogTitle className="hidden"></DialogTitle>
        <DialogDescription className="hidden"></DialogDescription>
        <FormLang
          name="languageField"
          defaultValue={langValue}
          onCloseModal={() => setIsOpenLang(false)}
          onSubmit={handleLanguageSubmit}
          maxLength={maxLength}
          type={type}
        />
      </DialogContent>
    </DialogPortal>
  </Dialog>
);

const FormLang = ({
  name,
  defaultValue,
  onCloseModal,
  onSubmit,
  maxLength,
  type,
}: {
  name: string;
  defaultValue: LanguageValue;
  onCloseModal: () => void;
  onSubmit: (value: LanguageValue) => void;
  maxLength: number | false;
  type?: string;
}) => {
  const intl = useIntl();
  const ruleMaxLength = maxLength
    ? {
        max: maxLength,
        message: intl.formatMessage(messageErrorCommon.fieldMax, {
          max: maxLength,
        }),
      }
    : undefined;
  const [openConfirmLang, setOpenConfirmLang] = useState(false);
  const [dataLang, setDataLang] = useState<LanguageValue | undefined>(undefined);
  const [formLang] = Form.useForm();

  const handleSubmit = (value: { [key: string]: LanguageValue }) => {
    const objectKey = value[name];
    const getValueFields = formLang.getFieldsValue() as {
      [key: string]: LanguageValue;
    };

    const languageField = getValueFields[name];

    // validateLanguageField({ languageField })
    const allEmpty = Object.values(languageField).every((value) => value === '');
    if (allEmpty) {
      const fieldViLang = {
        name: [name, 'vi'],
        errors: [
          capitalizeFirstLetter(
            intl.formatMessage(messageErrorCommon.fieldRequired, {
              field: intl.formatMessage({
                defaultMessage: 'Nội dung',
                id: 'components.ui.input-language.768903020',
              }),
            }),
          ),
        ],
      };
      const fieldEnLang = {
        name: [name, 'en'],
        errors: [
          capitalizeFirstLetter(
            intl.formatMessage(messageErrorCommon.fieldRequired, {
              field: intl.formatMessage({
                defaultMessage: 'Nội dung',
                id: 'components.ui.input-language.768903020',
              }),
            }),
          ),
        ],
      };

      const error: any = [fieldViLang, fieldEnLang].filter(Boolean);
      return formLang.setFields(error);
    } else if (Object.values(languageField).some((value) => value === '')) {
      setOpenConfirmLang(true);
      setDataLang(objectKey);
      return;
    }

    onSubmit(objectKey);
    onCloseModal();
  };

  return (
    <>
      <Form
        onFinish={handleSubmit}
        name="lang-form"
        form={formLang}
        onFinishFailed={(err) => console.log({ err })}
        initialValues={{
          [name]: defaultValue,
        }}
      >
        <div className="flex items-center gap-3 py-3 pl-6 pr-3 h-[64px] border-b border-gray-200 active:ring-0">
          <span className="flex-1 text-lg font-semibold text-gray-800">
            <FormattedMessage defaultMessage="Thiết lập ngôn ngữ" id="components.ui.input-language.1488782352" />
          </span>
          <Button className="flex-shrink-0" type="submit">
            <FormattedMessage defaultMessage="Lưu thiết lập" id="components.ui.input-language.216897238" />
          </Button>
          <Button icon variant="gray" className="flex-shrink-0" onClick={onCloseModal} type="button">
            <XIcon size={20} />
          </Button>
        </div>
        <div className="h-auto bg-gray-100 p-4 rounded-b-lg">
          <div className="bg-white flex flex-col rounded-lg">
            <div className="border-gray-200 flex flex-row items-center p-2">
              <div className="w-[150px] px-2 pt-2 text-gray-800 text-sm font-semibold leading-[18px]">
                <FormattedMessage defaultMessage="Ngôn ngữ" id="components.ui.input-language.1782945719" />
              </div>
              <div className=" w-[430px] px-2 pt-2 text-gray-800 text-sm font-semibold leading-[18px]">
                <FormattedMessage defaultMessage="Nội dung" id="components.ui.input-language.768903020" />
              </div>
            </div>
            <div className="h-auto border-gray-200 flex p-2">
              <div className="w-[40px] h-full py-2 pl-2 pr-0">
                <Image src="/images/languages/vi-flag.svg" alt="flag-vi" width={28} height={20} priority />
              </div>
              <div className="h-full w-[112px] py-2 px-0 text-gray-700 text-sm font-medium">
                <FormattedMessage defaultMessage="Tiếng Việt" id="components.ui.input-language.1225600157" />
              </div>
              <div className="h-full w-[385px] p-1">
                <Form.Field name={[name, 'vi']} rules={[ruleMaxLength!]}>
                  {(control, meta) => {
                    const isError = (meta.touched || meta.validated || meta.validating) && meta.errors.length > 0;
                    return (
                      <div>
                        {type === 'input' ? (
                          <Input
                            placeholder={intl.formatMessage({
                              defaultMessage: 'Nhập nội dung',
                              id: 'components.ui.input-language.901769905',
                            })}
                            onSubmitValue={(value) => control.onChange(value?.trim() || '')}
                            variant={isError ? 'error' : 'default'}
                            className={cn('focus:ring-0 focus-visible:ring-0 target:ring-0', {
                              'hover:ring-0 focus:ring-0': isError,
                            })}
                            {...control}
                          />
                        ) : (
                          <TextAreaField
                            rows={4}
                            className={cn('flex-1 min-w-0 py-2 px-3', {
                              'border-red-500 group-hover:border-red-500 hover:ring-0': isError,
                            })}
                            onChange={(value) => control.onChange(value?.trim() || '')}
                            placeholder={intl.formatMessage({
                              defaultMessage: 'Nhập nội dung',
                              id: 'components.ui.input-language.901769905',
                            })}
                            {...control}
                          />
                        )}
                        {isError && (
                          <span className="text-red-500 text-sm not-italic font-normal">{meta.errors[0]}</span>
                        )}
                      </div>
                    );
                  }}
                </Form.Field>
              </div>
              <div className="w-[55px] h-auto flex justify-center">
                <FlagIcon size={32} className="text-primary-500" />
              </div>
            </div>
            <div className="h-auto border-gray-200 flex p-2">
              <div className="w-[40px] h-full py-2 pl-2 pr-0">
                <Image src="/images/languages/en-flag.svg" alt="flag-vi" width={28} height={20} priority />
              </div>
              <div className="h-full w-[112px] py-2 pl-0 pr-2 text-gray-700 text-sm font-medium">English</div>
              <div className="h-full w-[385px] p-1">
                <Form.Field name={[name, 'en']} rules={[ruleMaxLength!]}>
                  {(control, meta) => {
                    const isError = (meta.touched || meta.validated || meta.validating) && meta.errors.length > 0;
                    return (
                      <div>
                        {type === 'input' ? (
                          <Input
                            placeholder={intl.formatMessage({
                              defaultMessage: 'Nhập nội dung',
                              id: 'components.ui.input-language.901769905',
                            })}
                            onSubmitValue={(value) => control.onChange(value?.trim() || '')}
                            variant={isError ? 'error' : 'default'}
                            className="focus:ring-0 focus-visible:ring-0 target:ring-0"
                            {...control}
                          />
                        ) : (
                          <TextAreaField
                            rows={4}
                            placeholder={intl.formatMessage({
                              defaultMessage: 'Nhập nội dung',
                              id: 'components.ui.input-language.901769905',
                            })}
                            className={cn('flex-1 min-w-0', {
                              'border-red-500 group-hover:border-red-500 hover:ring-0': isError,
                            })}
                            onChange={(value) => control.onChange(value?.trim() || '')}
                            {...control}
                          />
                        )}
                        {isError && (
                          <span className="text-red-500 text-sm not-italic font-normal">{meta.errors[0]}</span>
                        )}
                      </div>
                    );
                  }}
                </Form.Field>
              </div>
              <div className="w-[55px] h-full"></div>
            </div>
          </div>
        </div>
      </Form>
      <Dialog open={openConfirmLang}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className={cn('p-0 border-none gap-0 max-w-[350px] ')}>
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ConfirmChangeLanguage
              data={dataLang}
              onClose={() => {
                setOpenConfirmLang(false);
              }}
              onSubmit={onSubmit}
              onCloseModalLang={onCloseModal}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
};
