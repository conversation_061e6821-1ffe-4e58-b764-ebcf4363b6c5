/**
 * Comprehensive error handling and recovery mechanisms for iframe operations
 */

import { iframeLogger, logCSSOperation, logIframeOperation } from './logger';

export interface ErrorRecoveryConfig {
  maxRetries: number;
  retryDelay: number;
  exponentialBackoff: boolean;
  fallbackCSS?: string;
  enableFallbackRender: boolean;
  timeouts: {
    initialization: number;
    cssInjection: number;
    recovery: number;
  };
}

export interface IframeErrorContext {
  operation: string;
  error: Error;
  retryCount: number;
  timestamp: Date;
  iframeState?: any;
  additionalData?: any;
}

export interface RecoveryResult {
  success: boolean;
  action: 'retry' | 'fallback' | 'abort';
  message: string;
  nextRetryDelay?: number;
}

export class IframeErrorHandler {
  private config: ErrorRecoveryConfig;
  private errorHistory: IframeErrorContext[] = [];

  constructor(config: Partial<ErrorRecoveryConfig> = {}) {
    this.config = {
      maxRetries: 3,
      retryDelay: 1000,
      exponentialBackoff: true,
      enableFallbackRender: true,
      timeouts: {
        initialization: 10000,
        cssInjection: 8000,
        recovery: 5000,
      },
      ...config,
    };
  }

  /**
   * Handle iframe initialization errors
   */
  handleInitializationError(error: Error, retryCount: number, additionalData?: any): RecoveryResult {
    const context: IframeErrorContext = {
      operation: 'initialization',
      error,
      retryCount,
      timestamp: new Date(),
      additionalData,
    };

    this.errorHistory.push(context);
    logIframeOperation.error('Iframe initialization error', error, additionalData);

    // Check if we should retry
    if (retryCount < this.config.maxRetries) {
      const nextRetryDelay = this.calculateRetryDelay(retryCount);
      
      logIframeOperation.retry(retryCount + 1, this.config.maxRetries, 'initialization failed');
      
      return {
        success: false,
        action: 'retry',
        message: `Retrying initialization (attempt ${retryCount + 1}/${this.config.maxRetries})`,
        nextRetryDelay,
      };
    }

    // Max retries reached, use fallback if enabled
    if (this.config.enableFallbackRender) {
      iframeLogger.warn('iframe', 'Max retries reached, using fallback render');
      return {
        success: false,
        action: 'fallback',
        message: 'Using fallback rendering due to initialization failure',
      };
    }

    // Abort if no fallback available
    return {
      success: false,
      action: 'abort',
      message: 'Iframe initialization failed and no fallback available',
    };
  }

  /**
   * Handle CSS injection errors
   */
  handleCSSError(error: Error, retryCount: number, additionalData?: any): RecoveryResult {
    const context: IframeErrorContext = {
      operation: 'css_injection',
      error,
      retryCount,
      timestamp: new Date(),
      additionalData,
    };

    this.errorHistory.push(context);
    logCSSOperation.injectionError(error, additionalData);

    // For CSS errors, we're more lenient and try fallback CSS first
    if (this.config.fallbackCSS) {
      logCSSOperation.fallbackApplied('CSS injection failed');
      return {
        success: true, // Consider it successful if we can apply fallback
        action: 'fallback',
        message: 'Applied fallback CSS due to injection failure',
      };
    }

    // If no fallback CSS, try retry
    if (retryCount < this.config.maxRetries) {
      const nextRetryDelay = this.calculateRetryDelay(retryCount);
      
      return {
        success: false,
        action: 'retry',
        message: `Retrying CSS injection (attempt ${retryCount + 1}/${this.config.maxRetries})`,
        nextRetryDelay,
      };
    }

    // Continue without CSS if all else fails
    iframeLogger.warn('css', 'Proceeding without complete CSS injection');
    return {
      success: true, // Don't block iframe for CSS issues
      action: 'fallback',
      message: 'Proceeding with incomplete CSS injection',
    };
  }

  /**
   * Handle scaling calculation errors
   */
  handleScalingError(error: Error, dimensions: any): RecoveryResult {
    const context: IframeErrorContext = {
      operation: 'scaling',
      error,
      retryCount: 0,
      timestamp: new Date(),
      additionalData: dimensions,
    };

    this.errorHistory.push(context);
    iframeLogger.error('scaling', 'Scaling calculation error', dimensions, error);

    // For scaling errors, always use fallback (scale: 1, center position)
    return {
      success: true,
      action: 'fallback',
      message: 'Using default scaling due to calculation error',
    };
  }

  /**
   * Handle timeout errors
   */
  handleTimeoutError(operation: string, timeout: number, additionalData?: any): RecoveryResult {
    const error = new Error(`Operation timeout: ${operation} (${timeout}ms)`);
    const context: IframeErrorContext = {
      operation: `${operation}_timeout`,
      error,
      retryCount: 0,
      timestamp: new Date(),
      additionalData,
    };

    this.errorHistory.push(context);
    logIframeOperation.timeout(operation, timeout);

    // Handle different timeout scenarios
    switch (operation) {
      case 'initialization':
        return this.handleInitializationError(error, 0, additionalData);
      
      case 'css_injection':
        // For CSS timeout, apply fallback and continue
        if (this.config.fallbackCSS) {
          logCSSOperation.fallbackApplied('CSS injection timeout');
          return {
            success: true,
            action: 'fallback',
            message: 'Applied fallback CSS due to injection timeout',
          };
        }
        return {
          success: true,
          action: 'fallback',
          message: 'Proceeding without complete CSS due to timeout',
        };
      
      default:
        return {
          success: false,
          action: 'abort',
          message: `Operation ${operation} timed out`,
        };
    }
  }

  /**
   * Calculate retry delay with optional exponential backoff
   */
  private calculateRetryDelay(retryCount: number): number {
    if (!this.config.exponentialBackoff) {
      return this.config.retryDelay;
    }

    // Exponential backoff: delay * (2 ^ retryCount)
    return this.config.retryDelay * Math.pow(2, retryCount);
  }

  /**
   * Get error statistics for debugging
   */
  getErrorStatistics(): {
    totalErrors: number;
    errorsByOperation: { [operation: string]: number };
    recentErrors: IframeErrorContext[];
    mostCommonError: string | null;
  } {
    const errorsByOperation: { [operation: string]: number } = {};
    const errorMessages: { [message: string]: number } = {};

    this.errorHistory.forEach(context => {
      errorsByOperation[context.operation] = (errorsByOperation[context.operation] || 0) + 1;
      errorMessages[context.error.message] = (errorMessages[context.error.message] || 0) + 1;
    });

    // Find most common error
    let mostCommonError: string | null = null;
    let maxCount = 0;
    Object.entries(errorMessages).forEach(([message, count]) => {
      if (count > maxCount) {
        maxCount = count;
        mostCommonError = message;
      }
    });

    // Get recent errors (last 10)
    const recentErrors = this.errorHistory.slice(-10);

    return {
      totalErrors: this.errorHistory.length,
      errorsByOperation,
      recentErrors,
      mostCommonError,
    };
  }

  /**
   * Clear error history
   */
  clearErrorHistory(): void {
    this.errorHistory = [];
  }

  /**
   * Check if iframe is in a recoverable state
   */
  isRecoverable(iframe: HTMLIFrameElement | null): boolean {
    if (!iframe) return false;

    try {
      // Basic checks for iframe health
      const doc = iframe.contentDocument;
      if (!doc) return false;

      // Check if document is in a valid state
      if (doc.readyState === 'complete' || doc.readyState === 'interactive') {
        return true;
      }

      return false;
    } catch (error) {
      iframeLogger.error('iframe', 'Error checking iframe recoverability', { error });
      return false;
    }
  }

  /**
   * Attempt to recover iframe to a working state
   */
  async attemptRecovery(iframe: HTMLIFrameElement, fallbackCSS?: string): Promise<boolean> {
    try {
      iframeLogger.info('iframe', 'Attempting iframe recovery');

      const doc = iframe.contentDocument;
      if (!doc) {
        throw new Error('Cannot access iframe document');
      }

      // Clear existing content
      doc.head.innerHTML = '';
      doc.body.innerHTML = '';

      // Apply minimal CSS for basic functionality
      const minimalCSS = fallbackCSS || `
        * {
          box-sizing: border-box;
          margin: 0;
          padding: 0;
        }
        body {
          font-family: system-ui, -apple-system, sans-serif;
          line-height: 1.5;
        }
        .error-fallback {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100vh;
          background: #f9fafb;
          color: #374151;
        }
      `;

      const styleElement = doc.createElement('style');
      styleElement.textContent = minimalCSS;
      doc.head.appendChild(styleElement);

      iframeLogger.info('iframe', 'Iframe recovery completed');
      return true;
    } catch (error) {
      iframeLogger.error('iframe', 'Iframe recovery failed', { error });
      return false;
    }
  }
}

// Default error handler instance
export const defaultIframeErrorHandler = new IframeErrorHandler();

// Utility functions for common error scenarios
export const errorRecoveryUtils = {
  /**
   * Create a safe iframe initialization function with error handling
   */
  safeIframeInit: async (
    iframe: HTMLIFrameElement,
    initFunction: () => Promise<void>,
    errorHandler: IframeErrorHandler = defaultIframeErrorHandler
  ): Promise<boolean> => {
    let retryCount = 0;
    
    while (retryCount <= errorHandler['config'].maxRetries) {
      try {
        await initFunction();
        return true;
      } catch (error) {
        const result = errorHandler.handleInitializationError(
          error as Error,
          retryCount,
          { iframe: iframe.src }
        );

        if (result.action === 'abort') {
          return false;
        }

        if (result.action === 'fallback') {
          return await errorHandler.attemptRecovery(iframe);
        }

        if (result.action === 'retry' && result.nextRetryDelay) {
          await new Promise(resolve => setTimeout(resolve, result.nextRetryDelay));
          retryCount++;
        }
      }
    }

    return false;
  },

  /**
   * Create a safe CSS injection function with error handling
   */
  safeCSSInjection: async (
    doc: Document,
    injectionFunction: () => Promise<any>,
    fallbackCSS?: string,
    errorHandler: IframeErrorHandler = defaultIframeErrorHandler
  ): Promise<boolean> => {
    try {
      await injectionFunction();
      return true;
    } catch (error) {
      const result = errorHandler.handleCSSError(
        error as Error,
        0,
        { documentReady: doc.readyState }
      );

      if (result.action === 'fallback' && fallbackCSS) {
        try {
          const styleElement = doc.createElement('style');
          styleElement.textContent = fallbackCSS;
          doc.head.appendChild(styleElement);
          return true;
        } catch (fallbackError) {
          iframeLogger.error('css', 'Fallback CSS application failed', { fallbackError });
          return false;
        }
      }

      return result.success;
    }
  },

  /**
   * Create a safe scaling calculation function with error handling
   */
  safeScaleCalculation: (
    calculateFunction: () => any,
    fallbackScale: any = { scale: 1, translateX: 0, translateY: 0 },
    errorHandler: IframeErrorHandler = defaultIframeErrorHandler
  ): any => {
    try {
      const result = calculateFunction();
      
      // Validate result
      if (
        typeof result.scale !== 'number' ||
        !isFinite(result.scale) ||
        isNaN(result.scale) ||
        result.scale <= 0
      ) {
        throw new Error('Invalid scale calculation result');
      }

      return result;
    } catch (error) {
      const recoveryResult = errorHandler.handleScalingError(
        error as Error,
        { fallbackScale }
      );

      if (recoveryResult.action === 'fallback') {
        return fallbackScale;
      }

      return fallbackScale;
    }
  },
};