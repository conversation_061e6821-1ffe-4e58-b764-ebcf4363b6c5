import { ILedDetailData } from '@/types';
import { create } from 'zustand';

type LedStoreState = {
  redirectTo: string;
  isLogined: boolean;
  isPublic: boolean;
  isInitConfigured: boolean;
  dataLedDetail: ILedDetailData | null;
};

type LedStoreAction = {
  setIsLogined: (isLogined: LedStoreState['isLogined']) => void;
  setIsPublic: (isShared: LedStoreState['isPublic']) => void;
  setIsInitConfigured: (isInitConfigured: LedStoreState['isInitConfigured']) => void;
  setDataLedDetail: (dataLedDetail: LedStoreState['dataLedDetail']) => void;
  setRedirectTo: (redirectTo: LedStoreState['redirectTo']) => void;
};

const useLedStores = create<LedStoreState & LedStoreAction>((set) => ({
  isPublic: false,
  setIsPublic: (isPublic: boolean) => set(() => ({ isPublic })),

  isInitConfigured: false,
  setIsInitConfigured: (isInitConfigured: boolean) => set(() => ({ isInitConfigured })),

  dataLedDetail: null,
  setDataLedDetail: (dataLedDetail: any) => set(() => ({ dataLedDetail })),

  isLogined: false,
  setIsLogined: (isLogined: boolean) => set(() => ({ isLogined })),

  redirectTo: '',
  setRedirectTo: (redirectTo: string) => set(() => ({ redirectTo })),
}));

export { useLedStores };

