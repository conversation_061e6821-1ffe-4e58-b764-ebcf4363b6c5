import { ISlide, ISubSlide, SlideConfigDto } from '@/types/slide';

/**
 * Navigation utilities for handling sub-slide navigation logic
 */

export interface INavigationState {
  currentSlideIndex: number;
  currentSubSlideIndex: number;
  visibleSlides: SlideConfigDto[];
}

export interface INavigationResult {
  slideIndex: number;
  subSlideIndex: number;
  hasChanged: boolean;
}

/**
 * Calculates the next slide/sub-slide position
 * Logic: advance sub-slide first, then main slide
 */
export const calculateNextPosition = (state: INavigationState): INavigationResult => {
  const { currentSlideIndex, currentSubSlideIndex, visibleSlides } = state;

  // Handle empty slides array
  if (!visibleSlides || visibleSlides.length === 0) {
    return {
      slideIndex: 0,
      subSlideIndex: 0,
      hasChanged: false,
    };
  }

  // Handle invalid current slide index
  if (currentSlideIndex < 0 || currentSlideIndex >= visibleSlides.length) {
    return {
      slideIndex: 0,
      subSlideIndex: 0,
      hasChanged: true,
    };
  }

  const currentSlide = visibleSlides[currentSlideIndex];
  if (!currentSlide) {
    return {
      slideIndex: 0,
      subSlideIndex: 0,
      hasChanged: true,
    };
  }

  const visibleSubSlides = currentSlide.subSlides?.filter((subSlide) => subSlide.show) || [];
  const hasSubSlides = visibleSubSlides.length > 0;

  // If current slide has sub-slides and we're not at the last sub-slide
  if (hasSubSlides && currentSubSlideIndex < visibleSubSlides.length - 1) {
    return {
      slideIndex: currentSlideIndex,
      subSlideIndex: currentSubSlideIndex + 1,
      hasChanged: true,
    };
  }

  // Move to next main slide
  if (currentSlideIndex < visibleSlides.length - 1) {
    return {
      slideIndex: currentSlideIndex + 1,
      subSlideIndex: 0,
      hasChanged: true,
    };
  }

  // At the end - loop back to first slide (or stay at current position)
  return {
    slideIndex: 0,
    subSlideIndex: 0,
    hasChanged: currentSlideIndex !== 0 || currentSubSlideIndex !== 0,
  };
};

/**
 * Calculates the previous slide/sub-slide position
 * Logic: go to last sub-slide of previous slide
 */
export const calculatePrevPosition = (state: INavigationState): INavigationResult => {
  const { currentSlideIndex, currentSubSlideIndex, visibleSlides } = state;

  // Handle empty slides array
  if (!visibleSlides || visibleSlides.length === 0) {
    return {
      slideIndex: 0,
      subSlideIndex: 0,
      hasChanged: false,
    };
  }

  // Handle invalid current slide index
  if (currentSlideIndex < 0 || currentSlideIndex >= visibleSlides.length) {
    return {
      slideIndex: 0,
      subSlideIndex: 0,
      hasChanged: true,
    };
  }

  // If we're not at the first sub-slide of current slide
  if (currentSubSlideIndex > 0) {
    return {
      slideIndex: currentSlideIndex,
      subSlideIndex: currentSubSlideIndex - 1,
      hasChanged: true,
    };
  }

  // Move to previous main slide
  if (currentSlideIndex > 0) {
    const prevSlide = visibleSlides[currentSlideIndex - 1];
    const visibleSubSlides = prevSlide.subSlides?.filter((subSlide) => subSlide.show) || [];
    const lastSubSlideIndex = Math.max(0, visibleSubSlides.length - 1);

    return {
      slideIndex: currentSlideIndex - 1,
      subSlideIndex: lastSubSlideIndex,
      hasChanged: true,
    };
  }

  // At the beginning - loop to last slide and its last sub-slide
  const lastSlideIndex = visibleSlides.length - 1;
  const lastSlide = visibleSlides[lastSlideIndex];
  const visibleSubSlides = lastSlide.subSlides?.filter((subSlide) => subSlide.show) || [];
  const lastSubSlideIndex = Math.max(0, visibleSubSlides.length - 1);

  return {
    slideIndex: lastSlideIndex,
    subSlideIndex: lastSubSlideIndex,
    hasChanged: currentSlideIndex !== lastSlideIndex || currentSubSlideIndex !== lastSubSlideIndex,
  };
};

/**
 * Validates if a slide/sub-slide position is valid
 */
export const isValidPosition = (
  slideIndex: number,
  subSlideIndex: number,
  visibleSlides: SlideConfigDto[],
): boolean => {
  // Handle empty or invalid slides array
  if (!visibleSlides || visibleSlides.length === 0) {
    return false;
  }

  if (slideIndex < 0 || slideIndex >= visibleSlides.length) {
    return false;
  }

  const slide = visibleSlides[slideIndex];
  if (!slide) {
    return false;
  }

  const visibleSubSlides = slide.subSlides?.filter((subSlide) => subSlide.show) || [];

  if (visibleSubSlides.length === 0) {
    return subSlideIndex === 0;
  }

  return subSlideIndex >= 0 && subSlideIndex < visibleSubSlides.length;
};

/**
 * Gets the current visible sub-slide for a given position
 */
export const getCurrentSubSlide = (
  slideIndex: number,
  subSlideIndex: number,
  visibleSlides: SlideConfigDto[],
): ISubSlide | null => {
  if (!isValidPosition(slideIndex, subSlideIndex, visibleSlides)) {
    return null;
  }

  const slide = visibleSlides[slideIndex];
  const visibleSubSlides = slide.subSlides?.filter((subSlide) => subSlide.show) || [];

  if (visibleSubSlides.length === 0 || subSlideIndex >= visibleSubSlides.length) {
    return null;
  }

  return visibleSubSlides[subSlideIndex];
};

/**
 * Calculates total number of navigation steps (slides + sub-slides)
 */
export const getTotalNavigationSteps = (visibleSlides: SlideConfigDto[]): number => {
  if (!visibleSlides || visibleSlides.length === 0) {
    return 0;
  }

  return visibleSlides.reduce((total, slide) => {
    if (!slide) return total;
    const visibleSubSlides = slide.subSlides?.filter((subSlide) => subSlide.show) || [];
    return total + Math.max(1, visibleSubSlides.length);
  }, 0);
};

/**
 * Calculates current navigation step position (0-based)
 */
export const getCurrentNavigationStep = (
  slideIndex: number,
  subSlideIndex: number,
  visibleSlides: SlideConfigDto[],
): number => {
  if (!visibleSlides || visibleSlides.length === 0) {
    return 0;
  }

  if (slideIndex < 0 || slideIndex >= visibleSlides.length) {
    return 0;
  }

  let step = 0;

  for (let i = 0; i < slideIndex && i < visibleSlides.length; i++) {
    const slide = visibleSlides[i];
    if (!slide) continue;
    const visibleSubSlides = slide.subSlides?.filter((subSlide) => subSlide.show) || [];
    step += Math.max(1, visibleSubSlides.length);
  }

  if (slideIndex < visibleSlides.length) {
    const currentSlide = visibleSlides[slideIndex];
    if (currentSlide) {
      const visibleSubSlides = currentSlide.subSlides?.filter((subSlide) => subSlide.show) || [];

      if (visibleSubSlides.length > 0 && subSlideIndex >= 0 && subSlideIndex < visibleSubSlides.length) {
        step += subSlideIndex;
      }
    }
  }

  return step;
};

/**
 * Edge case handlers
 */
export const handleEdgeCases = {
  /**
   * Handle case when there are no visible slides
   */
  noVisibleSlides: (): INavigationResult => ({
    slideIndex: 0,
    subSlideIndex: 0,
    hasChanged: false,
  }),

  /**
   * Handle case when there's only one slide
   */
  singleSlide: (slide: ISlide, currentSubSlideIndex: number, direction: 'next' | 'prev'): INavigationResult => {
    const visibleSubSlides = slide.subSlides?.filter((subSlide) => subSlide.show) || [];

    if (visibleSubSlides.length === 0) {
      return {
        slideIndex: 0,
        subSlideIndex: 0,
        hasChanged: false,
      };
    }

    if (direction === 'next') {
      const nextSubSlideIndex = currentSubSlideIndex < visibleSubSlides.length - 1 ? currentSubSlideIndex + 1 : 0; // Loop back to first sub-slide

      return {
        slideIndex: 0,
        subSlideIndex: nextSubSlideIndex,
        hasChanged: nextSubSlideIndex !== currentSubSlideIndex,
      };
    } else {
      const prevSubSlideIndex = currentSubSlideIndex > 0 ? currentSubSlideIndex - 1 : visibleSubSlides.length - 1; // Loop to last sub-slide

      return {
        slideIndex: 0,
        subSlideIndex: prevSubSlideIndex,
        hasChanged: prevSubSlideIndex !== currentSubSlideIndex,
      };
    }
  },

  /**
   * Handle case when slide has no sub-slides
   */
  noSubSlides: (currentSlideIndex: number, visibleSlides: ISlide[], direction: 'next' | 'prev'): INavigationResult => {
    if (direction === 'next') {
      const nextSlideIndex = currentSlideIndex < visibleSlides.length - 1 ? currentSlideIndex + 1 : 0; // Loop back to first slide

      return {
        slideIndex: nextSlideIndex,
        subSlideIndex: 0,
        hasChanged: nextSlideIndex !== currentSlideIndex,
      };
    } else {
      const prevSlideIndex = currentSlideIndex > 0 ? currentSlideIndex - 1 : visibleSlides.length - 1; // Loop to last slide

      return {
        slideIndex: prevSlideIndex,
        subSlideIndex: 0,
        hasChanged: prevSlideIndex !== currentSlideIndex,
      };
    }
  },
};
