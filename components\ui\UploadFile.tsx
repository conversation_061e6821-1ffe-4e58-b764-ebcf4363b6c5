import { useState, useRef, ChangeEvent, useEffect } from 'react';
import { ImageIcon, TrashIcon, VideoIcon, CheckCircleIcon } from '@phosphor-icons/react';
import { defineMessages, FormattedMessage, useIntl } from 'react-intl';
import {
  <PERSON>ton,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  toast,
} from 'ui-components';
import Image from 'next/image';
import CircleProgressIcon from './CircleProgressIcon';
import envConfig from '@/constants/env';
import { capitalizeFirstLetter } from '@/utils';

type FileTypeConfig = {
  accept: string;
  maxSize: number | { value: number; unit: 'B' | 'KB' | 'MB' | 'GB' };
  customValidation?: (file: File) => string | null; // return error message if invalid
  getText?: (opts: { isUploading: boolean; selectedFile: File | null; previewUrl: string | null | undefined }) => {
    label: string;
    description: string;
  };
  getIcon?: (opts: {
    isUploading: boolean;
    selectedFile: File | null;
    previewUrl: string | null | undefined;
    isSuccess: boolean;
  }) => React.ReactNode;
};

export type UploadFileProps = {
  onChange?: (file: File | string | null) => void;
  accept?: string;
  maxSize?: number | { value: number; unit: 'B' | 'KB' | 'MB' | 'GB' };
  value?: string | null | File;
  fileTypeConfigs?: Record<string, FileTypeConfig>; // key: file type (image/pdf/docx...)
  fileType?: string; // key in fileTypeConfigs
};

const definedMessage = defineMessages({
  messageUploadFail: {
    defaultMessage: 'Không thể tải lên tệp tin. Vui lòng thử lại sau.',
    id: 'components.ui.UploadFile.1640628908',
  },
  messageUploadFailSize: {
    defaultMessage: 'Vui lòng chọn tệp tin có tổng dung lượng nhỏ hơn {maxSize}.',
    id: 'components.ui.UploadFile.2113771008',
  },
  messageUploadFailType: {
    defaultMessage: 'Định dạng tệp tin không hỗ trợ. Vui lòng chọn tệp tin khác.',
    id: 'components.ui.UploadFile.926063465',
  },
});

export default function UploadFile({
  onChange: onFileChange,
  accept = 'image/jpeg,image/png,image/svg+xml',
  maxSize = 512, // Default 512KB
  value,
  fileTypeConfigs,
  fileType = 'image',
}: UploadFileProps) {
  const intl = useIntl();
  const [selectedFile, setSelectedFile] = useState<File | null>(value instanceof File ? value : null);
  const [previewUrl, setPreviewUrl] = useState<string | null | undefined>(typeof value === 'string' ? value : null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isOpenModalDel, setIsOpenModalDel] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (typeof value !== 'object') {
      setPreviewUrl(value);
    }
  }, [value]);

  useEffect(() => {
    return () => {
      if (previewUrl && selectedFile && previewUrl !== value) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl, selectedFile, value]);

  // Determine config for current fileType
  const config = fileTypeConfigs?.[fileType] || {
    accept,
    maxSize,
  };

  // Helper: convert size to bytes
  function sizeToBytes(size: number | { value: number; unit: 'B' | 'KB' | 'MB' | 'GB' }) {
    if (typeof size === 'number') return size * 1024; // default KB
    const { value, unit } = size;
    switch (unit) {
      case 'B':
        return value;
      case 'KB':
        return value * 1024;
      case 'MB':
        return value * 1024 * 1024;
      case 'GB':
        return value * 1024 * 1024 * 1024;
      default:
        return value * 1024;
    }
  }
  // Helper: get display string for max size
  function maxSizeDisplay(size: number | { value: number; unit: 'B' | 'KB' | 'MB' | 'GB' }) {
    if (typeof size === 'number') return `${size}KB`;
    return `${size.value}${size.unit}`;
  }

  // Helper: check if file is video
  function isVideoFile(file: File): boolean {
    return file.type.startsWith('video/');
  }

  // Helper: check if file is image
  function isImageFile(file: File): boolean {
    return file.type.startsWith('image/');
  }

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Nếu có customValidation thì chỉ dùng customValidation
    if (config.customValidation) {
      const err = config.customValidation(file);
      if (err) {
        toast({
          type: 'error',
          title: err,
          options: {
            position: 'top-center',
          },
        });
        return;
      }
    } else {
      // Check file type
      if (config.accept && !config.accept.split(',').includes(file.type)) {
        toast({
          type: 'error',
          title: intl.formatMessage(definedMessage.messageUploadFailType),
          options: {
            position: 'top-center',
          },
        });
        return;
      }
      // Check file size
      if (config.maxSize && file.size > sizeToBytes(config.maxSize)) {
        toast({
          type: 'error',
          title: intl.formatMessage(definedMessage.messageUploadFailSize, { maxSize: maxSizeDisplay(config.maxSize) }),
          options: {
            position: 'top-center',
          },
        });
        return;
      }
    }

    // Simulate upload process
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress updates
      const totalSteps = 10;
      for (let step = 1; step <= totalSteps; step++) {
        await new Promise((resolve) => setTimeout(resolve, 50));
        setUploadProgress(Math.floor((step / totalSteps) * 100));
      }

      // Create preview URL for image files
      if (isImageFile(file)) {
        const url = URL.createObjectURL(file);
        setPreviewUrl(url);
      } else {
        setPreviewUrl(null);
      }

      setSelectedFile(file);
      if (onFileChange) {
        onFileChange(file);
      }
    } catch (err) {
      toast({
        type: 'error',
        title: intl.formatMessage(definedMessage.messageUploadFail),
        options: {
          position: 'top-center',
        },
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const onDelete = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    if (onFileChange) {
      onFileChange(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    setIsOpenModalDel(false);
  };

  // Check if upload is successful
  const isSuccess = !isUploading && (Boolean(selectedFile) || Boolean(previewUrl));
  const hasFile = Boolean(selectedFile) || Boolean(previewUrl);

  // Dynamic text for label/description
  const text = config.getText
    ? config.getText({ isUploading, selectedFile, previewUrl })
    : {
        label: intl.formatMessage({ defaultMessage: 'Ảnh đại diện', id: 'components.ui.UploadFile.745843351' }),
        description: isUploading
          ? intl.formatMessage({
              defaultMessage: 'Đang tải lên ảnh đại diện...',
              id: 'components.ui.UploadFile.606995948',
            })
          : selectedFile || previewUrl
          ? intl.formatMessage({ defaultMessage: 'Đã tải lên ảnh đại diện.', id: 'components.ui.UploadFile.778747531' })
          : intl.formatMessage({
              defaultMessage: 'Bạn có thể tải lên 1 tệp tin với định dạng JPG, PNG hoặc SVG.',
              id: 'components.ui.UploadFile.1047290012',
            }),
      };

  // Dynamic icon rendering
  const renderIcon = () => {
    if (config.getIcon) {
      return config.getIcon({ isUploading, selectedFile, previewUrl, isSuccess });
    }

    if (isUploading) {
      return (
        <div className="absolute inset-0 flex items-center justify-center">
          <CircleProgressIcon value={uploadProgress} />
        </div>
      );
    }

    if (isSuccess) {
      // Show success icon with different colors based on file type
      if (selectedFile && isVideoFile(selectedFile)) {
        return (
          <div className="h-full w-full bg-primary-50 flex items-center justify-center">
            <VideoIcon size={24} className="text-primary-500 flex-shrink-0" weight="regular" />
          </div>
        );
      }
    }

    // Default icon based on file type
    if ((config.accept && config.accept.includes('video/')) || fileType === 'video') {
      return <VideoIcon size={24} className="text-gray-500 flex-shrink-0" />;
    }

    return <ImageIcon size={24} className="text-gray-500 flex-shrink-0" />;
  };

  return (
    <div className="flex flex-col gap-2 w-full">
      <div className="flex flex-row items-center gap-2 rounded-xl py-2 pl-2 pr-3 w-full h-auto border border-gray-200">
        <div className="flex flex-row gap-4 flex-1 min-w-0 items-center">
          <div className="h-[52px] w-[52px] flex-shrink-0 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden relative">
            {isUploading ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <CircleProgressIcon value={uploadProgress} />
              </div>
            ) : !!previewUrl ? (
              !!selectedFile && !isImageFile(selectedFile) ? (
                <div className="flex flex-col items-center justify-center w-full h-full">
                  {isVideoFile(selectedFile) ? <VideoIcon size={24} className="text-gray-500 flex-shrink-0" /> : null}
                  <span className="text-xs text-gray-500 text-center px-1">{selectedFile?.name || previewUrl}</span>
                </div>
              ) : fileType === 'image' ? (
                <Image
                  src={
                    previewUrl && previewUrl?.startsWith('blob')
                      ? previewUrl
                      : `${envConfig.MEDIA_ENDPOINT}/${previewUrl ?? ''}`
                  }
                  alt="File preview"
                  width={52}
                  height={52}
                  className="object-contain w-full h-full"
                  priority
                />
              ) : (
                <div className="h-full w-full bg-primary-50 flex items-center justify-center">
                  <VideoIcon size={24} className="text-primary-500 flex-shrink-0" weight="regular" />
                </div>
              )
            ) : (
              renderIcon()
            )}
          </div>
          <div className="flex flex-col gap-1">
            <span className="font-medium text-sm text-gray-700">{text.label}</span>
            <span className="font-normal text-xs text-gray-500">{text.description}</span>
          </div>
        </div>
        {hasFile ? (
          <Button
            variant="gray"
            className="flex-shrink-0 text-red-500"
            type="button"
            icon
            onClick={() => {
              setIsOpenModalDel(true);
            }}
            disabled={isUploading}
          >
            <TrashIcon size={20} weight="regular" className="text-current" />
          </Button>
        ) : (
          <Button
            variant="gray"
            className="flex-shrink-0 text-primary-500 w-[68px]"
            type="button"
            onClick={handleButtonClick}
            disabled={isUploading}
          >
            <FormattedMessage defaultMessage="Chọn" id="components.ui.UploadFile.2340486" />
            <input
              hidden
              type="file"
              ref={fileInputRef}
              accept={config.accept}
              onChange={handleFileChange}
              disabled={isUploading}
            />
          </Button>
        )}
      </div>
      <Dialog open={isOpenModalDel} onOpenChange={setIsOpenModalDel}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[420px] h-auto rounded-xl sm:rounded-xl">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <div className="flex flex-col">
              <div className="flex flex-col justify-center items-center p-5">
                <Image
                  src={`/images/commons/delete-image.svg`}
                  alt="deleted-image"
                  width={200}
                  height={200}
                  className="h-50 w-50 object-contain flex-shrink-0 aspect-square"
                  priority
                />
                <div className="flex-1 min-h-0 flex flex-col gap-1 items-center justify-center">
                  <span className="text-center font-semibold text-lg leading-[27px] text-gray-800">
                    {capitalizeFirstLetter(
                      intl.formatMessage(
                        {
                          defaultMessage: 'Xác nhận xóa {label}',
                          id: 'components.ui.UploadFile.62368309',
                        },
                        {
                          label: text.label,
                        },
                      ),
                    )}
                  </span>
                  <span className="text-center font-normal text-sm leading-[21px] text-gray-700">
                    {capitalizeFirstLetter(
                      intl.formatMessage(
                        {
                          defaultMessage:
                            'Bạn chắc chắn muốn xóa {label} này? Vui lòng xác nhận nếu bạn muốn tiếp tục.',
                          id: 'components.ui.UploadFile.136903079',
                        },
                        {
                          label: text.label,
                        },
                      ),
                      '?',
                    )}
                  </span>
                </div>
              </div>
              <div className="flex flex-row justify-end gap-3 p-[14px] border-t border-gray-200">
                <Button
                  type="button"
                  variant="gray"
                  className="flex-1 text-primary-500 text-sm font-normal"
                  onClick={() => setIsOpenModalDel(false)}
                >
                  <FormattedMessage defaultMessage="Hủy bỏ" id="components.ui.UploadFile.780985299" />
                </Button>
                <Button type="button" variant="danger" className="flex-1 text-sm font-normal" onClick={onDelete}>
                  <FormattedMessage defaultMessage="Xác nhận" id="components.ui.UploadFile.559188735" />
                </Button>
              </div>
            </div>
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </div>
  );
}
