type CircleProgressIconProps = {
  size?: number;
  strokeWidth?: number;
  value: number; // từ 0 đến 100
};

const CircleProgressIcon = ({ size = 20, strokeWidth = 2, value }: CircleProgressIconProps) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const offset = circumference - (value / 100) * circumference;

  return (
    <svg width={size} height={size} className="transform -rotate-90">
      {/* Vòng nền xám */}
      <circle
        stroke="#E5E7EB" // gray-200
        fill="transparent"
        strokeWidth={strokeWidth}
        r={radius}
        cx={size / 2}
        cy={size / 2}
      />
      {/* Vòng màu xanh dần theo tiến trình */}
      <circle
        stroke="#3B82F6" // blue-500 (tailwind primary)
        fill="transparent"
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeDasharray={circumference}
        strokeDashoffset={offset}
        r={radius}
        cx={size / 2}
        cy={size / 2}
      />
    </svg>
  );
};

export default CircleProgressIcon;
