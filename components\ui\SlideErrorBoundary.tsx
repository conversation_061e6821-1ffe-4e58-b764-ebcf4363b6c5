import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Warning, ArrowClockwise } from '@phosphor-icons/react';
import { Button } from 'ui-components';

interface Props {
  children: ReactNode;
  slideIndex: number;
  slideId?: string;
  onError?: (error: Error, errorInfo: ErrorInfo, slideIndex: number) => void;
  onRetry?: (slideIndex: number) => void;
  onSkip?: (slideIndex: number) => void;
  fallbackContent?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  retryCount: number;
}

class SlideErrorBoundary extends Component<Props, State> {
  private maxRetries = 2;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { slideIndex, slideId, onError } = this.props;
    
    console.error(`Slide Error (index: ${slideIndex}, id: ${slideId}):`, error, errorInfo);
    
    this.setState({ error });

    // Call the onError callback if provided
    if (onError) {
      onError(error, errorInfo, slideIndex);
    }

    // Log error details for debugging
    console.error('Slide error details:', {
      slideIndex,
      slideId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
    });
  }

  handleRetry = () => {
    const { retryCount } = this.state;
    const { slideIndex, onRetry } = this.props;
    
    if (retryCount < this.maxRetries) {
      console.log(`Retrying slide ${slideIndex} (attempt ${retryCount + 1}/${this.maxRetries})`);
      
      this.setState({
        hasError: false,
        error: null,
        retryCount: retryCount + 1,
      });

      // Call the onRetry callback if provided
      if (onRetry) {
        onRetry(slideIndex);
      }
    }
  };

  handleSkip = () => {
    const { slideIndex, onSkip } = this.props;
    
    console.log(`Skipping slide ${slideIndex} due to error`);
    
    if (onSkip) {
      onSkip(slideIndex);
    }
  };

  render() {
    if (this.state.hasError) {
      // If custom fallback content is provided, use it
      if (this.props.fallbackContent) {
        return this.props.fallbackContent;
      }

      // Default error slide UI
      return (
        <div className="flex flex-col justify-center items-center w-full h-full bg-gray-900 text-white p-8">
          <div className="text-center max-w-md">
            <div className="flex justify-center mb-4">
              <Warning size={48} className="text-yellow-500" />
            </div>
            
            <h3 className="text-lg font-semibold mb-2">
              Slide Error
            </h3>
            
            <p className="text-gray-300 text-sm mb-6">
              This slide could not be displayed due to an error. You can retry loading the slide or skip to continue the presentation.
            </p>

            <div className="flex gap-3 justify-center">
              {this.state.retryCount < this.maxRetries && (
                <Button
                  size="sm"
                  type='button'
                  onClick={this.handleRetry}
                  className="flex items-center gap-2"
                >
                  <ArrowClockwise size={14} />
                  Retry
                </Button>
              )}
              
              <Button
                size="sm"
                type='button'
                variant="outline"
                onClick={this.handleSkip}
                className="text-white border-white hover:bg-white hover:text-gray-900"
              >
                Skip Slide
              </Button>
            </div>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-xs text-gray-400 hover:text-gray-300">
                  Error Details (Development)
                </summary>
                <pre className="mt-2 text-xs bg-gray-800 p-2 rounded overflow-auto max-h-24 text-left">
                  {this.state.error.message}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default SlideErrorBoundary;