# Ignore node_modules (sẽ được cài đặt lại trong container)
node_modules
npm-debug.log
yarn-error.log
.pnpm-debug.log

# Ignore các thư mục build cũ
.next
out

# Ignore các tệp c<PERSON>u hình editor
.vscode/
.idea/

# Ignore môi trường cục bộ
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Ignore các file hệ thống
.DS_Store
Thumbs.db

# Ignore các file log
logs
*.log

# Ignore các tệp liên quan đến Git
.git
.gitignore

# Ignore các file liên quan đến testing
coverage
*.spec.js
*.test.js
__tests__

# Ignore các file tạm thời
*.swp
*.swo
*.tmp
*.temp

# Ignore các tệp Dockerfile không cần thiết
Dockerfile.*
.dockerignore