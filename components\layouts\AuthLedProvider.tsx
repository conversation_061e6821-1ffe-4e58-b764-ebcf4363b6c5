'use client';

import React, { PropsWithChildren, useEffect, useState } from 'react';
import { useLedConfigStores, useLedStores } from '@/stores';
import { ILedDetailData } from '@/types';
import { useCreateClient } from '@/api/useCreateClient';
import { useParams } from 'next/navigation';

type InitDataProps = {
  isAuthenticated: boolean;
  isConfigured: boolean;
  isShared: boolean;
  ledDetail: ILedDetailData;
};
type AuthLedProviderProps = PropsWithChildren<{
  initData: InitDataProps;
}>;

export const AuthLedProvider = ({ children, initData }: AuthLedProviderProps) => {
  const store = useLedStores((store) => store);
  const { setIsPublic, setIsInitConfigured, setDataLedDetail, setIsLogined } = store;
  const { setDataConfig, setStationTypes } = useLedConfigStores((store) => store);
  const { led_key } = useParams() as { led_key: string };
  const { ledConfig } = useCreateClient();
  const [loading, setLoading] = useState(true);

  const { data: dataConfig, isLoading: isLoadingConfig, isFetching } = ledConfig.getLedConfig(led_key);

  const { data: stationTypes, isLoading: isLoadingStationType } = ledConfig.getStationTypes();

  useEffect(() => {
    if (initData.isShared) {
      setIsLogined(true);
    }
    setIsPublic(initData.isShared);
    setIsInitConfigured(initData.isConfigured);
    setDataLedDetail(initData.ledDetail);
    setLoading(false);
  }, [initData.isShared, initData.isConfigured, initData.ledDetail]);

  useEffect(() => {
    const data = dataConfig?.data
      ? { ...dataConfig?.data, password: initData?.ledDetail.password, name: initData?.ledDetail.name }
      : null;
    setDataConfig(data);
  }, [isLoadingConfig, isFetching]);

  useEffect(() => {
    const data = stationTypes?.data?.filter((station) =>
      initData.ledDetail.dataStations.map((item) => item.stationTypeKey).includes(station.key),
    );
    setStationTypes(data || []);
  }, [isLoadingStationType]);

  if (loading || isLoadingConfig || isLoadingStationType) return null;

  return <>{children}</>;
};
