import axios, { AxiosError, AxiosInstance, AxiosResponse, CreateAxiosDefaults } from 'axios';
import { DataResponse, DataWithPagination, FilterOptions, PaginationOptions, SortValue } from './type';

declare module 'axios' {
  export interface AxiosRequestConfig {
    _retry?: boolean;
  }
}

export class BaseClient<T> {
  protected api: AxiosInstance;
  moduleName = 'BaseClient';

  constructor(baseURL: string, configs?: CreateAxiosDefaults) {
    const defaultConfig: CreateAxiosDefaults = {
      baseURL,
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
      },
      ...configs,
    };

    this.api = axios.create(defaultConfig);

    this.api.interceptors.response.use(
      (response: AxiosResponse) => response.data,
      (error: AxiosError) => {
        return Promise.reject(error.response?.data as Error);
      },
    );
  }

  find(
    filter: FilterOptions<T>,
    pagination: PaginationOptions,
    sort: SortValue<T>,
  ): Promise<T[] | DataResponse<DataWithPagination<T> | T>> {
    return this.api.post(`/`, {
      filter,
      pagination,
      sort,
    });
  }

  getById(id: string | number): Promise<T | DataResponse<T>> {
    return this.api.get(`/${id}`);
  }

  create(data: Partial<T>): Promise<T | DataResponse<T>> {
    return this.api.post(`/`, data);
  }

  update(id: string | number, data: Partial<T>): Promise<T | DataResponse<T>> {
    return this.api.put(`/${id}`, data);
  }

  delete(id: string | number): Promise<T | DataResponse<T>> {
    return this.api.delete(`/${id}`);
  }

  updateConfig(config: Partial<CreateAxiosDefaults>): void {
    // Update the axios instance configuration
    Object.assign(this.api.defaults, config);

    // If headers are being updated
    if (config.headers) {
      // Get existing headers
      const existingHeaders = this.api.defaults.headers || {};

      // Get existing common headers
      const existingCommonHeaders = existingHeaders.common || {};

      // Create new headers object
      const newHeaders = {
        ...existingHeaders,
        common: {
          ...existingCommonHeaders,
          ...config.headers,
        },
      };

      // Update headers
      this.api.defaults.headers = newHeaders as any;
    }
  }
}
