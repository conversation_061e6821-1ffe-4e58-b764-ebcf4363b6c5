import { useEffect, useRef, useState } from 'react';

type Props = {
  ratio: number; // ví dụ: 4 / 3
  children: React.ReactNode;
};

export default function FitRatioBox({ ratio, children }: Props) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const observer = new ResizeObserver(([entry]) => {
      const { width: cw, height: ch } = entry.contentRect;

      const contentRatio = ratio;

      let width = cw;
      let height = cw / contentRatio;

      if (height > ch) {
        height = ch;
        width = ch * contentRatio;
      }

      setDimensions({ width, height });
    });

    observer.observe(container);
    return () => observer.disconnect();
  }, [ratio]);

  return (
    <div ref={containerRef} className="w-full h-full relative overflow-hidden">
      <div
        className="absolute top-1/2 left-1/2"
        style={{
          width: `${dimensions.width}px`,
          height: `${dimensions.height}px`,
          transform: 'translate(-50%, -50%)',
        }}
      >
        {children}
      </div>
    </div>
  );
}
