# Sử dụng Node.js base image
FROM node:18-alpine AS base
RUN apk add --no-cache git


FROM base AS deps

# Thiết lập thư mục làm việc
WORKDIR /app

# Copy và cài đặt dependencies
COPY package.json package-lock.json ./

RUN sed -i 's|git+ssh://**************:|https://ci-deploy:<EMAIL>/|g' package.json package-lock.json
RUN sed -i 's|git+ssh://git|https://ci-deploy:gldt-V8BkNwp_-6_2RYrPDXGH|g' package.json package-lock.json

RUN npm install

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
# COPY . .

ENV NEXT_SHARP_PATH=/app/node_modules/sharp

RUN npm run build

FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

RUN mkdir .next
RUN chown nextjs:nodejs .next

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/node_modules/@img ./node_modules/@img
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3004

ENV PORT=3004
ENV NEXT_SHARP_PATH=/app/node_modules/sharp

CMD HOSTNAME="0.0.0.0" node server.js
