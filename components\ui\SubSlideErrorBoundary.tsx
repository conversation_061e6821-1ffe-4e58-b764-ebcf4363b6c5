import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorType, ErrorSeverity, createError, errorHandler } from '@/utils/errorHandling';

interface Props {
  children: ReactNode;
  slideId?: string;
  subSlideId?: string;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}

/**
 * Error boundary specifically designed for SubSlide rendering errors
 * Provides graceful fallbacks and recovery mechanisms
 */
class SubSlideErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;
  private retryTimeout: NodeJS.Timeout | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error with comprehensive context
    const subSlidesError = createError.rendering(`SubSlide rendering error: ${error.message}`, {
      slideId: this.props.slideId,
      subSlideId: this.props.subSlideId,
      componentStack: errorInfo.componentStack,
      errorStack: error.stack,
      retryCount: this.state.retryCount,
      props: {
        slideId: this.props.slideId,
        subSlideId: this.props.subSlideId,
      },
    });

    errorHandler.handleError(subSlidesError);

    // Update state with error info
    this.setState({
      errorInfo,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Attempt automatic recovery for certain error types
    this.attemptRecovery(error);
  }

  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
  }

  private attemptRecovery = (error: Error) => {
    // Only attempt recovery if we haven't exceeded max retries
    if (this.state.retryCount >= this.maxRetries) {
      console.warn('Max retry attempts reached for SubSlide error recovery');
      return;
    }

    // Determine if error is recoverable
    const isRecoverable = this.isRecoverableError(error);

    if (isRecoverable) {
      console.debug(`Attempting recovery for SubSlide error (attempt ${this.state.retryCount + 1}/${this.maxRetries})`);

      // Wait before retrying to avoid rapid error loops
      this.retryTimeout = setTimeout(() => {
        this.handleRetry();
      }, 1000 * (this.state.retryCount + 1)); // Exponential backoff
    }
  };

  private isRecoverableError = (error: Error): boolean => {
    const recoverablePatterns = [/network/i, /timeout/i, /loading/i, /fetch/i, /temporary/i];

    const nonRecoverablePatterns = [/syntax/i, /reference/i, /type/i, /undefined.*function/i, /cannot read prop/i];

    const errorMessage = error.message.toLowerCase();

    // Check if error is explicitly non-recoverable
    if (nonRecoverablePatterns.some((pattern) => pattern.test(errorMessage))) {
      return false;
    }

    // Check if error is explicitly recoverable
    if (recoverablePatterns.some((pattern) => pattern.test(errorMessage))) {
      return true;
    }

    // Default to recoverable for unknown errors (with retry limit)
    return true;
  };

  private handleRetry = () => {
    this.setState((prevState) => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1,
    }));

    console.debug('SubSlide error boundary: Retrying render');
  };

  private handleManualRetry = () => {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
    this.handleRetry();
  };

  private getErrorSeverity = (error: Error): 'low' | 'medium' | 'high' => {
    const errorMessage = error.message.toLowerCase();

    if (errorMessage.includes('critical') || errorMessage.includes('fatal')) {
      return 'high';
    }

    if (errorMessage.includes('warning') || errorMessage.includes('deprecated')) {
      return 'low';
    }

    return 'medium';
  };

  private renderErrorFallback = () => {
    const { slideId, subSlideId } = this.props;
    const { error, retryCount } = this.state;
    const severity = error ? this.getErrorSeverity(error) : 'medium';

    return (
      <div className="flex items-center justify-center w-full h-full bg-gradient-to-br from-red-900/20 to-red-800/20 backdrop-blur-sm">
        <div className="text-center p-6 bg-black/50 rounded-lg border border-red-500/30 max-w-md">
          {/* Error icon */}
          <div className="mb-4">
            <div
              className={`w-12 h-12 mx-auto rounded-full flex items-center justify-center ${
                severity === 'high' ? 'bg-red-600' : severity === 'medium' ? 'bg-yellow-600' : 'bg-orange-600'
              }`}
            >
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
          </div>

          {/* Error message */}
          <h3 className="text-lg font-semibold text-white mb-2">SubSlide Rendering Error</h3>

          <p className="text-gray-300 text-sm mb-4">
            {error?.message || 'An unexpected error occurred while rendering this subSlide'}
          </p>

          {/* Error details */}
          <div className="text-xs text-gray-400 mb-4 space-y-1">
            {slideId && <p>Slide: {slideId}</p>}
            {subSlideId && <p>SubSlide: {subSlideId}</p>}
            {retryCount > 0 && (
              <p>
                Retry attempts: {retryCount}/{this.maxRetries}
              </p>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex gap-2 justify-center">
            {retryCount < this.maxRetries && (
              <button
                onClick={this.handleManualRetry}
                type="button"
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors"
              >
                Retry
              </button>
            )}
            <button
              onClick={() => {
                // Skip this subSlide by triggering next slide
                if (typeof window !== 'undefined' && (window as any).presentationStores) {
                  (window as any).presentationStores.getState().nextSlide();
                }
              }}
              type="button"
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-md transition-colors"
            >
              Skip
            </button>
          </div>

          {/* Developer info (only in development) */}
          {process.env.NODE_ENV === 'development' && error && (
            <details className="mt-4 text-left">
              <summary className="text-xs text-gray-400 cursor-pointer hover:text-gray-300">Developer Info</summary>
              <pre className="mt-2 text-xs text-red-300 bg-black/30 p-2 rounded overflow-auto max-h-32">
                {error.stack}
              </pre>
            </details>
          )}
        </div>
      </div>
    );
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided, otherwise use default
      return this.props.fallback || this.renderErrorFallback();
    }

    return this.props.children;
  }
}

export default SubSlideErrorBoundary;
