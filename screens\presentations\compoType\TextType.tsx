import InputLanguage from '@/components/ui/InputLanguage/InputLanguage';
import { FormattedMessage, useIntl } from 'react-intl';
import { Form, FormInstance } from 'ui-components';
import { ISlide } from '@/types/slide';

type FormData = {
  width: number;
  height: number;
  presentations: ISlide[];
};

type PropsTextType = {
  focusedIndex: number;
  form: FormInstance<FormData>;
};
export const TextType = ({ focusedIndex, form }: PropsTextType) => {
  const intl = useIntl();

  return (
    <>
      <div className="flex flex-col gap-2">
        <span className="font-medium text-sm text-gray-700">
          <FormattedMessage defaultMessage="Nội dung" id="screens.presentations.FooterConfigSlide.768903020" />
        </span>
        <div className="h-full w-full">
          <Form.Field
            name={['presentations', focusedIndex, 'content', 'metadata', 'content']}
            valuePropName="value"
            trigger="onBlur"
          >
            <InputLanguage
              placeholder={intl.formatMessage({
                defaultMessage: 'Nhập nội dung',
                id: 'screens.presentations.FooterConfigSlide.901769905',
              })}
              name="title"
              type="textarea"
            />
          </Form.Field>
        </div>
      </div>
    </>
  );
};
