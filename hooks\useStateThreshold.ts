'use client';

import { useLedConfigStores } from '@/stores';
import { IMeasuring } from '@/types';
import { EStatus, RealTimeMetadata } from '@/types/slide';
import { useEffect, useState } from 'react';

export const useStateThreshold = (
  dataItem: IMeasuring & { value: number | null },
  metadata: RealTimeMetadata
) => {
  const [isStateThreshold, setIsStateThreshold] = useState<EStatus>(EStatus.Good);
  const [styleThreshold, setStyeThreshold] = useState<React.CSSProperties>({});
  const dataConfig = useLedConfigStores((store) => store.dataConfig);

  useEffect(() => {
    if (
      (dataItem?.minLimit !== null && dataItem?.value !== null && dataItem?.value < dataItem?.minLimit) ||
      (dataItem?.maxLimit !== null && dataItem?.value !== null && dataItem?.value > dataItem?.maxLimit)
    ) {
      if (metadata?.showOptions?.isThresholdExceeded) {
        setIsStateThreshold(EStatus.Exceeded);
        setStyeThreshold({
          backgroundColor: dataConfig?.init.colors.color_over_limit,
        });
      } else {
        setIsStateThreshold(EStatus.Good);
        setStyeThreshold({
          backgroundColor:
            metadata?.layout?.template === 'grid'
              ? dataConfig?.slide_config_default.background
              : 'transparent',
        });
      }
    } else if (
      (dataItem?.minTend !== null && dataItem?.value !== null && dataItem?.value < dataItem?.minTend) ||
      (dataItem?.maxTend !== null && dataItem?.value !== null && dataItem?.value > dataItem?.maxTend)
    ) {
      if (metadata?.showOptions?.isThresholdApproaching) {
        setIsStateThreshold(EStatus.ExceededPreparing);
        setStyeThreshold({
          backgroundColor: dataConfig?.init.colors.color_near_limit,
        });
      } else {
        setIsStateThreshold(EStatus.Good);
        setStyeThreshold({
          backgroundColor:
            metadata?.layout?.template === 'grid'
              ? dataConfig?.slide_config_default.background
              : 'transparent',
        });
      }
    } else {
      setIsStateThreshold(EStatus.Good);
      setStyeThreshold({
        backgroundColor:
          metadata?.layout?.template === 'grid'
            ? dataConfig?.slide_config_default.background
            : 'transparent',
      });
    }
  }, [dataItem, metadata, dataConfig]);

  return { isStateThreshold, styleThreshold };
};
