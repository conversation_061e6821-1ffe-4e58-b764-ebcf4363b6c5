import { ContentLayout, GridView } from '@/constants';
import { Color, ILanguageData } from '.';

export interface IHeaderSlides {
  show: boolean;
  avatar?: string;
  title?: ILanguageData;
  sub_title?: ILanguageData;
  showOptions: {
    showAvatar: boolean;
    showTitle: boolean;
    showSubTitle: boolean;
    isDataSynced: boolean;
  };
  fontSize: {
    auto: boolean;
    config: {
      title: number;
      description: number;
      datetime: number;
    };
  };
}

export type IFooterSlides = {
  type: 'banner-text' | 'timestamp';
  show: boolean;
  timestamp?: {
    content?: ILanguageData;
    showOptions?: {
      showTime: boolean;
      showDate: boolean;
    };
  };
  bannerText?: {
    content?: ILanguageData;
  };
  fontSize: {
    auto: boolean;
    config: {
      content: number;
    };
  };
  config: {
    background: string;
    color: string;
  };
};

export type ContentType = 'realtime' | 'histories' | 'images' | 'video' | 'text' | null;

export enum EContentType {
  realtime = 'realtime',
  histories = 'histories',
  images = 'images',
  video = 'video',
  text = 'text',
}

export type IFontSize = {
  /**
   * default auto là true, tự động auto font-size
   */
  auto: boolean;
  /**
   * config font-size theo ý của người dùng
   * gồm: headerTable, stationName, measureCode, limitThreshold, measureValue
   * measureUnit, legendWarning, parameterCode, value, unit, text
   */
  config: {
    headerTable?: number;
    stationName: number;
    measureCode: number;
    limitThreshold: number;
    measureValue: number;
    measureUnit: number;
    legendWarning: number;
    parameterCode?: number;
  };
};

export type RealtimeContent = {
  /**
   * Loại trạm
   */
  stationType: string;
  /**
   * Trạm
   */
  station: string;
  /**
   * Thông số quan trắc
   */
  measure: string[];
  /**
   * Bố cục
   */
  layout: {
    /**
     * template = grid | table
     */
    template: keyof typeof ContentLayout;
    /**
     * bố cục dạng grid
     */
    grid: {
      /**
       * kiểu chữ
       */
      fontSize: IFontSize;
      /**
       * kiểu grid type = 'general' | 'custom'
       * hỗ trợ custom theo từng device 'desktop' | 'tablet' | 'mobile'
       */
      view: GridView;
      /**
       * kiểu grid type = 'general'
       */
      general: IConfigLayoutData;
      /**
       * kiểu grid type = 'custom'
       */
      custom: {
        /**
         * hỗ trợ custom theo device 'desktop'
         * có số hàng và số cột theo device
         */
        desktop: IConfigLayoutData;
        /**
         * hỗ trợ custom theo device 'tablet'
         * có số hàng và số cột theo device
         */
        tablet: IConfigLayoutData;
        /**
         * hỗ trợ custom theo device 'mobile'
         * có số hàng và số cột theo device
         */
        mobile: IConfigLayoutData;
      };
    };
    /**
     * bố cục dạng table
     */
    table: {
      /**
       * kiểu chữ
       */
      fontSize: IFontSize;
      /**
       * kiểu grid type = 'general' | 'custom'
       * hỗ trợ custom theo từng device 'desktop' | 'tablet' | 'mobile'
       */
      view: GridView;
      /**
       * kiểu grid type = 'general'
       */
      general: IConfigLayoutData;
      /**
       * kiểu grid type = 'custom'
       */
      custom: {
        /**
         * hỗ trợ custom theo device 'desktop'
         * có số hàng và số cột theo device
         */
        desktop: IConfigLayoutData;
        /**
         * hỗ trợ custom theo device 'tablet'
         * có số hàng và số cột theo device
         */
        tablet: IConfigLayoutData;
        /**
         * hỗ trợ custom theo device 'mobile'
         * có số hàng và số cột theo device
         */
        mobile: IConfigLayoutData;
      };
    };
  };
  /**
   * Cấu hình hiển thị
   */
  showOptions: {
    /**
     * Tên trạm quan trắc
     */
    isShowStationName: boolean;
    /**
     * Cảnh báo vượt ngưỡng
     */
    isThresholdExceeded: boolean;
    /**
     * Cảnh báo chuẩn bị vượt
     */
    isThresholdApproaching: boolean;
  };
  /**
   * config camera
   */
  camera: {
    /**
     * hiển thị camera
     */
    isShow: boolean;
    /**
     * danh sách camera
     */
    list: string[];
  };
};

export type VideoContent = {
  video: string | File | null;
};

export enum ETimeRange {
  '7 days' = '7 days',
  '15 days' = '15 days',
  '30 days' = '30 days',
}

export enum ETypeTimeRange {
  yesterday = 'yesterday',
  today = 'today',
}

export type HistoriesContent = {
  /**
   * Loại trạm
   */
  stationType: string;
  /**
   * Trạm
   */
  station: string;
  /**
   * Thông số quan trắc
   */
  measure: string[];
  /**
   * Khoảng thời gian
   */
  timeRange: keyof typeof ETimeRange;
  /**
   * Type timeRange: Hôm qua | hôm nay
   */
  typeTimeRange: keyof typeof ETypeTimeRange;
  /**
   * Tính toán theo giá trị trung bình ngày
   */
  calc: 'average_day';
  /**
   * Bố cục hiển thị: bố cụ chung và bố cục tùy chỉnh
   * bố cục chung: số cột và số dòng
   * bố cục tùy chỉnh theo device: desktop, tablet, mobile. Đều thể hiện số cột và số dòng
   */
  layout: {
    view: GridView;
    general: IConfigLayoutData;
    custom: {
      desktop: IConfigLayoutData;
      mobile: IConfigLayoutData;
      tablet: IConfigLayoutData;
    };
  };
  /**
   * Cấu hình hiển thị
   * Hiển thị thành phần: Tên trạm quan trắc, Đơn vị thông số, Cột đánh giá, Ngưỡng giớ hạn
   * Hiển thị màu cảnh báo giá trị: Cảnh báo chuẩn bị vượt, Cảnh báo vượt ngưỡng
   */
  config: {
    showOptions: {
      isShowStationName: boolean;
      isShowUnit: boolean;
      isShowLimitThreshold: boolean;
      isShowColumnEvaluate: boolean;
    };
    warningColor: {
      isThresholdExceeded: boolean;
      isThresholdApproaching: boolean;
    };
  };
  /**
   * Cấu hình font-size
   */
  fontSize: IFontSize;
};

export enum ImageMode {
  cover = 'cover',
  contain = 'contain',
}

export type ImageContent = {
  images: ({ order: number; url: string | File } | null)[];
  config: IConfigLayoutData;
  mode: keyof typeof ImageMode;
};

export type TextContent = {
  content: ILanguageData;
  fontSize: {
    auto: boolean;
    config: {
      content: number;
    };
  };
  config: {
    background: string;
    color: string;
  };
};

export type IContentSlides = {
  /**
   * Loại dữ liệu
   */
  type: ContentType;
  /**
   * Hiển thị nội dung
   */
  show: boolean;
  /**
   * Metadata của loại dữ liệu
   */
  metadata?: RealtimeContent | VideoContent | HistoriesContent | ImageContent | TextContent;
};

export type IConfigLayoutData = {
  row: number;
  column: number;
  cameraPerPage?: number;
};

export type IGridData = {
  row?: number;
  column?: number;
};

export type IRealTimeContent = {
  type?: 'realtime';
  stationKey?: string;
  measuringKey?: string[];
  dataGrid?: IGridData;
  config?: {
    showStation?: boolean;
    isThresholdExceeded?: boolean;
    isThresholdApproaching?: boolean;
  };
  fontSize?: IFontSize;
};

export type IHistoryContent = Omit<IRealTimeContent, 'type' | 'config'> & {
  type?: 'histories';
  timeRange?: '7 days' | '1 month' | '1 quarter' | '1 year';
  calc?: 'average' | 'max' | 'min';
  item_per_page?: number;
  config?: {
    showStation?: boolean;
    unit?: boolean;
    qcvn?: false | string;
    isThresholdExceeded?: boolean;
    isThresholdApproaching?: boolean;
  };
};

export type IImageContent = {
  type?: 'images';
  dataGrid?: IGridData;
  imageUrls?: string[];
  viewType?: 'grid' | 'slider';
};

export type IVideoContent = {
  type?: 'videos';
  videoUrls?: string;
};

export type ITextContent = {
  type?: 'text';
  content?: string;
  config?: {
    background: Color;
    color: Color;
  };
  fontSize: IFontSize;
};

export interface ISubSlide {
  id: string;
  content: any;
  duration?: number; // specific duration for this sub-slide in milliseconds
  show: boolean;
}

export interface ISlide {
  id: string;
  ledBoardId?: string;
  title: ILanguageData;
  show: boolean;
  header: IHeaderSlides;
  content: IContentSlides;
  footer: IFooterSlides;
  order: number;
  subSlides?: ISubSlide[]; // optional sub-slides for this slide
}

export interface RealTimeMetadata {
  measure: string[];
  station: string;
  stationType: string;
  cameras?: string[];
  showOptions: {
    isShowStationName: boolean;
    isThresholdApproaching: boolean;
    isThresholdExceeded: boolean;
  };
  layout: {
    template: keyof typeof ContentLayout;
    config: {
      view: keyof typeof GridView;
      settings:
        | IConfigLayoutData
        | {
            desktop: IConfigLayoutData;
            mobile: IConfigLayoutData;
            tablet: IConfigLayoutData;
          };
      fontSize: IFontSize;
    };
  };
}

export interface HistoryMetadata {
  measure: string[];
  station: string;
  stationType: string;
  timeRange: keyof typeof ETimeRange;
  typeTimeRange: keyof typeof ETypeTimeRange;
  calc: 'average_day';
  layout: {
    view: keyof typeof GridView;
    settings:
      | IConfigLayoutData
      | {
          desktop: IConfigLayoutData;
          mobile: IConfigLayoutData;
          tablet: IConfigLayoutData;
        };
  };
  config: {
    showOptions: {
      isShowStationName: boolean;
      isShowUnit: boolean;
      isShowLimitThreshold: boolean;
      isShowColumnEvaluate: boolean;
    };
    warningColor: {
      isThresholdExceeded: boolean;
      isThresholdApproaching: boolean;
    };
  };
  fontSize: IFontSize;
}

export interface VideoMetadata {
  video: string | File | null;
}

export interface ImagesMetadata {
  images: ({ order: number; url: string } | null)[];
  config: IConfigLayoutData;
  mode: keyof typeof ImageMode;
}

export interface TextMetadata {
  content: ILanguageData;
  fontSize: {
    auto: boolean;
    config: {
      content: number;
    };
  };
  config: {
    background: string;
    color: string;
  };
}

export interface SlideConfigDto {
  id: string;
  ledBoardId?: string;
  title?: ILanguageData;
  show: boolean;
  header: {
    show: boolean;
    title?: ILanguageData;
    sub_title?: ILanguageData;
    showOptions: {
      showAvatar: boolean;
      showTitle: boolean;
      showSubTitle: boolean;
      isDataSynced: boolean;
    };
    fontSize: {
      auto: boolean;
      config: {
        title: number;
        description: number;
        datetime: number;
      };
    };
  };
  content: {
    show: boolean;
    type?: ContentType;
    metadata: RealTimeMetadata | VideoMetadata | ImagesMetadata | HistoryMetadata | TextMetadata | null;
  };
  footer: {
    show: boolean;
    type: 'banner-text' | 'timestamp';
    content?: ILanguageData;
    config: {
      background: string;
      color: string;
    };
    fontSize: {
      auto: boolean;
      config: {
        content: number;
      };
    };
  };
  order: number;
  subSlides?: ISubSlide[]; // optional sub-slides for this slide
}

export enum EStatus {
  Good = 'GOOD',
  Exceeded = 'EXCEEDED',
  ExceededPreparing = 'EXCEEDED_PREPARING',
}
