import { dataTypeObject } from '@/constants';
import { cn } from '@/utils/tailwind';
import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import {
  Combobox,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Form,
  FormInstance,
  OptionItemType,
} from 'ui-components';
import { HistoryType, ImageType, RealtimeType, TextType, VideoType } from './compoType';
import { messageContentType } from '@/constants/defineMessages';
import { HistoriesContent, ImageContent, ISlide, RealtimeContent, TextContent } from '@/types/slide';
import {
  initContentHistory,
  initContentImage,
  initContentRealtime,
  initContentText,
  initContentVideo,
} from '@/constants/slide';
import { useLedConfigStores, useLedStores } from '@/stores';
import RealtimeOptions from './compoType/RealtimeOptions';
import ContentChangedBase from '@/components/ui/ContentChangedBase';
import HistoriesOptions from './compoType/HistoriesOptions';
import ImagesOptions from './compoType/ImagesOptions';
import TextOptions from './compoType/TextOptions';

type Props = {
  focusedIndex: number;
  form: FormInstance;
  dataContent: ISlide['content'];
  initFont: {
    grid: {
      [x: string]: number;
    };
    table: {
      [x: string]: number;
    };
    text: {
      [x: string]: number;
    };
  };
};

export default function ContentConfigSlide({
  focusedIndex,
  form,
  dataContent,
  initFont,
  ...props
}: Props & React.HTMLAttributes<HTMLDivElement>) {
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';
  const { stationTypes, dataConfig } = useLedConfigStores((store) => store);
  const dataLedDetail = useLedStores((store) => store.dataLedDetail);
  const currentDataType = dataContent.type;
  const [stationOptions, setStationOptions] = useState<OptionItemType[]>([]);
  const [measuringOptions, setMeasuringOptions] = useState<OptionItemType[]>([]);
  const [cameraOptions, setCameraOptions] = useState<OptionItemType[]>([]);
  const [changeType, onConfirmChangeType] = useState({
    isConfirm: false,
    newType: '',
    oldType: currentDataType,
  });

  const dataTypeOptions = useMemo(() => {
    return [
      {
        title: intl.formatMessage(messageContentType.realtime),
        value: dataTypeObject.realtime,
      },

      {
        title: intl.formatMessage(messageContentType.histories),
        value: dataTypeObject.histories,
      },
      {
        title: intl.formatMessage(messageContentType.images),
        value: dataTypeObject.images,
      },
      {
        title: 'Video',
        value: dataTypeObject.video,
      },
      {
        title: intl.formatMessage(messageContentType.text),
        value: dataTypeObject.text,
      },
    ];
  }, [locale]);

  const stationTypeOptions: OptionItemType[] = useMemo(() => {
    return stationTypes.map((stationType) => {
      return {
        title: stationType.name[locale],
        value: stationType.key,
      };
    });
  }, [stationTypes, intl.locale]);

  const onChangeDataType = useCallback(
    (type: keyof typeof dataTypeObject) => {
      switch (type) {
        case dataTypeObject.realtime:
          if (stationTypeOptions.length > 0) {
            const stationTypeSelected = stationTypeOptions[0].value;

            const stationFilter =
              dataLedDetail?.dataStations.filter((station) => station.stationTypeKey === stationTypeSelected) ?? [];

            const stations = stationFilter.map((station) => {
              return {
                title: station?.name[locale],
                value: station.key,
              };
            });

            setStationOptions(stations ?? []);

            const stationSelected = stations.length > 0 ? stations[0].value : '';

            const measurings = stationFilter
              .find((station) => station.key === stationSelected)
              ?.measuringList.map((measure) => {
                return {
                  title: measure?.name[locale],
                  value: measure?.key,
                };
              });
            setMeasuringOptions(measurings ?? []);
            const measureSelected = measurings?.map((measure) => measure.value) ?? [];
            form.setFieldValue(
              ['presentations', focusedIndex, 'content', 'metadata', 'stationType'],
              stationTypeSelected,
            );
            form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'station'], stationSelected);
            form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'measure'], measureSelected);
            form.setFieldValue(
              ['presentations', focusedIndex, 'content', 'metadata', 'layout'],
              initContentRealtime.layout,
            );
            form.setFieldValue(
              ['presentations', focusedIndex, 'content', 'metadata', 'showOptions'],
              initContentRealtime.showOptions,
            );
            const cameraList = stationFilter
              .find((station) => station.key === stationSelected)
              ?.cameras?.map((camera) => {
                return {
                  title: camera.name,
                  value: camera._id,
                };
              });
            setCameraOptions(cameraList ?? []);
            const cameraSelected = cameraList?.map((camera) => camera.value) ?? [];
            form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'camera'], {
              isShow: false,
              list: cameraSelected,
            });
            form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'layout', 'grid', 'fontSize'], {
              auto: true,
              config: initFont.grid,
            });
            form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'layout', 'table', 'fontSize'], {
              auto: true,
              config: initFont.table,
            });
          }
          break;
        case dataTypeObject.histories:
          if (stationTypeOptions.length > 0) {
            const stationTypeSelected = stationTypeOptions[0].value;
            const stationFilter =
              dataLedDetail?.dataStations.filter((station) => station.stationTypeKey === stationTypeSelected) ?? [];
            const stations = stationFilter.map((station) => {
              return {
                title: station?.name[locale],
                value: station.key,
              };
            });
            setStationOptions(stations ?? []);
            const stationSelected = stations.length > 0 ? stations[0].value : '';
            const measurings = stationFilter
              .find((station) => station.key === stationSelected)
              ?.measuringList.map((measure) => {
                return {
                  title: measure?.name[locale],
                  value: measure?.key,
                };
              });
            setMeasuringOptions(measurings ?? []);
            const measureSelected = measurings?.map((measure) => measure.value) ?? [];
            form.setFieldValue(
              ['presentations', focusedIndex, 'content', 'metadata', 'stationType'],
              stationTypeSelected,
            );
            form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'station'], stationSelected);
            form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'measure'], measureSelected);
            form.setFieldValue(
              ['presentations', focusedIndex, 'content', 'metadata', 'timeRange'],
              initContentHistory.timeRange,
            );
            form.setFieldValue(
              ['presentations', focusedIndex, 'content', 'metadata', 'typeTimeRange'],
              initContentHistory.typeTimeRange,
            );
            form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'calc'], initContentHistory.calc);
            form.setFieldValue(
              ['presentations', focusedIndex, 'content', 'metadata', 'layout'],
              initContentHistory.layout,
            );
            form.setFieldValue(
              ['presentations', focusedIndex, 'content', 'metadata', 'config'],
              initContentHistory.config,
            );
            form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'fontSize'], {
              auto: true,
              config: initFont.table,
            });
          }
          break;
        case dataTypeObject.video:
          form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata'], initContentVideo);
          break;
        case dataTypeObject.images:
          form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata'], initContentImage);
          break;
        case dataTypeObject.text: {
          form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata'], initContentText);
          form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'fontSize'], {
            auto: true,
            config: initFont.text,
          });
          break;
        }
        default:
          form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata'], null);
          break;
      }
    },
    [stationTypeOptions, focusedIndex, dataConfig, initFont],
  );

  const renderDataRealtimeType = useMemo(() => {
    return (
      <RealtimeType
        focusedIndex={focusedIndex}
        form={form}
        type={currentDataType}
        stationOptions={stationOptions}
        measuringOptions={measuringOptions}
        cameraOptions={cameraOptions}
        stationTypeOptions={stationTypeOptions}
        dataContent={
          dataContent as {
            metadata: RealtimeContent;
          }
        }
      />
    );
  }, [stationTypeOptions, stationOptions, measuringOptions, cameraOptions, focusedIndex, form, dataContent]);

  const renderDataVideoType = useMemo(() => {
    return <VideoType focusedIndex={focusedIndex} form={form} type={currentDataType} />;
  }, [focusedIndex, form, currentDataType]);

  const renderDataHistoryType = useMemo(() => {
    return (
      <HistoryType
        focusedIndex={focusedIndex}
        form={form}
        type={currentDataType}
        stationOptions={stationOptions}
        measuringOptions={measuringOptions}
        stationTypeOptions={stationTypeOptions}
      />
    );
  }, [stationTypeOptions, stationOptions, measuringOptions, dataContent, focusedIndex, form]);

  const renderDataImageType = useMemo(() => {
    return (
      <ImageType
        focusedIndex={focusedIndex}
        dataContent={
          dataContent as {
            metadata: ImageContent;
          }
        }
      />
    );
  }, [focusedIndex, dataContent]);

  useEffect(() => {
    const realtimeMetadata = dataContent.metadata as RealtimeContent | HistoriesContent;
    if (realtimeMetadata) {
      const stationTypeSelected = realtimeMetadata?.stationType || '';

      const stationFilter =
        dataLedDetail?.dataStations.filter((station) => station.stationTypeKey === stationTypeSelected) ?? [];
      const stations = stationFilter.map((station) => {
        return {
          title: station?.name[locale],
          value: station.key,
        };
      });

      setStationOptions(stations ?? []);
      const stationSelected = stations.length > 0 ? realtimeMetadata?.station : '';

      const cameraList = stationFilter
        .find((station) => station.key === stationSelected)
        ?.cameras?.map((camera) => {
          return {
            title: camera.name,
            value: camera._id,
          };
        });
      setCameraOptions(cameraList ?? []);

      const measurings = stationFilter
        .find((station) => station.key === stationSelected)
        ?.measuringList.map((measure) => {
          return {
            title: measure?.name[locale],
            value: measure?.key,
          };
        });
      setMeasuringOptions(measurings ?? []);
    }
  }, [dataContent]);

  const renderDataWithType = useMemo(() => {
    switch (currentDataType) {
      case dataTypeObject.realtime:
        return renderDataRealtimeType;
      case dataTypeObject.histories:
        return renderDataHistoryType;
      case dataTypeObject.images:
        return renderDataImageType;
      case dataTypeObject.video:
        return renderDataVideoType;
      case dataTypeObject.text:
        return <TextType focusedIndex={focusedIndex} form={form} />;
      default:
        return null;
    }
  }, [currentDataType, focusedIndex, form, renderDataRealtimeType, renderDataVideoType, renderDataImageType]);

  return (
    <>
      <div {...props} className={cn('flex-1 min-w-0 flex flex-col gap-4', props.className)}>
        <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
          <span className="font-semibold text-base text-gray-700">
            <FormattedMessage defaultMessage="Cấu hình tổng quan" id="screens.presentations.ConfigSlide.257768898" />
          </span>

          <div className="flex flex-col gap-2">
            <div className="flex flex-row justify-between">
              <span className="font-medium text-sm text-gray-700">
                <FormattedMessage defaultMessage="Loại dữ liệu" id="screens.presentations.ConfigSlide.1454429291" />
              </span>
            </div>

            <div className="h-full w-full">
              <Form.Field name={['presentations', focusedIndex, 'content', 'type']} shouldUpdate>
                {(control) => (
                  <Combobox
                    value={control?.value || ''}
                    onChange={(value) => {
                      if (value === currentDataType) return;
                      return onConfirmChangeType({
                        isConfirm: true,
                        newType: value as keyof typeof dataTypeObject,
                        oldType: currentDataType,
                      });
                    }}
                    options={dataTypeOptions}
                    placeholder={intl.formatMessage({
                      defaultMessage: 'Chọn loại dữ liệu',
                      id: 'screens.presentations.compoType.RealtimeType.1787698267',
                    })}
                    contentClassName="p-1 rounded-xl [&_div.selected]:bg-gray-100 [&_[cmdk-group-items]]:flex [&_[cmdk-group-items]]:flex-col [&_[cmdk-group-items]]:gap-1"
                    itemClassName="bg-white hover:bg-gray-100 data-[selected=true]:bg-gray-100 text-gray-700 cursor-pointer p-2"
                    sideOffset={-80}
                    textEmpty={intl.formatMessage({
                      defaultMessage: 'Danh sách trống',
                      id: 'screens.presentations.compoType.RealtimeType.1597700344',
                    })}
                  />
                )}
              </Form.Field>
            </div>
          </div>
          <Fragment>{renderDataWithType}</Fragment>
        </div>
        {currentDataType === dataTypeObject.realtime && (
          <RealtimeOptions
            focusedIndex={focusedIndex}
            form={form}
            dataContent={dataContent as { metadata: RealtimeContent }}
            initFont={initFont}
          />
        )}
        {currentDataType === dataTypeObject.histories && (
          <HistoriesOptions
            focusedIndex={focusedIndex}
            form={form}
            dataContent={dataContent as { metadata: HistoriesContent }}
            initFont={initFont}
          />
        )}
        {currentDataType === dataTypeObject.images && <ImagesOptions focusedIndex={focusedIndex} />}
        {currentDataType === dataTypeObject.text && (
          <TextOptions
            focusedIndex={focusedIndex}
            form={form}
            dataContent={dataContent as { metadata: TextContent }}
            initFont={initFont}
          />
        )}
      </div>

      <Dialog
        open={changeType.isConfirm}
        onOpenChange={(open) => onConfirmChangeType({ ...changeType, isConfirm: open })}
      >
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="max-w-[420px] p-0">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentChangedBase
              onCancel={() => {
                onConfirmChangeType({ ...changeType, isConfirm: false });
                form.setFieldValue(['presentations', focusedIndex, 'content', 'type'], changeType.oldType);
                onChangeDataType(changeType.oldType as keyof typeof dataTypeObject);
              }}
              onSubmit={() => {
                form.setFieldValue(['presentations', focusedIndex, 'content', 'type'], changeType.newType);
                onChangeDataType(changeType.newType as keyof typeof dataTypeObject);
                onConfirmChangeType({ ...changeType, isConfirm: false });
              }}
              heading={intl.formatMessage({
                defaultMessage: 'Xác nhận thay đổi loại dữ liệu',
                id: 'screens.presentations.ContentConfigSlide.83510683',
              })}
              description={intl.formatMessage({
                defaultMessage:
                  'Bạn có chắc muốn thay đổi loại dữ liệu này không? Các thông tin mà bạn vừa nhập sẽ không được lưu lại.',
                id: 'screens.presentations.ContentConfigSlide.523491325',
              })}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
}
