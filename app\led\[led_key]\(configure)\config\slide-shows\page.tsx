'use client';

import { useCreateClient } from '@/api/useCreateClient';
import GroupBox from '@/components/sessions/GroupBox';
import { NumberInput } from '@/components/ui/NumberInput';
import { PaletteColorPicker } from '@/components/ui/PaletteColorPicker';
import SkeletonComp from '@/components/ui/SkeletonComp';
import { EFontSupport, fontOptions } from '@/constants';
import { usePreventNavigation } from '@/hooks/usePreventNavigation';
import { useLedConfigStores } from '@/stores';
import { hexToRgba } from '@/utils';
import { cn } from '@/utils/tailwind';
import { use, useEffect, useMemo, useState } from 'react';
import { defineMessages, FormattedMessage, useIntl } from 'react-intl';
import { Button, Combobox, Form, Spin, toast } from 'ui-components';

type IFormData = {
  duration: number;
  font_family: EFontSupport;
  background: string;
  color: string;
};

const definedMessages = defineMessages({
  submitSuccess: {
    defaultMessage: 'Đ<PERSON> lưu cấu hình trang trình chiếu thành công.',
    id: 'app.led.[led_key].(configure).config.slide-shows.page.129890065',
  },
  submitFail: {
    defaultMessage: 'Không thể lưu cấu hình trang trình chiếu. Vui lòng thử lại sau.',
    id: 'app.led.[led_key].(configure).config.slide-shows.page.1103570150',
  },
});

export default function LedConfigSlideShow({ params }: { params: Promise<{ led_key: string }> }) {
  const { led_key } = use(params) as { led_key: string };
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const [form] = Form.useForm();
  const intl = useIntl();
  const { ledConfig } = useCreateClient();
  const { mutateAsync: asyncConfigSlide, isPending: penddingConfigSlide } = ledConfig.configSlide({
    invalidateQueries: [
      {
        enable: true,
      },
      {
        enable: true,
        queryKey: ['LedServiceClient', 'getLedConfig', led_key],
        exact: true,
      },
    ],
  });
  const [formChange, setFormChange] = useState(false);

  usePreventNavigation({
    isDirty: formChange,
  });

  const initFormValue: IFormData = useMemo(() => {
    return {
      duration: dataConfig?.slide_config_default.duration || 5,
      font_family: dataConfig?.slide_config_default.font_family || EFontSupport.Inter,
      background: dataConfig?.slide_config_default.background || '#000000',
      color: dataConfig?.slide_config_default.color || '#FFFFFF',
      borderColor: dataConfig?.slide_config_default.borderColor || '#71717A',
    };
  }, []);

  useEffect(() => {
    form?.setFieldsValue(initFormValue);
  }, [form, initFormValue]);

  const onSubmit = async (values: IFormData) => {
    try {
      await asyncConfigSlide([led_key, values]);
      toast({
        type: 'success',
        title: intl.formatMessage(definedMessages.submitSuccess),
        options: {
          position: 'top-center',
        },
      });
      setFormChange(false);
    } catch (error) {
      toast({
        type: 'error',
        title: intl.formatMessage(definedMessages.submitFail),
        options: {
          position: 'top-center',
        },
      });
    }
  };

  return (
    <Form
      form={form}
      initialValues={initFormValue}
      onFinish={onSubmit}
      onFieldsChange={(changedFields, allFields) => {
        const isChange = allFields.some((field) => {
          return field.touched;
        });
        setFormChange(isChange);
      }}
      name="slide-show-config"
      className="flex flex-col h-full items-center"
    >
      <Spin loading={penddingConfigSlide} className="flex flex-col h-full items-center">
        <div className="w-[600px] h-fit max-h-full rounded-3xl flex flex-col gap-4 bg-white py-6">
          <span className="font-semibold text-lg text-gray-700 flex-shrink-0 px-6">
            <FormattedMessage
              defaultMessage="Cấu hình trang trình chiếu"
              id="app.led.[led_key].(configure).config.slide-shows.page.126322537"
            />
          </span>
          <div className="flex-1 min-h-0 max-h-full overflow-auto flex flex-col gap-4 px-6">
            <div className="flex flex-col gap-2">
              <span className="font-medium text-sm text-gray-700">
                <FormattedMessage
                  defaultMessage="Tốc độ chuyển trang (giây)"
                  id="app.led.[led_key].(configure).config.slide-shows.page.1779980518"
                />
              </span>
              <Form.Field name="duration" trigger="onBlurInput">
                <NumberInput
                  placeholder=""
                  precision={0}
                  keyboard
                  controls
                  clearable={false}
                  min={5}
                  max={1000000000}
                />
              </Form.Field>
              <span className="font-normal text-sm text-gray-600">
                <FormattedMessage
                  defaultMessage="Tốc độ chuyển trang tối thiểu được thiết lập là 5 giây."
                  id="app.led.[led_key].(configure).config.slide-shows.page.1674427239"
                />
              </span>
            </div>
            <div className="flex flex-col gap-2">
              <span className="font-medium text-sm text-gray-700">
                <FormattedMessage
                  defaultMessage="Hiển thị mặc định"
                  id="app.led.[led_key].(configure).config.slide-shows.page.1067860744"
                />
              </span>
              <GroupBox>
                <div className="flex flex-row">
                  <div className="h-11 flex-1 min-w-0 p-3 border-r border-gray-200">
                    <span className="font-normal text-sm text-gray-700">
                      <FormattedMessage
                        defaultMessage="Kiểu chữ"
                        id="app.led.[led_key].(configure).config.slide-shows.page.1509228774"
                      />
                    </span>
                  </div>
                  <div className="w-[180px] flex-shrink-0">
                    <Form.Field name="font_family">
                      <Combobox
                        options={fontOptions}
                        placeholder={intl.formatMessage({
                          defaultMessage: 'Chọn kiểu chữ',
                          id: 'app.led.[led_key].(configure).config.slide-shows.page.1227310252',
                        })}
                        triggerClassName="border-none active:ring-0 focus:ring-0 hover:ring-0 hover:bg-gray-50 rounded-none"
                        contentClassName="p-1 rounded-xl [&_div.selected]:bg-gray-100 [&_[cmdk-group-items]]:flex [&_[cmdk-group-items]]:flex-col [&_[cmdk-group-items]]:gap-1"
                        itemClassName="bg-white hover:bg-gray-100 data-[selected=true]:bg-gray-100 text-gray-700 cursor-pointer p-2"
                        sideOffset={-100}
                        customOption={(option) => (
                          <span
                            className={cn(
                              'text-sm font-medium text-gray-700',
                              { 'font-inter': option.value === EFontSupport.Inter },
                              { 'font-montserrat': option.value === EFontSupport.Montserrat },
                              { 'font-tektur': option.value === EFontSupport.Tektur },
                              { 'font-jetbrains-mono': option.value === EFontSupport['JetBrains Mono'] },
                              { 'font-be-vietnam-pro': option.value === EFontSupport['Be Vietnam Pro'] },
                            )}
                          >
                            {option.title}
                          </span>
                        )}
                      />
                    </Form.Field>
                  </div>
                </div>
                <div className="flex flex-row">
                  <div className="h-11 flex-1 min-w-0 p-3 border-r border-gray-200">
                    <span className="font-normal text-sm text-gray-700">
                      <FormattedMessage
                        defaultMessage="Màu nền"
                        id="app.led.[led_key].(configure).config.slide-shows.page.1845624381"
                      />
                    </span>
                  </div>
                  <div className="w-[180px] flex-shrink-0">
                    <Form.Field name="background">
                      <PaletteColorPicker className="border-none" />
                    </Form.Field>
                  </div>
                </div>
                <div className="flex flex-row">
                  <div className="h-11 flex-1 min-w-0 p-3 border-r border-gray-200">
                    <span className="font-normal text-sm text-gray-700">
                      <FormattedMessage
                        defaultMessage="Màu chữ"
                        id="app.led.[led_key].(configure).config.slide-shows.page.1845380780"
                      />
                    </span>
                  </div>
                  <div className="w-[180px] flex-shrink-0">
                    <Form.Field name="color">
                      <PaletteColorPicker className="border-none" />
                    </Form.Field>
                  </div>
                </div>
                <div className="flex flex-row">
                  <div className="h-11 flex-1 min-w-0 p-3 border-r border-gray-200">
                    <span className="font-normal text-sm text-gray-700">
                      <FormattedMessage
                        defaultMessage="Màu viền"
                        id="app.led.[led_key].(configure).config.slide-shows.page.1372795006"
                      />
                    </span>
                  </div>
                  <div className="w-[180px] flex-shrink-0">
                    <Form.Field name="borderColor">
                      <PaletteColorPicker className="border-none" />
                    </Form.Field>
                  </div>
                </div>
              </GroupBox>
            </div>
          </div>
          <div className="px-6">
            <div className="w-full bg-gray-50 p-2 border border-gray-200 rounded-lg">
              <Form.Field name="background">
                {(controlBackground) => {
                  return (
                    <Form.Field name="color">
                      {(controlColor) => {
                        return (
                          <div className="h-full w-full flex items-center justify-center rounded-lg relative">
                            <Form.Field name="borderColor">
                              {(controlBorderColor) => {
                                const styleColor: React.CSSProperties = {
                                  backgroundColor: controlColor?.value,
                                };
                                const styleBackground: React.CSSProperties = {
                                  backgroundColor: controlBackground?.value,
                                };
                                const styleBorderColor: React.CSSProperties = {
                                  backgroundColor: controlBorderColor?.value,
                                };
                                return (
                                  <div className="flex flex-row gap-3 w-full">
                                    <div className="flex-1 flex flex-col gap-2">
                                      <span className="font-medium text-sm text-gray-700">
                                        <FormattedMessage
                                          defaultMessage="Xem trước dạng lưới"
                                          id="app.led.[led_key].(configure).config.slide-shows.page.658262262"
                                        />
                                      </span>
                                      <div className="flex w-full rounded overflow-hidden">
                                        <div className="flex flex-col w-full gap-[1px]" style={styleBorderColor}>
                                          <div className="flex flex-row items-center gap-2 p-3" style={styleBackground}>
                                            <SkeletonComp
                                              className="animate-none h-1 w-4 flex-shrink-0"
                                              style={styleColor}
                                            />
                                            <SkeletonComp
                                              className="animate-none h-1 flex-1 min-w-0"
                                              style={styleColor}
                                            />
                                            <SkeletonComp
                                              className="animate-none h-1 w-[42px] flex-shrink-0"
                                              style={styleColor}
                                            />
                                          </div>
                                          <div className="flex w-full">
                                            <div className="flex flex-row w-full gap-[1px]" style={styleBorderColor}>
                                              <div
                                                className="flex-1 flex flex-col items-center px-3 py-4 gap-3"
                                                style={styleBackground}
                                              >
                                                <SkeletonComp className="animate-none h-1 w-full" style={styleColor} />
                                                <SkeletonComp
                                                  className="animate-none h-1 w-[62px]"
                                                  style={styleColor}
                                                />
                                              </div>
                                              <div
                                                className="flex-1 flex flex-col items-center px-3 py-4 gap-3"
                                                style={styleBackground}
                                              >
                                                <SkeletonComp className="animate-none h-1 w-full" style={styleColor} />
                                                <SkeletonComp
                                                  className="animate-none h-1 w-[62px]"
                                                  style={styleColor}
                                                />
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="flex-1 flex flex-col gap-2 h-full">
                                      <span className="font-medium text-sm text-gray-700 flex-shrink-0">
                                        <FormattedMessage
                                          defaultMessage="Xem trước dạng bảng"
                                          id="app.led.[led_key].(configure).config.slide-shows.page.664844862"
                                        />
                                      </span>
                                      <div className="flex-1 min-h-0 flex rounded overflow-hidden">
                                        <div className="flex flex-col gap-[1px] w-full h-full" style={styleBorderColor}>
                                          <div
                                            className="flex flex-row items-center gap-2 p-3 flex-shrink-0"
                                            style={styleBackground}
                                          >
                                            <SkeletonComp
                                              className="animate-none h-1 w-4 flex-shrink-0"
                                              style={styleColor}
                                            />
                                            <SkeletonComp
                                              className="animate-none h-1 flex-1 min-w-0"
                                              style={styleColor}
                                            />
                                            <SkeletonComp
                                              className="animate-none h-1 w-[42px] flex-shrink-0"
                                              style={styleColor}
                                            />
                                          </div>
                                          <div
                                            className="flex-1 min-h-[25px] flex flex-row items-center px-3 py-2 gap-3"
                                            style={styleBackground}
                                          >
                                            <SkeletonComp className="animate-none h-1 flex-1" style={styleColor} />
                                            <SkeletonComp className="animate-none h-1 flex-1" style={styleColor} />
                                            <SkeletonComp className="animate-none h-1 flex-1" style={styleColor} />
                                            <SkeletonComp className="animate-none h-1 flex-1" style={styleColor} />
                                          </div>
                                          <div
                                            className="flex-1 min-h-[25px] flex flex-row items-center"
                                            style={styleBackground}
                                          >
                                            <div
                                              className="flex-1 min-h-[25px] flex flex-row items-center px-3 py-2 gap-3"
                                              style={{
                                                backgroundColor: hexToRgba(controlColor?.value, 0.2),
                                              }}
                                            >
                                              <SkeletonComp className="animate-none h-1 flex-1" style={styleColor} />
                                              <SkeletonComp className="animate-none h-1 flex-1" style={styleColor} />
                                              <SkeletonComp className="animate-none h-1 flex-1" style={styleColor} />
                                              <SkeletonComp className="animate-none h-1 flex-1" style={styleColor} />
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                );
                              }}
                            </Form.Field>
                          </div>
                        );
                      }}
                    </Form.Field>
                  );
                }}
              </Form.Field>
            </div>
          </div>

          <div className="flex-shrink-0 px-6">
            <Button className="w-full" type="submit" disabled={penddingConfigSlide}>
              <FormattedMessage
                defaultMessage="Lưu cấu hình"
                id="app.led.[led_key].(configure).config.slide-shows.page.1043319462"
              />
            </Button>
          </div>
        </div>
      </Spin>
    </Form>
  );
}
