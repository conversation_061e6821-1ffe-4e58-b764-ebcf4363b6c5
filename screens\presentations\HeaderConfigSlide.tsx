import GroupBox from '@/components/sessions/GroupBox';
import ContentChangedBase from '@/components/ui/ContentChangedBase';
import InputLanguage from '@/components/ui/InputLanguage/InputLanguage';
import { NumberInput } from '@/components/ui/NumberInput';
import { routePath } from '@/constants';
import { ISlide } from '@/types/slide';
import { cn } from '@/utils/tailwind';
import { InfoIcon } from '@phosphor-icons/react';
import { useParams, useRouter } from 'next/navigation';
import { Dispatch, SetStateAction, useCallback, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Form,
  FormInstance,
  Switch,
  toast,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from 'ui-components';

type FormData = {
  width: number;
  height: number;
  presentations: ISlide[];
};

type Props = {
  focusedIndex: number;
  form: FormInstance<FormData>;
  onChangeType: Dispatch<SetStateAction<'submit' | 'save'>>;
  dataHeader: ISlide['header'];
  initFont: {
    [x: string]: number;
  };
};

export default function HeaderConfigSlide({
  focusedIndex,
  form,
  onChangeType,
  dataHeader,
  initFont,
  ...props
}: Props & React.HTMLAttributes<HTMLDivElement>) {
  const intl = useIntl();
  const { led_key } = useParams() as { led_key: string };
  const router = useRouter();
  const [isOpenConfigDefault, setIsOpenConfigDefault] = useState(false);
  const showOptionKeys = ['showAvatar', 'showTitle', 'showSubTitle', 'isDataSynced'] as const;

  const handleShowOptionSwitchChange = useCallback(
    (key: 'showTitle' | 'showAvatar' | 'showSubTitle' | 'isDataSynced', checked: boolean, focusedIndex: number) => {
      const showOptions = form.getFieldValue(['presentations', focusedIndex, 'header', 'showOptions']) || {};
      const checkedCount = showOptionKeys.filter((k) => showOptions[k]).length;
      if (checkedCount === 1 && !checked && showOptions[key]) {
        toast({
          type: 'error',
          title: intl.formatMessage({
            defaultMessage: 'Không thể tắt cấu hình hiển thị cuối cùng.',
            id: 'screens.presentations.HeaderConfigSlide.270961639',
          }),
          options: { position: 'top-center' },
        });
        return;
      }
      form.setFieldValue(['presentations', focusedIndex, 'header', 'showOptions', key], checked);
    },
    [form, showOptionKeys, intl],
  );

  return (
    <div {...props} className={cn('flex-1 min-w-0 flex flex-col gap-4 h-fit', props.className)}>
      <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
        <span className="font-semibold text-base text-gray-700">
          <FormattedMessage defaultMessage="Cấu hình tổng quan" id="screens.presentations.ConfigSlide.257768898" />
        </span>
        <div className="flex flex-col gap-2">
          <div className="flex flex-row gap-2 items-center">
            <span className="font-medium text-sm text-gray-700">
              <FormattedMessage defaultMessage="Tiêu đề tùy chỉnh" id="screens.presentations.ConfigSlide.1801769058" />
            </span>
            <Tooltip>
              <TooltipTrigger asChild>
                <InfoIcon size={18} weight="regular" className="text-gray-500 flex-shrink-0 cursor-pointer" />
              </TooltipTrigger>
              <TooltipContent
                className="p-2 text-gray-300 bg-gray-700 rounded-lg shadow font-normal text-xs max-w-[269px]"
                side="right"
                align="start"
              >
                <FormattedMessage
                  defaultMessage="Tiêu đề trang trình chiếu không được nhập quá 72 ký tự."
                  id="app.led.[led_key].(configure).config.default-data.page.775148511"
                />
              </TooltipContent>
            </Tooltip>
          </div>
          <div className="h-full w-full">
            <Form.Field<FormData> name={['presentations', focusedIndex, 'header', 'title']} trigger="onBlur">
              <InputLanguage
                placeholder={intl.formatMessage({
                  defaultMessage: 'Nhập tiêu đề tùy chỉnh',
                  id: 'screens.presentations.HeaderConfigSlide.1480117793',
                })}
                name="title"
                maxLenght={72}
              />
            </Form.Field>
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <div className="flex flex-row gap-2 items-center">
            <span className="font-medium text-sm text-gray-700">
              <FormattedMessage defaultMessage="Phụ đề tùy chỉnh" id="screens.presentations.ConfigSlide.430321621" />
            </span>
            <Tooltip>
              <TooltipTrigger asChild>
                <InfoIcon size={18} weight="regular" className="text-gray-500 flex-shrink-0 cursor-pointer" />
              </TooltipTrigger>
              <TooltipContent
                className="p-2 text-gray-300 bg-gray-700 rounded-lg shadow font-normal text-xs max-w-[269px]"
                side="right"
                align="start"
              >
                <FormattedMessage
                  defaultMessage="Phụ đề trang trình chiếu không được nhập quá 100 ký tự."
                  id="app.led.[led_key].(configure).config.default-data.page.1879174810"
                />
              </TooltipContent>
            </Tooltip>
          </div>
          <div className="h-full w-full">
            <Form.Field<FormData> name={['presentations', focusedIndex, 'header', 'sub_title']} trigger="onBlur">
              <InputLanguage
                placeholder={intl.formatMessage({
                  defaultMessage: 'Nhập phụ đề tùy chỉnh',
                  id: 'screens.presentations.HeaderConfigSlide.1155738296',
                })}
                name="sub_title"
                maxLenght={100}
              />
            </Form.Field>
          </div>
        </div>
        <span className="font-normal text-sm text-gray-600">
          <FormattedMessage
            defaultMessage="Nếu tiêu đề tùy chỉnh và phụ đề tùy chỉnh không có dữ liệu. Giá trị mặc định sẽ được thay thế."
            id="screens.presentations.HeaderConfigSlide.1590576924"
          />{' '}
          <span
            className="decoration-solid underline text-primary-500 cursor-pointer"
            onClick={() => {
              setIsOpenConfigDefault(true);
            }}
          >
            <FormattedMessage
              defaultMessage="Cấu hình dữ liệu mặc định."
              id="screens.presentations.HeaderConfigSlide.817909374"
            />
          </span>
        </span>
      </div>

      <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
        <span className="font-semibold text-base text-gray-700">
          <FormattedMessage defaultMessage="Cấu hình hiển thị" id="screens.presentations.ConfigSlide.206334904" />
        </span>
        <GroupBox>
          <div className="flex flex-row gap-3 p-3 items-center">
            <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
              <FormattedMessage defaultMessage="Ảnh đại diện" id="screens.presentations.ConfigSlide.745843351" />
            </span>
            <Form.Field<FormData> name={['presentations', focusedIndex, 'header', 'showOptions', 'showAvatar']}>
              {(control) => (
                <Switch
                  className="flex-shrink-0"
                  checked={control.value}
                  onCheckedChange={(checked) => handleShowOptionSwitchChange('showAvatar', checked, focusedIndex)}
                />
              )}
            </Form.Field>
          </div>
          <div className="flex flex-row gap-3 p-3 items-center">
            <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
              <FormattedMessage defaultMessage="Tiêu đề" id="screens.presentations.ConfigSlide.466595280" />
            </span>
            <Form.Field<FormData> name={['presentations', focusedIndex, 'header', 'showOptions', 'showTitle']}>
              {(control) => (
                <Switch
                  className="flex-shrink-0"
                  checked={control.value}
                  onCheckedChange={(checked) => handleShowOptionSwitchChange('showTitle', checked, focusedIndex)}
                />
              )}
            </Form.Field>
          </div>
          <div className="flex flex-row gap-3 p-3 items-center">
            <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
              <FormattedMessage defaultMessage="Phụ đề" id="screens.presentations.ConfigSlide.1672924925" />
            </span>
            <Form.Field<FormData> name={['presentations', focusedIndex, 'header', 'showOptions', 'showSubTitle']}>
              {(control) => (
                <Switch
                  className="flex-shrink-0"
                  checked={control.value}
                  onCheckedChange={(checked) => handleShowOptionSwitchChange('showSubTitle', checked, focusedIndex)}
                />
              )}
            </Form.Field>
          </div>
          <div className="flex flex-row gap-3 p-3 items-center">
            <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
              <FormattedMessage
                defaultMessage="Thời gian nhận dữ liệu"
                id="screens.presentations.ConfigSlide.1564152116"
              />
            </span>
            <Form.Field<FormData> name={['presentations', focusedIndex, 'header', 'showOptions', 'isDataSynced']}>
              {(control) => (
                <Switch
                  className="flex-shrink-0"
                  checked={control.value}
                  onCheckedChange={(checked) => handleShowOptionSwitchChange('isDataSynced', checked, focusedIndex)}
                />
              )}
            </Form.Field>
          </div>
        </GroupBox>
      </div>

      <Form.Field<FormData> name={['presentations', focusedIndex, 'header', 'fontSize', 'auto']}>
        {(control) => {
          return (
            <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
              <span className="font-semibold text-base text-gray-700">
                <FormattedMessage
                  defaultMessage="Kích thước kiểu chữ"
                  id="screens.presentations.ConfigSlide.1435964239"
                />
              </span>
              <div className="flex flex-col gap-2">
                <span className="font-medium text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage=" Cấu hình kích thước"
                    id="screens.presentations.ConfigSlide.641803552"
                  />
                </span>
                <div className="h-full w-full">
                  <GroupBox>
                    <div className="flex flex-row gap-3 p-3 items-center">
                      <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                        <FormattedMessage
                          defaultMessage="Tự động chọn kích thước phù hợp"
                          id="screens.presentations.ConfigSlide.320775361"
                        />
                      </span>
                      <Form.Field<FormData> name={['presentations', focusedIndex, 'header', 'fontSize', 'auto']}>
                        {(control) => (
                          <Switch
                            className="flex-shrink-0"
                            checked={control.value}
                            onCheckedChange={(checked) => {
                              control.onChange(checked);
                              if (checked) {
                                form.setFieldValue(
                                  ['presentations', focusedIndex, 'header', 'fontSize', 'config'],
                                  initFont,
                                );
                              }
                            }}
                          />
                        )}
                      </Form.Field>
                    </div>
                  </GroupBox>
                </div>
                {Boolean(control.value) && (
                  <span className="font-normal text-xs text-gray-700">
                    <FormattedMessage
                      defaultMessage="Hệ thống sẽ tự động điều chỉnh kích thước nội dung để phù hợp với kích thước màn hình."
                      id="screens.presentations.HeaderConfigSlide.291032092"
                    />
                  </span>
                )}
              </div>
              {!Boolean(control.value) && (
                <div className="flex flex-col gap-2">
                  <span className="font-medium text-sm text-gray-700">
                    <FormattedMessage
                      defaultMessage="Tùy biến riêng"
                      id="screens.presentations.ConfigSlide.1975998546"
                    />
                  </span>
                  <div className="h-full w-full">
                    <GroupBox>
                      <div className="flex flex-row items-center bg-gray-50">
                        <span className="flex-1 min-w-0 p-3 font-semibold text-xs text-gray-700 uppercase">
                          <FormattedMessage
                            defaultMessage="Nội dung"
                            id="screens.presentations.HeaderConfigSlide.768903020"
                          />
                        </span>
                        <span className="w-[110px] flex-shrink-0 p-3 font-semibold text-xs text-gray-700 uppercase text-center">
                          <FormattedMessage
                            defaultMessage="Kích thước"
                            id="screens.presentations.HeaderConfigSlide.1145935829"
                          />
                        </span>
                      </div>
                      <div className="flex flex-row items-center">
                        <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                          <FormattedMessage
                            defaultMessage="Tiêu đề"
                            description=""
                            id="screens.presentations.HeaderConfigSlide.466595280"
                          />
                        </span>
                        <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                          <Form.Field
                            name={['presentations', focusedIndex, 'header', 'fontSize', 'config', 'title']}
                            trigger="onBlurInput"
                          >
                            <NumberInput
                              precision={0}
                              placeholder=""
                              suffix="px"
                              classNameWrapper="h-[33px]"
                              defaultValue={dataHeader.fontSize.config.title}
                              clearable={false}
                              controls={false}
                              keyboard
                            />
                          </Form.Field>
                        </div>
                      </div>
                      <div className="flex flex-row items-center">
                        <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                          <FormattedMessage
                            defaultMessage="Phụ đề"
                            id="screens.presentations.HeaderConfigSlide.1672924925"
                          />
                        </span>
                        <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                          <Form.Field
                            name={['presentations', focusedIndex, 'header', 'fontSize', 'config', 'description']}
                            trigger="onBlurInput"
                          >
                            <NumberInput
                              precision={0}
                              placeholder=""
                              suffix="px"
                              classNameWrapper="h-[33px]"
                              defaultValue={dataHeader.fontSize.config.description}
                              clearable={false}
                              controls={false}
                              keyboard
                            />
                          </Form.Field>
                        </div>
                      </div>
                      <div className="flex flex-row items-center">
                        <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                          <FormattedMessage
                            defaultMessage="Thời gian"
                            id="screens.presentations.HeaderConfigSlide.885104111"
                          />
                        </span>
                        <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                          <Form.Field
                            name={['presentations', focusedIndex, 'header', 'fontSize', 'config', 'datetime']}
                            trigger="onBlurInput"
                          >
                            <NumberInput
                              precision={0}
                              placeholder=""
                              suffix="px"
                              classNameWrapper="h-[33px]"
                              defaultValue={dataHeader.fontSize.config.datetime}
                              clearable={false}
                              controls={false}
                              keyboard
                            />
                          </Form.Field>
                        </div>
                      </div>
                    </GroupBox>
                  </div>
                  <span className="font-normal text-xs text-gray-700">
                    <FormattedMessage
                      defaultMessage="Cho phép tùy chỉnh kích thước nội dung hiển thị trên màn hình. Hệ thống sẽ không tự động thay đổi kích thước."
                      id="screens.presentations.HeaderConfigSlide.595062375"
                    />
                  </span>
                </div>
              )}
            </div>
          );
        }}
      </Form.Field>

      <Dialog open={isOpenConfigDefault} onOpenChange={setIsOpenConfigDefault}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="max-w-[420px] p-0">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentChangedBase
              onCancel={() => setIsOpenConfigDefault(false)}
              onSubmit={() => {
                onChangeType('save');
                form.submit();
                router.push(routePath.ledConfigDefault(led_key));
              }}
              heading={intl.formatMessage({
                defaultMessage: 'Chuyển sang cấu hình dữ liệu mặc định',
                id: 'screens.presentations.HeaderConfigSlide.768760477',
              })}
              description={intl.formatMessage({
                defaultMessage:
                  'Bạn có muốn lưu lại các thay đổi trước khi chuyển sang cấu hình dữ liệu mặc định? Vui lòng nhấn xác nhận để lưu và tiếp tục.',
                id: 'screens.presentations.HeaderConfigSlide.767979549',
              })}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </div>
  );
}
