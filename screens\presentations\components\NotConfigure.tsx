import { useLedConfigStores, useSlideConfigStores } from '@/stores';
import { hexToRgba } from '@/utils';
import { useMemo } from 'react';
import { FormattedMessage } from 'react-intl';
import { TextBoxFitSize } from './TextBoxFitSize';
import { ResponsiveTextWithOverride } from './ResponsiveTextWithOverride';
import { priorityText } from '../compoType/constants';

const NotConfigure = ({
  title = (
    <FormattedMessage defaultMessage="Chưa cấu hình nội dung" id="screens.presentations.ContentSlider.1788853769" />
  ),
  description = (
    <FormattedMessage
      defaultMessage="Vui lòng chọn thông tin trạm, thông số quan trắc để hiển thị dữ liệu trên màn hình."
      id="screens.presentations.ContentSlider.1516263099"
    />
  ),
}: {
  title?: string | React.ReactNode;
  description?: string | React.ReactNode;
}) => {
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const config = useSlideConfigStores((store) => store.config);

  if (!dataConfig) return null;

  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );
  return (
    <div className="h-full w-full flex justify-center items-center bg-transparent">
      <div className="flex flex-col h-[224px] w-full items-center justify-center gap-4">
        <div className="w-full flex flex-col gap-2 items-center justify-center">
          <ResponsiveTextWithOverride
            options={config}
            priority={priorityText.title as any}
            className="font-semibold text-[clamp(14px,_1.25vw_+_6.5px,_32px)]"
            style={{
              color: defaultConfig.slideConfig?.color,
            }}
          >
            {title}
          </ResponsiveTextWithOverride>
          <ResponsiveTextWithOverride
            options={config}
            priority={priorityText.description as any}
            className="font-normal text-[clamp(10px,_0.94vw_+_4px,_22px)] text-center"
            style={{
              color: hexToRgba(defaultConfig.slideConfig?.color, 0.8),
            }}
          >
            {description}
          </ResponsiveTextWithOverride>
        </div>
      </div>
    </div>
  );
};

export default NotConfigure;
