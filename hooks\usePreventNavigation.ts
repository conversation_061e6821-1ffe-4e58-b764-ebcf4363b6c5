import { LANGUEGES_VI } from '@/constants';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';

export function usePreventNavigation({ isDirty }: { isDirty: boolean }) {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const lang = window.navigator.language;
    const warningMessage =
      lang === LANGUEGES_VI
        ? 'M<PERSON><PERSON> thay đổi bạn thực hiện có thể không được lưu.'
        : 'Changes you made may not be saved.';

    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (isDirty) {
        event.preventDefault();
        event.returnValue = true;
        return '';
      }
    };

    const handleClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const closestLink = target.closest('a');

      if (closestLink && isDirty) {
        const href = closestLink.getAttribute('href');
        if (href && href !== pathname && !href.startsWith('#')) {
          event.preventDefault();
          event.stopPropagation();

          const userConfirmed = window.confirm(warningMessage);

          if (userConfirmed) {
            router.push(href);
          }
        }
      }
    };

    window.onbeforeunload = handleBeforeUnload;
    document.addEventListener('click', handleClick, { capture: true });

    return () => {
      window.onbeforeunload = null;
      document.removeEventListener('click', handleClick, { capture: true });
    };
  }, [isDirty, pathname, router]);
}
