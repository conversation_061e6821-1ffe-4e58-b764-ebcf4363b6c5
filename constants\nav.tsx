'use client';

import { FormattedMessage } from 'react-intl';
import type { UrlObject } from 'url';
import { segmentsPath } from '.';

type Url = string | UrlObject;

export interface AsideItemProps {
  name: string | React.ReactNode;
  icon: string;
  path: Url;
}

export const navLedConfig: AsideItemProps[] = [
  {
    name: <FormattedMessage defaultMessage="Cấu hình chung" id="constants.nav.1669473708" />,
    icon: 'WrenchIcon',
    path: segmentsPath.ledConfig,
  },
  {
    name: <FormattedMessage defaultMessage="Trang trình chiếu" id="constants.nav.1023776734" />,
    icon: 'MagicWandIcon',
    path: segmentsPath.ledPresentations,
  },
];

export const navLedChildConfig: AsideItemProps[] = [
  {
    name: <FormattedMessage defaultMessage="Chia sẻ bảng LED" id="constants.nav.729825786" />,
    icon: 'ShareNetworkIcon',
    path: segmentsPath.ledConfig,
  },
  {
    name: <FormattedMessage defaultMessage="Trang trình chiếu" id="constants.nav.1023776734" />,
    icon: 'SlideshowIcon',
    path: segmentsPath.ledConfigSlideShows,
  },
  {
    name: <FormattedMessage defaultMessage="Dữ liệu mặc định" id="constants.nav.221760357" />,
    icon: 'ReceiptIcon',
    path: segmentsPath.ledConfigDefaultData,
  },
];
