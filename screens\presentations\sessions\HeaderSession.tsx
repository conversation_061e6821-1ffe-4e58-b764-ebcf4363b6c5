import { ETimeRange, ETypeTimeRange, HistoryMetadata, RealTimeMetadata, SlideConfigDto } from '@/types/slide';
import { ResponsiveTextWithOverride } from '../components/ResponsiveTextWithOverride';
import { useLedConfigStores, useLedStores } from '@/stores';
import { useMemo } from 'react';
import { cn } from '@/utils/tailwind';
import Image from 'next/image';
import envConfig from '@/constants/env';
import { hexToRgba } from '@/utils';
import { ImageIcon } from '@phosphor-icons/react';
import { FormattedMessage, useIntl } from 'react-intl';
import { dataTypeObject, DATE_FORMAT, DATETIME_FORMAT } from '@/constants';
import dayjs from 'dayjs';

type HeaderProps = {
  slide: SlideConfigDto;
  mode?: 'config' | 'preview';
};

export default function HeaderSession({ slide, mode = 'config' }: HeaderProps) {
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const dataLedDetail = useLedStores((store) => store.dataLedDetail);
  if (!dataConfig || !dataLedDetail) return null;

  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );

  const dataStations = useMemo(() => {
    if (slide.content.type === dataTypeObject.realtime) {
      return dataLedDetail?.dataStations?.find(
        (station) => station.key === (slide.content.metadata as RealTimeMetadata)?.station,
      );
    }
    return null;
  }, [dataLedDetail, slide]);

  const getTimeAsync = useMemo(() => {
    switch (slide.content.type) {
      case dataTypeObject.realtime:
        return dayjs(dataStations?.lastLog?.receivedAt).format(DATETIME_FORMAT);
      case dataTypeObject.histories: {
        const timeRange = (slide.content.metadata as HistoryMetadata)?.timeRange;
        const typeTimeRange = (slide.content.metadata as HistoryMetadata)?.typeTimeRange;

        let days = 7;
        if (timeRange === ETimeRange['15 days']) days = 15;
        else if (timeRange === ETimeRange['30 days']) days = 30;

        let endDate = dayjs();
        if (typeTimeRange === ETypeTimeRange['yesterday']) {
          endDate = endDate.subtract(1, 'day');
        }
        const startDate = endDate.subtract(days - 1, 'day');

        const startStr = startDate.format(DATE_FORMAT);
        const endStr = endDate.format(DATE_FORMAT);

        return `${startStr} - ${endStr}`;
      }
      default:
        return '---';
    }
  }, [slide]);
  return (
    <div
      className={cn(
        'w-full flex-shrink-0 text-current flex flex-col',
        'sm:flex-row',
        {
          'mobile-short-height:hidden': !Boolean(slide.header.showOptions?.isDataSynced),
        },
        {
          'mobile-short-height:flex': Boolean(slide.header.showOptions?.isDataSynced),
        },
      )}
      style={{ backgroundColor: defaultConfig.slideConfig?.background, color: defaultConfig.slideConfig?.color }}
    >
      <div
        className={cn(
          'h-full flex flex-row items-center flex-1 min-w-0',
          'p-2 sm:p-[6px] md:p-2 lg:p-[10px] xl:p-[14px] 2xl:p-4 3xl:p-5',
          'gap-2 lg:gap-[10px] xl:gap-[14px] 2xl:gap-4',
          'mobile-short-height:hidden',
        )}
      >
        {slide.header.showOptions?.showAvatar && (
          <div className="h-full max-h-full flex-shrink-0">
            {defaultConfig.init?.avatar ? (
              <div className="flex h-full w-full items-center justify-center">
                <Image
                  src={`${envConfig.MEDIA_ENDPOINT}/${defaultConfig.init?.avatar}`}
                  height={36}
                  width={36}
                  alt="logo"
                  className="h-full w-full object-contain"
                  style={{ maxWidth: '100%', maxHeight: '100%' }}
                />
              </div>
            ) : (
              <div className="h-full grid max-h-full aspect-square flex-shrink-0">
                <div
                  className="flex items-center justify-center h-full w-full"
                  style={{
                    background: hexToRgba(defaultConfig.slideConfig?.color, 0.3),
                  }}
                >
                  <ImageIcon
                    className="flex-shrink-0 h-[40%] w-[40%]"
                    style={{
                      color: hexToRgba(defaultConfig.slideConfig?.color, 0.4),
                    }}
                  />
                </div>
              </div>
            )}
          </div>
        )}
        <div className="h-full flex-1 min-w-0 flex flex-col justify-center">
          <div className={cn('visible', { invisible: !slide.header.showOptions.showTitle })}>
            {slide.header.title?.[locale] || defaultConfig.init?.title?.[locale] ? (
              <ResponsiveTextWithOverride
                className="font-semibold truncate block"
                value={slide.header.fontSize.config.title}
              >
                {slide.header.title?.[locale] || defaultConfig.init?.title?.[locale]}
              </ResponsiveTextWithOverride>
            ) : (
              <ResponsiveTextWithOverride
                className={cn('font-semibold truncate block', { invisible: mode === 'preview' })}
                value={slide.header.fontSize.config.title}
                style={{
                  color: hexToRgba(defaultConfig.slideConfig?.color, 0.7),
                }}
              >
                <FormattedMessage
                  defaultMessage="Nhập nội dung tiêu đề"
                  id="screens.presentations.ContentSlider.1657403361"
                />
              </ResponsiveTextWithOverride>
            )}
          </div>
          <div className={cn('visible', { invisible: !slide.header.showOptions.showSubTitle })}>
            {slide.header?.sub_title?.[locale] || defaultConfig.init?.sub_title?.[locale] ? (
              <ResponsiveTextWithOverride
                className="font-normal truncate block"
                value={slide.header.fontSize.config.description}
              >
                {slide.header?.sub_title?.[locale] || defaultConfig.init?.sub_title?.[locale]}
              </ResponsiveTextWithOverride>
            ) : (
              <ResponsiveTextWithOverride
                className={cn('font-normal truncate block text-[100%]', { invisible: mode === 'preview' })}
                value={slide.header.fontSize.config.description}
                style={{
                  color: hexToRgba(defaultConfig.slideConfig?.color, 0.7),
                }}
              >
                <FormattedMessage
                  defaultMessage="Nhập nội dung phụ đề"
                  id="screens.presentations.ContentSlider.613863636"
                />
              </ResponsiveTextWithOverride>
            )}
          </div>
        </div>
      </div>
      {slide.header.showOptions?.isDataSynced && (
        <div
          className={cn(
            'h-auto w-full flex flex-row items-center justify-between flex-shrink-0',
            'sm:flex-col sm:h-full sm:w-fit sm:justify-center sm:items-end',
            'p-[4px] sm:p-[6px] md:p-2 lg:p-[10px] xl:p-[14px] 2xl:p-4 3xl:p-5',
            'mobile-short-height:w-full mobile-short-height:h-full mobile-short-height:flex-row mobile-short-height:justify-between mobile-short-height:items-center mobile-short-height:gap-[2px]',
          )}
        >
          <ResponsiveTextWithOverride className="font-normal" value={slide.header.fontSize.config.datetime}>
            <FormattedMessage
              defaultMessage="Thời gian nhận dữ liệu"
              id="screens.presentations.ContentSlider.1564152116"
            />
          </ResponsiveTextWithOverride>
          <ResponsiveTextWithOverride className="font-semibold" value={slide.header.fontSize.config.datetime}>
            {getTimeAsync}
          </ResponsiveTextWithOverride>
        </div>
      )}
    </div>
  );
}
