import { useLedConfigStores, useSlideConfigStores } from '@/stores';
import { EContentType, ImageContent, SlideConfigDto } from '@/types/slide';
import { useEffect, useMemo } from 'react';
import { FormattedMessage } from 'react-intl';
import { cn } from '@/utils/tailwind';
import { hexToRgba } from '@/utils';
import useEmblaCarousel from 'embla-carousel-react';
import { usePagination } from '@/hooks/usePagination';
import Fade from 'embla-carousel-fade';
import { EmblaCarouselType } from 'embla-carousel';
import { ImageIcon } from '@phosphor-icons/react';
import Image from 'next/image';
import envConfig from '@/constants/env';
import { ResponsiveTextWithOverride } from '../components/ResponsiveTextWithOverride';
import { priorityText } from '../compoType/constants';

type Props = {
  dataSlide: SlideConfigDto;
  isIframeReady: boolean;
  setEmblaApi: (emblaApi: EmblaCarouselType) => void;
};

export default function ContentImage({ dataSlide, isIframeReady, setEmblaApi }: Props) {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: false, watchDrag: false, containScroll: false }, [Fade()]);
  const { selectedIndex } = usePagination(emblaApi);
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const config = useSlideConfigStores((store) => store.config);

  if (!dataConfig) return null;

  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );

  const dataContentSlide = useMemo(() => dataSlide.content, [dataSlide]) as {
    type: EContentType.images;
    metadata: ImageContent;
  };

  const slide = useMemo(() => dataSlide, [dataSlide]);

  const dataItems = useMemo(() => {
    return dataContentSlide.metadata?.images ?? [];
  }, [slide]);

  const configGrid = useMemo(() => {
    return {
      column: dataContentSlide.metadata?.config.column ?? 3,
      row: dataContentSlide.metadata?.config.row ?? 3,
    };
  }, [dataContentSlide]);

  useEffect(() => {
    if (emblaApi && isIframeReady) {
      setEmblaApi(emblaApi);
    }
  }, [emblaApi, setEmblaApi, isIframeReady]);

  return (
    <div className="h-full w-full overflow-hidden" ref={emblaRef}>
      <div className={cn('embla__container flex h-full w-full')}>
        <div className="embla__slide flex-[0_0_100%] min-w-0 h-full">
          <div
            className="flex-1 h-full w-full grid gap-[1px]"
            style={{
              gridTemplateColumns: `repeat(${configGrid.column}, minmax(0, 1fr))`,
              gridTemplateRows: `repeat(${configGrid.row}, minmax(0, 1fr))`,
              backgroundColor: defaultConfig.slideConfig?.borderColor,
            }}
          >
            {Array.from({ length: configGrid.column * configGrid.row }).map((_, index) => {
              const currentItem = dataItems?.[index] || null;
              if (currentItem) {
                const url = currentItem.url instanceof File ? URL.createObjectURL(currentItem.url) : currentItem.url;
                if (typeof url !== 'string')
                  return (
                    <div
                      key={index}
                      className="h-full w-full flex items-center justify-center"
                      style={{
                        backgroundColor: defaultConfig.slideConfig?.background,
                      }}
                    >
                      <ResponsiveTextWithOverride
                        options={config}
                        priority={priorityText.subDescription as any}
                        className="flex flex-col items-center justify-center gap-1"
                        style={{
                          color: hexToRgba(defaultConfig.slideConfig?.color, 0.4),
                        }}
                      >
                        <ImageIcon className="h-[2em] w-auto flex-shrink-0 text-current" />
                        <span className="font-medium text-current text-center">
                          <FormattedMessage
                            defaultMessage="Vui lòng chọn hình ảnh hiển thị"
                            id="screens.presentations.contentType.ContentImage.399412569"
                          />
                        </span>
                      </ResponsiveTextWithOverride>
                    </div>
                  );
                return (
                  <div
                    key={index}
                    className="relative h-full w-full"
                    style={{
                      backgroundColor: defaultConfig.slideConfig?.background,
                    }}
                  >
                    <Image
                      src={url?.startsWith('blob') ? url : `${envConfig.MEDIA_ENDPOINT}/${url}`}
                      alt={'image' + index}
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      className="select-none"
                      style={{
                        objectFit: dataContentSlide.metadata?.mode,
                      }}
                      loading="lazy"
                      priority={false}
                    />
                  </div>
                );
              }
              return (
                <div
                  key={index}
                  className="h-full w-full flex items-center justify-center"
                  style={{
                    backgroundColor: defaultConfig.slideConfig?.background,
                  }}
                >
                  <ResponsiveTextWithOverride
                    options={config}
                    priority={priorityText.subDescription as any}
                    className="flex flex-col items-center justify-center gap-1"
                    style={{
                      color: hexToRgba(defaultConfig.slideConfig?.color, 0.4),
                    }}
                  >
                    <ImageIcon className="h-[2em] w-auto flex-shrink-0 text-current" />
                    <span className="font-medium text-current text-center">
                      <FormattedMessage
                        defaultMessage="Vui lòng chọn hình ảnh hiển thị"
                        id="screens.presentations.contentType.ContentImage.399412569"
                      />
                    </span>
                  </ResponsiveTextWithOverride>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
