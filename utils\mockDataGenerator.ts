import { IMeasuring } from '@/types';
import { EStatus } from '@/types/slide';
import { DateMeasureData, evaluateThreshold, MeasureData, ThresholdOptions } from './evaluationUtils';

/**
 * Generates a random value for a measure parameter within the parameter's range
 * If range is not defined, generates a value based on limits or defaults to 0-10 range
 * Implements requirement 4.2: "When sample data is created, the system SHALL create random values for monitoring parameters"
 * Implements requirement 5.1: "When displaying numeric values, the system SHALL round decimal places according to decimalFormat configuration"
 *
 * @param measureItem - The measuring item with range information
 * @param decimalFormat - Number of decimal places for formatting
 * @param distribution - Optional distribution type ('normal', 'uniform', or 'realistic')
 * @returns Random value formatted to the specified decimal places
 */
export const generateRandomValue = (
  measureItem: IMeasuring,
  decimalFormat: number,
  distribution: 'normal' | 'uniform' | 'realistic' = 'realistic',
): number => {
  let min = 0;
  let max = 10;
  let mean: number;
  let stdDev: number;

  // Use parameter's range if available
  if (measureItem.minRange !== null && measureItem.maxRange !== null) {
    min = measureItem.minRange;
    max = measureItem.maxRange;
  } else {
    // If no range is defined, use limits to inform the range
    if (measureItem.minLimit !== null) {
      min = measureItem.minLimit * 0.5; // Set min to 50% of minLimit
    }

    if (measureItem.maxLimit !== null) {
      max = measureItem.maxLimit * 1.5; // Set max to 150% of maxLimit
    }
  }

  // Calculate mean and standard deviation for normal distribution
  mean = (min + max) / 2;
  stdDev = (max - min) / 6; // 99.7% of values will fall within min-max range

  let randomValue: number;

  switch (distribution) {
    case 'normal':
      // Generate normally distributed random value
      // Using Box-Muller transform to generate normal distribution
      const u1 = Math.random();
      const u2 = Math.random();
      const z0 = Math.sqrt(-2.0 * Math.log(u1)) * Math.cos(2.0 * Math.PI * u2);
      randomValue = mean + z0 * stdDev;

      // Clamp value to min-max range
      randomValue = Math.max(min, Math.min(max, randomValue));
      break;

    case 'realistic':
      // Generate more realistic values that tend to cluster in normal ranges
      // but occasionally approach or exceed thresholds

      // Determine if this value should be an outlier
      const outlierChance = Math.random();

      if (outlierChance > 0.95) {
        // 5% chance of generating a value near or beyond thresholds
        const nearThreshold = Math.random() > 0.5;

        if (nearThreshold && measureItem.minTend !== null) {
          // Generate value near minimum threshold
          randomValue = measureItem.minTend * (0.9 + Math.random() * 0.2);
        } else if (nearThreshold && measureItem.maxTend !== null) {
          // Generate value near maximum threshold
          randomValue = measureItem.maxTend * (0.9 + Math.random() * 0.2);
        } else if (measureItem.minLimit !== null) {
          // Generate value near minimum limit
          randomValue = measureItem.minLimit * (0.9 + Math.random() * 0.2);
        } else if (measureItem.maxLimit !== null) {
          // Generate value near maximum limit
          randomValue = measureItem.maxLimit * (0.9 + Math.random() * 0.2);
        } else {
          // No thresholds defined, use normal distribution
          randomValue = mean + (Math.random() * 2 - 1) * stdDev;
        }
      } else {
        // 95% chance of generating a value in the normal range
        // Use triangular distribution centered on the mean
        const u = Math.random();
        const v = Math.random();
        randomValue = mean + (u - v) * stdDev * 2;
      }

      // Clamp value to min-max range
      randomValue = Math.max(min, Math.min(max, randomValue));
      break;

    case 'uniform':
    default:
      // Generate uniformly distributed random value between min and max
      randomValue = min + Math.random() * (max - min);
      break;
  }

  // Format to specified decimal places
  // This implements requirement 5.1: "When displaying numeric values,
  // the system SHALL round decimal places according to decimalFormat configuration"
  return Number(randomValue.toFixed(decimalFormat));
};

/**
 * Applies threshold rules to a measure value based on configuration
 * This function implements requirements 4.3, 5.3, and 5.4 for threshold warnings
 *
 * @param measureItem - The measuring item
 * @param value - The value to evaluate
 * @param warningConfig - Configuration for threshold warnings
 * @returns Measure data with threshold status
 */
export const applyThresholdRules = (
  measureItem: IMeasuring,
  value: number,
  warningConfig: ThresholdOptions,
): MeasureData => {
  // Apply threshold evaluation using the evaluateThreshold function
  // This implements requirements:
  // - 5.3: "When a parameter exceeds threshold limit and user has enabled exceeded warning,
  //        the system SHALL display that value with exceeded warning color"
  // - 5.4: "When a parameter is approaching threshold and user has enabled approaching warning,
  //        the system SHALL display that value with approaching warning color"
  const evaluation = evaluateThreshold({ ...measureItem, value }, warningConfig);

  // If warnings are disabled, always return GOOD status
  return {
    measure: measureItem.key,
    value,
    status: evaluation.status,
  };
};

/**
 * Generates mock data for all dates and measures
 * Applies proper formatting based on decimalFormat
 *
 * @param dateList - List of dates to generate data for
 * @param measureList - List of measure parameters to generate data for
 * @param measuringList - List of all measuring items with metadata
 * @param decimalFormat - Number of decimal places for formatting
 * @param warningConfig - Configuration for threshold warnings
 * @returns Object mapping dates to arrays of measure data
 */
/**
 * Generates a random value with a trend pattern over time
 * This creates more realistic data that shows patterns over time
 *
 * @param measureItem - The measuring item with range information
 * @param dateIndex - Index of the date in the sequence (used for trend calculation)
 * @param totalDates - Total number of dates in the sequence
 * @param decimalFormat - Number of decimal places for formatting
 * @param patternType - Type of pattern to generate ('stable', 'increasing', 'decreasing', 'fluctuating', 'random')
 * @returns Random value formatted to the specified decimal places
 */
export const generateTrendValue = (
  measureItem: IMeasuring,
  dateIndex: number,
  totalDates: number,
  decimalFormat: number,
  patternType: 'stable' | 'increasing' | 'decreasing' | 'fluctuating' | 'random' = 'random',
): number => {
  let min = 0;
  let max = 10;

  // Use parameter's range if available
  if (measureItem.minRange !== null && measureItem.maxRange !== null) {
    min = measureItem.minRange;
    max = measureItem.maxRange;
  } else {
    // If no range is defined, use limits to inform the range
    if (measureItem.minLimit !== null) {
      min = measureItem.minLimit * 0.5; // Set min to 50% of minLimit
    }

    if (measureItem.maxLimit !== null) {
      max = measureItem.maxLimit * 1.5; // Set max to 150% of maxLimit
    }
  }

  const range = max - min;
  let value: number;

  // Generate value based on pattern type
  switch (patternType) {
    case 'stable':
      // Generate values that stay relatively stable with small fluctuations
      const baseValue = min + range * 0.5; // Middle of the range
      const fluctuation = range * 0.1; // 10% fluctuation
      value = baseValue + (Math.random() * 2 - 1) * fluctuation;
      break;

    case 'increasing':
      // Generate values that show an increasing trend over time
      const startValue = min + range * 0.2; // Start at 20% of range
      const endValue = min + range * 0.8; // End at 80% of range
      const progress = dateIndex / (totalDates - 1 || 1); // 0 to 1
      const trendValue = startValue + progress * (endValue - startValue);
      const noise = range * 0.05; // 5% noise
      value = trendValue + (Math.random() * 2 - 1) * noise;
      break;

    case 'decreasing':
      // Generate values that show a decreasing trend over time
      const startVal = min + range * 0.8; // Start at 80% of range
      const endVal = min + range * 0.2; // End at 20% of range
      const prog = dateIndex / (totalDates - 1 || 1); // 0 to 1
      const trendVal = startVal - prog * (startVal - endVal);
      const noiseFactor = range * 0.05; // 5% noise
      value = trendVal + (Math.random() * 2 - 1) * noiseFactor;
      break;

    case 'fluctuating':
      // Generate values that fluctuate in a sine wave pattern
      const midValue = min + range * 0.5; // Middle of the range
      const amplitude = range * 0.3; // 30% amplitude
      const frequency = (2 * Math.PI) / totalDates; // Complete 1 cycle
      value = midValue + amplitude * Math.sin(dateIndex * frequency) + (Math.random() * 2 - 1) * (range * 0.05);
      break;

    case 'random':
    default:
      // Generate random values using the realistic distribution
      value = generateRandomValue(measureItem, decimalFormat, 'realistic');
      break;
  }

  // Clamp value to min-max range
  value = Math.max(min, Math.min(max, value));

  // Format to specified decimal places
  return Number(value.toFixed(decimalFormat));
};

/**
 * Generates mock data for all dates and measures
 * Applies proper formatting based on decimalFormat
 * Implements requirement 4.1: "When user configures history data display, the system SHALL create sample data for display"
 * Implements requirement 4.2: "When sample data is created, the system SHALL create random values for monitoring parameters"
 * Implements requirement 5.1: "When displaying numeric values, the system SHALL round decimal places according to decimalFormat configuration"
 *
 * @param dateList - List of dates to generate data for
 * @param measureList - List of measure parameters to generate data for
 * @param measuringList - List of all measuring items with metadata
 * @param decimalFormat - Number of decimal places for formatting
 * @param warningConfig - Configuration for threshold warnings
 * @returns Object mapping dates to arrays of measure data
 */
export const generateMockData = (
  dateList: string[],
  measureList: string[],
  measuringList: IMeasuring[],
  decimalFormat: number,
  warningConfig: ThresholdOptions,
): DateMeasureData => {
  const allMeasureData: DateMeasureData = {};

  // Assign a pattern type to each measure for more realistic data trends
  const measurePatterns: Record<string, 'stable' | 'increasing' | 'decreasing' | 'fluctuating' | 'random'> = {};
  const patternTypes: Array<'stable' | 'increasing' | 'decreasing' | 'fluctuating' | 'random'> = [
    'stable',
    'increasing',
    'decreasing',
    'fluctuating',
    'random',
  ];

  // Assign a random pattern to each measure
  measureList.forEach((measureKey) => {
    const randomIndex = Math.floor(Math.random() * patternTypes.length);
    measurePatterns[measureKey] = patternTypes[randomIndex];
  });

  // Generate data for each date and measure
  dateList.forEach((date, dateIndex) => {
    allMeasureData[date] = [];

    measureList.forEach((measureKey) => {
      const measureItem = measuringList.find((item) => item.key === measureKey);
      if (!measureItem) return;

      // Get the pattern type for this measure
      const patternType = measurePatterns[measureKey] || 'random';

      // Generate random value with proper decimal formatting and trend pattern
      // This implements requirement 4.2: "When sample data is created, the system SHALL create random values for monitoring parameters"
      // This implements requirement 5.1: "When displaying numeric values, the system SHALL round decimal places according to decimalFormat configuration"
      const randomValue = generateTrendValue(measureItem, dateIndex, dateList.length, decimalFormat, patternType);

      // Apply threshold rules to the value
      // This implements requirements:
      // - 4.3: "When sample data is created, the system SHALL apply the correct threshold rules for visual indicators"
      // - 5.3: "When a parameter exceeds threshold limit and user has enabled exceeded warning,
      //        the system SHALL display that value with exceeded warning color"
      // - 5.4: "When a parameter is approaching threshold and user has enabled approaching warning,
      //        the system SHALL display that value with approaching warning color"
      const measureData = applyThresholdRules(measureItem, randomValue, warningConfig);

      // Store the measure data with threshold status
      allMeasureData[date].push(measureData);
    });
  });

  return allMeasureData;
};

/**
 * Generates a set of data with seasonal or cyclical patterns
 * This creates more realistic data that follows natural patterns like daily or weekly cycles
 *
 * @param dateList - List of dates to generate data for
 * @param measureList - List of measure parameters to generate data for
 * @param measuringList - List of all measuring items with metadata
 * @param decimalFormat - Number of decimal places for formatting
 * @param warningConfig - Configuration for threshold warnings
 * @returns Object mapping dates to arrays of measure data with seasonal patterns
 */
export const generateSeasonalData = (
  dateList: string[],
  measureList: string[],
  measuringList: IMeasuring[],
  decimalFormat: number,
  warningConfig: ThresholdOptions,
): DateMeasureData => {
  const allMeasureData: DateMeasureData = {};

  // Assign a seasonal pattern type to each measure
  const seasonalPatterns: Record<
    string,
    {
      type: 'daily' | 'weekly' | 'random';
      baseValue: number;
      amplitude: number;
      phase: number;
      trend: 'none' | 'increasing' | 'decreasing';
      trendStrength: number;
    }
  > = {};

  // Initialize patterns for each measure
  measureList.forEach((measureKey) => {
    const measureItem = measuringList.find((item) => item.key === measureKey);
    if (!measureItem) return;

    let min = 0;
    let max = 10;

    // Use parameter's range if available
    if (measureItem.minRange !== null && measureItem.maxRange !== null) {
      min = measureItem.minRange;
      max = measureItem.maxRange;
    } else {
      // If no range is defined, use limits to inform the range
      if (measureItem.minLimit !== null) {
        min = measureItem.minLimit * 0.5; // Set min to 50% of minLimit
      }

      if (measureItem.maxLimit !== null) {
        max = measureItem.maxLimit * 1.5; // Set max to 150% of maxLimit
      }
    }

    const range = max - min;
    const baseValue = min + range * (0.3 + Math.random() * 0.4); // Base value between 30% and 70% of range
    const amplitude = range * (0.1 + Math.random() * 0.2); // Amplitude between 10% and 30% of range
    const phase = Math.random() * 2 * Math.PI; // Random phase shift

    // Randomly select pattern type
    const patternTypes: Array<'daily' | 'weekly' | 'random'> = ['daily', 'weekly', 'random'];
    const type = patternTypes[Math.floor(Math.random() * patternTypes.length)];

    // Randomly select trend direction
    const trendTypes: Array<'none' | 'increasing' | 'decreasing'> = ['none', 'increasing', 'decreasing'];
    const trend = trendTypes[Math.floor(Math.random() * trendTypes.length)];

    // Random trend strength between 0% and 20% of range
    const trendStrength = range * (Math.random() * 0.2);

    seasonalPatterns[measureKey] = {
      type,
      baseValue,
      amplitude,
      phase,
      trend,
      trendStrength,
    };
  });

  // Generate data for each date
  dateList.forEach((date, dateIndex) => {
    allMeasureData[date] = [];

    // Calculate normalized position in the time series (0 to 1)
    const timePosition = dateIndex / (dateList.length - 1 || 1);

    // For each measure, generate data with seasonal patterns
    measureList.forEach((measureKey) => {
      const measureItem = measuringList.find((item) => item.key === measureKey);
      if (!measureItem) return;

      const pattern = seasonalPatterns[measureKey];
      if (!pattern) return;

      let value: number;

      // Apply seasonal pattern based on type
      switch (pattern.type) {
        case 'daily':
          // Daily cycle (24-hour pattern)
          // Use dateIndex modulo 7 to simulate a week of data
          const dayPosition = dateIndex % 7;
          // Create a daily pattern with higher values during day, lower at night
          value = pattern.baseValue + pattern.amplitude * Math.sin((dayPosition / 7) * 2 * Math.PI + pattern.phase);
          break;

        case 'weekly':
          // Weekly cycle (7-day pattern)
          // Use integer division of dateIndex by 7 to get week number
          const weekNumber = Math.floor(dateIndex / 7);
          // Create a weekly pattern with peaks and valleys
          value = pattern.baseValue + pattern.amplitude * Math.sin(weekNumber * 2 * Math.PI + pattern.phase);
          break;

        case 'random':
        default:
          // Random variation around base value
          value = pattern.baseValue + (Math.random() * 2 - 1) * pattern.amplitude;
          break;
      }

      // Apply trend if specified
      if (pattern.trend === 'increasing') {
        value += timePosition * pattern.trendStrength;
      } else if (pattern.trend === 'decreasing') {
        value -= timePosition * pattern.trendStrength;
      }

      // Add some random noise (5% of amplitude)
      value += (Math.random() * 2 - 1) * pattern.amplitude * 0.05;

      // Ensure value stays within parameter's range or limits
      let min = 0;
      let max = 10;

      if (measureItem.minRange !== null && measureItem.maxRange !== null) {
        min = measureItem.minRange;
        max = measureItem.maxRange;
      } else {
        if (measureItem.minLimit !== null) {
          min = measureItem.minLimit * 0.5;
        }

        if (measureItem.maxLimit !== null) {
          max = measureItem.maxLimit * 1.5;
        }
      }

      // Clamp value to min-max range
      value = Math.max(min, Math.min(max, value));

      // Format to specified decimal places
      // This implements requirement 5.1: "When displaying numeric values,
      // the system SHALL round decimal places according to decimalFormat configuration"
      value = Number(value.toFixed(decimalFormat));

      // Apply threshold rules to the value
      const measureData = applyThresholdRules(measureItem, value, warningConfig);

      // Store the measure data with threshold status
      allMeasureData[date].push(measureData);
    });
  });

  return allMeasureData;
};

/**
 * Generates a value that will produce a specific threshold status
 * This function is used to create test data with predictable threshold characteristics
 *
 * @param measureItem - The measuring item
 * @param targetStatus - The desired threshold status ('GOOD', 'EXCEEDED_PREPARING', or 'EXCEEDED')
 * @param decimalFormat - Number of decimal places for formatting
 * @returns A value that will produce the target threshold status
 */
export const generateValueForThreshold = (
  measureItem: IMeasuring,
  targetStatus: EStatus,
  decimalFormat: number,
): number => {
  let value: number;

  switch (targetStatus) {
    case EStatus.Good:
      // Generate a value that will be evaluated as GOOD
      if (measureItem.minLimit !== null && measureItem.maxLimit !== null) {
        // Generate a value in the middle of the acceptable range
        value = measureItem.minLimit + (measureItem.maxLimit - measureItem.minLimit) * 0.5;
      } else if (measureItem.minLimit !== null) {
        // Generate a value safely above the minimum limit
        value = measureItem.minLimit * 1.2;
      } else if (measureItem.maxLimit !== null) {
        // Generate a value safely below the maximum limit
        value = measureItem.maxLimit * 0.8;
      } else if (measureItem.minTend !== null) {
        // Generate a value safely above the minimum tendency
        value = measureItem.minTend * 1.2;
      } else if (measureItem.maxTend !== null) {
        // Generate a value safely below the maximum tendency
        value = measureItem.maxTend * 0.8;
      } else {
        // No thresholds defined, use a default value in the middle of the range
        value =
          measureItem.minRange !== null && measureItem.maxRange !== null
            ? (measureItem.minRange + measureItem.maxRange) / 2
            : 5;
      }
      break;

    case EStatus.ExceededPreparing:
      // Generate a value that will be evaluated as EXCEEDED_PREPARING (approaching threshold)
      if (measureItem.minTend !== null && measureItem.maxTend !== null) {
        // Randomly choose between approaching min or max
        if (Math.random() > 0.5) {
          // Approaching minimum (just below minTend)
          value = measureItem.minTend * 0.95;
        } else {
          // Approaching maximum (just above maxTend)
          value = measureItem.maxTend * 1.05;
        }
      } else if (measureItem.minTend !== null) {
        // Approaching minimum (just below minTend)
        value = measureItem.minTend * 0.95;
      } else if (measureItem.maxTend !== null) {
        // Approaching maximum (just above maxTend)
        value = measureItem.maxTend * 1.05;
      } else {
        // No tendency thresholds defined, try to use limits as reference
        if (measureItem.minLimit !== null) {
          // Use minimum limit as reference (slightly above it)
          value = measureItem.minLimit * 1.05;
        } else if (measureItem.maxLimit !== null) {
          // Use maximum limit as reference (slightly below it)
          value = measureItem.maxLimit * 0.95;
        } else {
          // No thresholds defined, use a default value
          value = 7;
        }
      }
      break;

    case EStatus.Exceeded:
      // Generate a value that will be evaluated as EXCEEDED (beyond threshold)
      if (measureItem.minLimit !== null && measureItem.maxLimit !== null) {
        // Randomly choose between exceeding min or max
        if (Math.random() > 0.5) {
          // Exceed minimum (go below it)
          value = measureItem.minLimit * 0.8;
        } else {
          // Exceed maximum (go above it)
          value = measureItem.maxLimit * 1.2;
        }
      } else if (measureItem.minLimit !== null) {
        // Exceed minimum (go below it)
        value = measureItem.minLimit * 0.8;
      } else if (measureItem.maxLimit !== null) {
        // Exceed maximum (go above it)
        value = measureItem.maxLimit * 1.2;
      } else {
        // No limit thresholds defined, try to use tendencies
        if (measureItem.minTend !== null) {
          // Use minimum tendency as reference (well below it)
          value = measureItem.minTend * 0.7;
        } else if (measureItem.maxTend !== null) {
          // Use maximum tendency as reference (well above it)
          value = measureItem.maxTend * 1.3;
        } else {
          // No thresholds defined, use a default high value
          value = measureItem.maxRange !== null ? measureItem.maxRange * 1.2 : 9;
        }
      }
      break;

    default:
      // Default to a middle value
      value = 5;
  }

  // Format to specified decimal places
  return Number(value.toFixed(decimalFormat));
};

/**
 * Generates a set of mock data with specific threshold characteristics
 * This is useful for testing threshold visualization
 * This function implements requirements 4.3, 5.3, and 5.4 for threshold warnings
 *
 * @param dateList - List of dates to generate data for
 * @param measureList - List of measure parameters to generate data for
 * @param measuringList - List of all measuring items with metadata
 * @param decimalFormat - Number of decimal places for formatting
 * @param warningConfig - Configuration for threshold warnings
 * @returns Object mapping dates to arrays of measure data with specific threshold characteristics
 */
export const generateThresholdTestData = (
  dateList: string[],
  measureList: string[],
  measuringList: IMeasuring[],
  decimalFormat: number,
  warningConfig: ThresholdOptions,
): DateMeasureData => {
  const allMeasureData: DateMeasureData = {};
  // Generate data for each date
  dateList.forEach((date, dateIndex) => {
    allMeasureData[date] = [];

    // For each measure, generate data with specific threshold characteristics
    measureList.forEach((measureKey, measureIndex) => {
      const measureItem = measuringList.find((item) => item.key === measureKey);
      if (!measureItem) return;

      // Distribute threshold statuses across the data to ensure all types are represented
      // This creates a pattern where:
      // - Some values exceed thresholds
      // - Some values approach thresholds
      // - Some values are within normal range

      // Use a deterministic pattern based on date and measure indices
      // This ensures we get a good distribution of all threshold types
      const patternIndex = (dateIndex + measureIndex) % 3;

      // Determine target threshold status based on pattern
      let targetStatus: EStatus;

      switch (patternIndex) {
        case 0:
          targetStatus = EStatus.Good;
          break;
        case 1:
          // Only generate EXCEEDED_PREPARING if approaching warning is enabled
          targetStatus = EStatus.ExceededPreparing;
          break;
        case 2:
          // Only generate EXCEEDED if exceeded warning is enabled
          targetStatus = EStatus.Exceeded;
          break;
        default:
          targetStatus = EStatus.Good;
      }

      // Generate a value that will produce the target threshold status
      const value = generateValueForThreshold(measureItem, targetStatus, decimalFormat);

      // Apply threshold rules to the value
      // This implements requirements:
      // - 4.3: "When sample data is created, the system SHALL apply the correct threshold rules for visual indicators"
      // - 5.3: "When a parameter exceeds threshold limit and user has enabled exceeded warning,
      //        the system SHALL display that value with exceeded warning color"
      // - 5.4: "When a parameter is approaching threshold and user has enabled approaching warning,
      //        the system SHALL display that value with approaching warning color"
      const measureData = applyThresholdRules(measureItem, value, warningConfig);

      // Store the measure data with threshold status
      allMeasureData[date].push(measureData);
    });
  });

  return allMeasureData;
};

/**
 * Creates a smart mock data generator that selects the most appropriate generation strategy
 * based on the data characteristics and configuration
 *
 * This function implements requirements:
 * - 4.1: "When user configures history data display, the system SHALL create sample data for display"
 * - 4.2: "When sample data is created, the system SHALL create random values for monitoring parameters"
 * - 4.3: "When sample data is created, the system SHALL apply the correct threshold rules for visual indicators"
 * - 5.1: "When displaying numeric values, the system SHALL round decimal places according to decimalFormat configuration"
 * - 5.3: "When a parameter exceeds threshold limit and user has enabled exceeded warning,
 *        the system SHALL display that value with exceeded warning color"
 * - 5.4: "When a parameter is approaching threshold and user has enabled approaching warning,
 *        the system SHALL display that value with approaching warning color"
 *
 * @param dateList - List of dates to generate data for
 * @param measureList - List of measure parameters to generate data for
 * @param measuringList - List of all measuring items with metadata
 * @param decimalFormat - Number of decimal places for formatting
 * @param warningConfig - Configuration for threshold warnings
 * @param options - Additional options for data generation
 * @returns Object mapping dates to arrays of measure data
 */
export const createSmartMockData = (
  dateList: string[],
  measureList: string[],
  measuringList: IMeasuring[],
  decimalFormat: number,
  warningConfig: ThresholdOptions,
  options?: {
    dataStrategy?: 'random' | 'seasonal' | 'threshold-test';
    ensureThresholdVariety?: boolean;
    largeValues?: boolean;
    valueMultiplier?: number;
    minValue?: number;
    maxValue?: number;
  },
): DateMeasureData => {
  // Default options
  const dataStrategy = options?.dataStrategy || 'random';
  const largeValues = options?.largeValues || false;
  const valueMultiplier = options?.valueMultiplier || 1000;
  const minValue = options?.minValue;
  const maxValue = options?.maxValue;

  // If large values are requested, modify the measuring items to have larger ranges
  let modifiedMeasuringList = measuringList;
  if (largeValues) {
    modifiedMeasuringList = measuringList.map((item) => ({
      ...item,
      minRange:
        minValue !== undefined ? minValue : item.minRange !== null ? item.minRange * valueMultiplier : minValue || 10,
      maxRange:
        maxValue !== undefined
          ? maxValue
          : item.maxRange !== null
          ? item.maxRange * valueMultiplier
          : maxValue || valueMultiplier * 10,
      minLimit:
        minValue !== undefined ? minValue * 0.8 : item.minLimit !== null ? item.minLimit * valueMultiplier : 100,
      maxLimit:
        maxValue !== undefined
          ? maxValue * 1.2
          : item.maxLimit !== null
          ? item.maxLimit * valueMultiplier
          : valueMultiplier,
      minTend: minValue !== undefined ? minValue * 0.9 : item.minTend !== null ? item.minTend * valueMultiplier : 1000,
      maxTend:
        maxValue !== undefined
          ? maxValue * 1.1
          : item.maxTend !== null
          ? item.maxTend * valueMultiplier
          : valueMultiplier,
    }));
  } else if (minValue !== undefined || maxValue !== undefined) {
    // Nếu có minValue hoặc maxValue được chỉ định mà không có largeValues
    modifiedMeasuringList = measuringList.map((item) => ({
      ...item,
      minRange: minValue !== undefined ? minValue : item.minRange,
      maxRange: maxValue !== undefined ? maxValue : item.maxRange,
    }));
  }

  // Select the appropriate data generation strategy based on options
  switch (dataStrategy) {
    case 'seasonal':
      return generateSeasonalData(dateList, measureList, modifiedMeasuringList, decimalFormat, warningConfig);
    case 'threshold-test':
      return generateThresholdTestData(dateList, measureList, modifiedMeasuringList, decimalFormat, warningConfig);
    case 'random':
    default:
      return generateMockData(dateList, measureList, modifiedMeasuringList, decimalFormat, warningConfig);
  }
};

/**
 * Main function to generate mock data for history display
 * This is the primary function that should be called from the slide.ts buildPages function
 *
 * @param dateList - List of dates to generate data for
 * @param measureList - List of measure parameters to generate data for
 * @param measuringList - List of all measuring items with metadata
 * @param decimalFormat - Number of decimal places for formatting
 * @returns Object mapping dates to arrays of measure data
 */
export const generateMockDataForHistory = (
  dateList: string[],
  measureList: string[],
  measuringList: IMeasuring[],
  decimalFormat: number,
  warningConfig: ThresholdOptions,
): DateMeasureData => {
  // Create diverse data ranges for different measures
  const diverseRanges = [
    { min: 0.01, max: 99.99 }, // Percentage values
    { min: 10.5, max: 99.99 }, // Medium values
    { min: 10.1, max: 99.99 }, // Large values
    { min: 10.5, max: 99.99 }, // Very large values
    { min: 0.001, max: 9.999 }, // Small decimal values
    { min: 5.25, max: 50.75 }, // Mid-range values
  ];

  // Generate data with different ranges for each measure
  const allMeasureData: DateMeasureData = {};

  dateList.forEach((date, dateIndex) => {
    allMeasureData[date] = [];

    measureList.forEach((measureKey, measureIndex) => {
      const measureItem = measuringList.find((item) => item.key === measureKey);
      if (!measureItem) return;

      // Assign different range to each measure for diversity
      const rangeIndex = measureIndex % diverseRanges.length;
      const range = diverseRanges[rangeIndex];

      // Create modified measure item with diverse range
      const modifiedMeasureItem = {
        ...measureItem,
        minRange: range.min,
        maxRange: range.max,
        minLimit: range.min * 0.8,
        maxLimit: range.max * 1.2,
        minTend: range.min * 0.9,
        maxTend: range.max * 1.1,
      };

      // Use createSmartMockData for single measure
      const singleMeasureData = createSmartMockData(
        [date],
        [measureKey],
        [modifiedMeasureItem],
        decimalFormat,
        warningConfig,
        {
          dataStrategy: 'seasonal',
          largeValues: false,
          minValue: range.min,
          maxValue: range.max,
        },
      );

      // Add to result
      if (singleMeasureData[date]) {
        allMeasureData[date].push(...singleMeasureData[date]);
      }
    });
  });

  return allMeasureData;
};
