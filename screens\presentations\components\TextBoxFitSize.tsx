import { cn } from '@/utils/tailwind';
import React, { useEffect, useRef, useState } from 'react';

interface TextBoxFitSizeProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export const TextBoxFitSize = ({ children, ...props }: TextBoxFitSizeProps) => {
  const itemGridRef = useRef<HTMLDivElement>(null);
  const [size, setSize] = useState({ height: 1, width: 1 });

  useEffect(() => {
    const observer = new ResizeObserver(([entry]) => {
      const { width, height } = entry.contentRect;
      setSize({ width, height });
    });

    if (itemGridRef.current) observer.observe(itemGridRef.current);
    return () => observer.disconnect();
  }, [props.style?.fontSize]);

  return (
    <div
      {...props}
      ref={itemGridRef}
      className={cn('flex h-full w-full', props.className)}
      style={{
        ...props.style,
        fontSize: Math.max(8, Math.min(size.height, size.width) * 0.5),
      }}
    >
      {children}
    </div>
  );
};
