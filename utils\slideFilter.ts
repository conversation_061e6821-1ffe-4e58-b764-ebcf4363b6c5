import { DataHistory } from '@/api/type';
import { IDataStation } from '@/types';
import { ISubSlide, SlideConfigDto } from '@/types/slide';
import { IntlShape } from 'react-intl';
import { generateSubSlides } from './subSlidesGeneration';

/**
 * Filters slides to return only visible slides (non-hidden slides using `show` property)
 * Also filters sub-slides to include only visible ones
 * @param slides - Array of slides to filter
 * @returns Array of visible slides where show property is true, with filtered sub-slides
 */
export const getVisibleSlides = (slides: SlideConfigDto[]): SlideConfigDto[] => {
  // Handle null or undefined slides array
  if (!slides || slides.length === 0) {
    return [];
  }

  return slides
    .filter((slide) => slide.show === true)
    .map((slide) => ({
      ...slide,
      subSlides: slide.subSlides?.filter((subSlide) => subSlide.show === true) || [],
    }));
};

/**
 * Counts the number of visible slides
 * @param slides - Array of slides to count
 * @returns Number of visible slides
 */
export const getVisibleSlidesCount = (slides: SlideConfigDto[]): number => {
  return getVisibleSlides(slides).length;
};

/**
 * Filters sub-slides to return only visible ones
 * @param subSlides - Array of sub-slides to filter
 * @returns Array of visible sub-slides where show property is true
 */
export const getVisibleSubSlides = (subSlides: ISubSlide[]): ISubSlide[] => {
  return subSlides.filter((subSlide) => subSlide.show === true);
};

/**
 * Counts the total number of visible sub-slides across all slides
 * @param slides - Array of slides to count sub-slides from
 * @returns Total number of visible sub-slides
 */
export const getTotalVisibleSubSlidesCount = (slides: SlideConfigDto[]): number => {
  const visibleSlides = getVisibleSlides(slides);
  return visibleSlides.reduce((total, slide) => {
    return total + (slide.subSlides?.length || 0);
  }, 0);
};

/**
 * Enhanced function to get visible slides with generated subSlides for SlideConfigDto
 * @param slides - Array of SlideConfigDto to filter and process
 * @param intl - Internationalization instance for formatting
 * @param dataStations - Station data for histories processing
 * @param realData - Optional real DataHistory data for histories type
 * @returns Array of visible slides with generated subSlides
 */
export const getVisibleSlidesWithSubSlides = (
  slides: SlideConfigDto[],
  intl?: IntlShape,
  dataStations?: IDataStation,
  realData?: DataHistory[],
): SlideConfigDto[] => {
  // Handle null or undefined slides array
  if (!slides || slides.length === 0) {
    return [];
  }

  return slides
    .filter((slide) => slide.show === true)
    .map((slide) => {
      // Generate subSlides for this slide
      const subSlides = generateSubSlides(slide, intl, dataStations, realData);

      // Convert SlideConfigDto to ISlide format with subSlides
      return {
        ...slide,
        subSlides,
      };
    });
};

/**
 * Gets slides safe for presentation with basic validation
 * @param slides - Array of slides to process
 * @returns Array of slides safe for presentation
 */
export const getSlidesForPresentation = (slides: SlideConfigDto[]): SlideConfigDto[] => {
  // Handle null or undefined slides array
  if (!slides || slides.length === 0) {
    return [];
  }

  // Get visible slides
  const visibleSlides = getVisibleSlides(slides);

  // Return visible slides or empty array
  return visibleSlides;
};
