import { DataHistory } from '@/api/type';
import { IDataStation } from '@/types';
import { ISlide } from '@/types/slide';
import useEmblaCarousel from 'embla-carousel-react';
import React, { forwardRef, useEffect, useMemo, useRef } from 'react';
import { FormattedMessage, IntlShape } from 'react-intl';
import { toast, Toaster } from 'ui-components';
import SubSlideRenderer from './SubSlideRenderer';
import { cn } from '@/utils/tailwind';
import { EFontSupport } from '@/constants';
import { useLedConfigStores } from '@/stores';
import { createIframeSpecificStyles } from '@/utils/cssInjection';
import { getSlidesForPresentation, getVisibleSlidesWithSubSlides } from '@/utils/slideFilter';
import { validateSlidesForPresentation, createEmptyPresentationSlide } from '@/utils/slideValidation';
import { prepareData } from '@/utils/slide';
import { getLocalizedText } from '@/utils/languageHelper';
import { usePrevNextButtons } from '@/hooks/usePrevNextButtons';
import { usePresentationStores } from '@/stores/presentationStores';
import { usePresentationRecovery } from '@/hooks/usePresentationRecovery';
import PresentationErrorBoundary from '@/components/ui/PresentationErrorBoundary';
import SlideErrorBoundary from '@/components/ui/SlideErrorBoundary';
import PresentationControlBar from '@/components/ui/PresentationControlBar';
import { toast as sonnerToast } from 'sonner';

type Props = {
  presentations: ISlide[];
  onClose: () => void;
  intl?: IntlShape;
  dataStations?: IDataStation;
  realData?: DataHistory[];
};

const ContainerPresent = forwardRef<HTMLDivElement, Props>(
  ({ presentations, onClose, intl, dataStations, realData }, ref) => {
    const dataConfig = useLedConfigStores((store) => store.dataConfig);
    const defaultConfig = useMemo(() => {
      return {
        init: dataConfig?.init,
        slideConfig: dataConfig?.slide_config_default,
      };
    }, [dataConfig]);

    // Validate and prepare slides for presentation with error handling
    const slideValidation = useMemo(() => {
      // Handle null/undefined presentations array
      if (!presentations) {
        return {
          isValid: false,
          hasVisibleSlides: false,
          visibleSlidesCount: 0,
          totalSlidesCount: 0,
          errors: ['No presentation data provided'],
          warnings: [],
        };
      }
      return validateSlidesForPresentation(presentations);
    }, [presentations]);

    const visibleSlides = useMemo(() => {
      // Handle null/undefined presentations array
      if (!presentations) {
        return [createEmptyPresentationSlide()];
      }

      try {
        // Convert ISlide[] to SlideConfigDto[] for enhanced subSlides generation
        const slideConfigs = prepareData(presentations);

        if (slideConfigs && slideConfigs.length > 0) {
          const slidesWithSubSlides = getVisibleSlidesWithSubSlides(slideConfigs, intl, dataStations, realData);

          if (slidesWithSubSlides.length === 0) {
            console.warn('No visible slides from converted slideConfigs, using fallback');
            return [createEmptyPresentationSlide()];
          }

          return slidesWithSubSlides;
        }
      } catch (error) {
        console.error('Error converting presentations to slideConfigs or generating slides with subSlides:', error);
        // Fall back to legacy method
      }

      // Legacy fallback method
      const slides = getSlidesForPresentation(prepareData(presentations));

      // If no visible slides, provide fallback
      if (slides.length === 0) {
        return [createEmptyPresentationSlide()];
      }

      // Additional validation: ensure slides have required properties
      const validSlides = slides.filter((slide) => {
        return slide && slide.id && typeof slide.show === 'boolean';
      });

      if (validSlides.length === 0) {
        console.warn('All slides failed basic validation, using fallback');
        return [createEmptyPresentationSlide()];
      }

      return validSlides;
    }, [presentations, intl, dataStations, realData]);

    // Initialize presentation store with visible slides and get state/actions
    const {
      nextSlide,
      prevSlide,
      isAutoPlaying,
      toggleAutoPlay,
      startPresentation,
      stopPresentation,
      autoPlayInterval,
      canGoNext,
      canGoPrev,
      currentSlideIndex,
      currentSubSlideIndex,
      getCurrentSlide,
    } = usePresentationStores();

    // Initialize Embla carousel without autoplay plugin (we'll handle it manually)
    const [emblaRef, emblaApi] = useEmblaCarousel({
      loop: true, // Don't loop to respect slide boundaries
      containScroll: false,
      watchDrag: false, // Disable drag to prevent conflicts with controls
      skipSnaps: false,
    });

    // Initialize presentation recovery
    const {
      isRecovering,
      lastError,
      recoverAutoPlay,
      recoverNavigation,
      triggerManualRecovery,
      resetRecoveryState,
      hasRecoveryFailed,
    } = usePresentationRecovery({
      maxRetries: 3,
      retryDelay: 1000,
      onRecoverySuccess: () => {
        console.log('Presentation recovery successful');
      },
      onRecoveryFailure: (error) => {
        console.error('Presentation recovery failed:', error);
      },
      enableAutoRecovery: true,
    });

    // Custom auto-play implementation that handles sub-slides with recovery
    const autoPlayTimerRef = useRef<NodeJS.Timeout | null>(null);
    const autoPlayFailureCountRef = useRef(0);
    const maxAutoPlayFailures = 3;

    const startAutoPlay = useRef(() => {
      if (autoPlayTimerRef.current) {
        clearInterval(autoPlayTimerRef.current);
      }

      autoPlayTimerRef.current = setInterval(() => {
        try {
          const success = nextSlide();
          if (!success) {
            autoPlayFailureCountRef.current++;

            // If we can't go to next slide, attempt recovery
            console.warn(
              `Auto-play navigation failed (attempt ${autoPlayFailureCountRef.current}/${maxAutoPlayFailures}), attempting recovery`,
            );

            if (autoPlayFailureCountRef.current >= maxAutoPlayFailures) {
              console.error('Auto-play failed too many times, stopping auto-play');
              toggleAutoPlay();
              autoPlayFailureCountRef.current = 0;
              return;
            }

            recoverAutoPlay().then((recovered) => {
              if (recovered) {
                autoPlayFailureCountRef.current = 0; // Reset failure count on successful recovery
              } else if (autoPlayFailureCountRef.current >= maxAutoPlayFailures) {
                // If recovery failed and we've exceeded max failures, stop auto-play
                toggleAutoPlay();
                autoPlayFailureCountRef.current = 0;
              }
            });
          } else {
            // Reset failure count on successful navigation
            autoPlayFailureCountRef.current = 0;
          }
        } catch (error) {
          console.error('Auto-play error:', error);
          autoPlayFailureCountRef.current++;

          if (autoPlayFailureCountRef.current >= maxAutoPlayFailures) {
            console.error('Auto-play errors exceeded maximum, stopping auto-play');
            toggleAutoPlay();
            autoPlayFailureCountRef.current = 0;
            return;
          }

          recoverAutoPlay().then((recovered) => {
            if (recovered) {
              autoPlayFailureCountRef.current = 0;
            } else if (autoPlayFailureCountRef.current >= maxAutoPlayFailures) {
              toggleAutoPlay();
              autoPlayFailureCountRef.current = 0;
            }
          });
        }
      }, autoPlayInterval);
    });

    const stopAutoPlay = useRef(() => {
      if (autoPlayTimerRef.current) {
        clearInterval(autoPlayTimerRef.current);
        autoPlayTimerRef.current = null;
      }
    });

    // Update refs when dependencies change
    useEffect(() => {
      startAutoPlay.current = () => {
        if (autoPlayTimerRef.current) {
          clearInterval(autoPlayTimerRef.current);
        }

        autoPlayTimerRef.current = setInterval(() => {
          const success = nextSlide();
          if (!success) {
            // If we can't go to next slide, stop auto-play
            toggleAutoPlay();
          }
        }, autoPlayInterval);
      };
    }, [nextSlide, toggleAutoPlay, autoPlayInterval]);

    // Navigation controls using existing hook (only for compatibility, not used in current implementation)
    usePrevNextButtons(emblaApi);

    // Show initial welcome message and keyboard shortcuts - only run once on mount
    const hasShownToast = useRef(false);
    // const [showKeyboardHelp, setShowKeyboardHelp] = useState(true);

    useEffect(() => {
      if (!hasShownToast.current) {
        hasShownToast.current = true;
        toast({
          type: 'info',
          title: (
            <span className="text-current font-semibold text-sm text-primary-600">
              <FormattedMessage
                defaultMessage="Đang ở chế độ trình chiếu. Di chuột xuống phía dưới để hiển thị thanh điều khiển."
                id="screens.presentations.present.ContainerPresent.880002631"
              />
              <br />
              <FormattedMessage
                defaultMessage="Nhấn Esc để thoát, ← / → để chuyển trang, Space để tạm dừng/tiếp tục."
                id="screens.presentations.present.ContainerPresent.1174313492"
              />
            </span>
          ),
          options: {
            position: 'bottom-center',
            duration: 5000,
            id: 'presentation-welcome',
          },
        });
      }

      // Cleanup function to remove toast on unmount
      return () => {
        sonnerToast.dismiss('presentation-welcome');
      };
    }, []);

    // Initialize presentation when component mounts
    useEffect(() => {
      try {
        if (visibleSlides.length > 0) {
          startPresentation(visibleSlides);
        } else {
          console.warn('No visible slides available for presentation');
        }
      } catch (error) {
        console.error('Failed to start presentation:', error);
        // Could trigger recovery mechanism here if needed
      }

      return () => {
        console.log('[ContainerPresent] Cleanup: stopping presentation');
        try {
          stopPresentation();
        } catch (error) {
          console.error('Failed to stop presentation:', error);
        }
      };
    }, [visibleSlides, startPresentation, stopPresentation]);

    // Log validation warnings for debugging
    useEffect(() => {
      if (slideValidation.warnings.length > 0) {
        console.warn('Presentation validation warnings:', slideValidation.warnings);
      }
      if (slideValidation.errors.length > 0) {
        console.error('Presentation validation errors:', slideValidation.errors);
      }
    }, [slideValidation]);

    // Sync embla carousel with presentation store navigation
    useEffect(() => {
      console.log('[ContainerPresent] Syncing carousel with currentSlideIndex:', currentSlideIndex);
      if (emblaApi && currentSlideIndex >= 0 && currentSlideIndex < visibleSlides.length) {
        try {
          console.log('[ContainerPresent] Scrolling carousel to index:', currentSlideIndex);
          emblaApi.scrollTo(currentSlideIndex, false); // Don't animate to avoid conflicts
        } catch (error) {
          console.error('Failed to sync carousel position:', error);
          // Attempt recovery by resetting to first slide
          if (currentSlideIndex !== 0) {
            try {
              console.log('[ContainerPresent] Recovery: Scrolling carousel to index 0');
              emblaApi.scrollTo(0, false);
            } catch (recoveryError) {
              console.error('Failed to recover carousel position:', recoveryError);
            }
          }
        }
      }
    }, [emblaApi, currentSlideIndex, visibleSlides.length]);

    // Handle auto-play state changes
    // Use a ref to track the previous auto-play state to avoid unnecessary updates
    const previousAutoPlayStateRef = useRef(isAutoPlaying);

    useEffect(() => {
      // Only trigger changes if the auto-play state actually changed
      if (previousAutoPlayStateRef.current !== isAutoPlaying) {
        previousAutoPlayStateRef.current = isAutoPlaying;

        if (isAutoPlaying) {
          startAutoPlay.current();
        } else {
          stopAutoPlay.current();
        }
      }

      // Cleanup on unmount
      return () => {
        stopAutoPlay.current();
      };
    }, [isAutoPlaying]);

    // Handle embla carousel events to sync with presentation store
    useEffect(() => {
      if (!emblaApi) return;

      const onSelect = () => {
        try {
          // Sync embla carousel position with presentation store
          const selectedIndex = emblaApi.selectedScrollSnap();
          if (selectedIndex !== currentSlideIndex) {
            // Only update if there's a mismatch to avoid infinite loops
            // This can happen if user manually scrolls the carousel
            console.log(`Carousel position mismatch detected: carousel=${selectedIndex}, store=${currentSlideIndex}`);
          }
        } catch (error) {
          console.error('Error in carousel select handler:', error);
          // Attempt to recover by resetting carousel position
          try {
            emblaApi.scrollTo(currentSlideIndex, false);
          } catch (recoveryError) {
            console.error('Failed to recover carousel position:', recoveryError);
          }
        }
      };

      emblaApi.on('select', onSelect);

      return () => {
        try {
          emblaApi.off('select', onSelect);
        } catch (error) {
          console.warn('Error removing carousel event listeners:', error);
        }
      };
    }, [emblaApi, currentSlideIndex]);

    // Focus management for keyboard navigation
    useEffect(() => {
      // Focus the container when component mounts to enable keyboard navigation
      if (ref && typeof ref === 'object' && ref.current) {
        ref.current.focus();
      }
    }, [ref]);

    const handlePlayPause = () => {
      try {
        toggleAutoPlay();
      } catch (error) {
        console.error('Play/pause error:', error);
        // Don't attempt recovery for play/pause errors, just log them
      }
    };

    const handlePrevious = () => {
      try {
        const success = prevSlide();
        if (!success) {
          console.warn('Previous navigation failed, attempting recovery');
          recoverNavigation('prev');
        }
      } catch (error) {
        console.error('Previous navigation error:', error);
        recoverNavigation('prev');
      }
    };

    const handleNext = () => {
      try {
        const success = nextSlide();
        if (!success) {
          console.warn('Next navigation failed, attempting recovery');
          recoverNavigation('next');
        }
      } catch (error) {
        console.error('Next navigation error:', error);
        recoverNavigation('next');
      }
    };

    // Keyboard navigation support with error handling
    useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
        try {
          // Prevent default behavior for presentation keys
          switch (event.code) {
            case 'Space':
              event.preventDefault();
              handlePlayPause();
              break;
            case 'ArrowLeft':
              event.preventDefault();
              handlePrevious();
              break;
            case 'ArrowRight':
              event.preventDefault();
              handleNext();
              break;
            case 'ArrowUp':
              event.preventDefault();
              handlePrevious();
              break;
            case 'ArrowDown':
              event.preventDefault();
              handleNext();
              break;
            case 'Escape':
              event.preventDefault();
              onClose();
              break;
            case 'KeyR':
              // Manual recovery trigger with Ctrl+R
              if (event.ctrlKey) {
                event.preventDefault();
                console.log('Manual recovery triggered via keyboard');
                triggerManualRecovery();
              }
              break;
          }
        } catch (error) {
          console.error('Keyboard navigation error:', error);
          // Don't prevent the event if there's an error, let it bubble up
        }
      };

      try {
        // Add event listener when component mounts
        document.addEventListener('keydown', handleKeyDown);

        // Focus the container to ensure keyboard events are captured
        if (ref && typeof ref === 'object' && ref.current) {
          ref.current.focus();
        }
      } catch (error) {
        console.error('Failed to setup keyboard navigation:', error);
      }

      // Cleanup event listener when component unmounts
      return () => {
        try {
          document.removeEventListener('keydown', handleKeyDown);
        } catch (error) {
          console.warn('Failed to cleanup keyboard event listener:', error);
        }
      };
    }, [handlePlayPause, handlePrevious, handleNext, onClose, ref, triggerManualRecovery]);

    useEffect(() => {
      if (!defaultConfig.slideConfig) return;

      const styleId = 'present-dynamic-styles';

      try {
        // To avoid duplicates, first check if the style tag already exists.
        const existingStyleElement = document.getElementById(styleId);
        if (existingStyleElement) {
          return;
        }

        const cssString = createIframeSpecificStyles({
          background: defaultConfig.slideConfig.background,
          color: defaultConfig.slideConfig.color,
          borderColor: defaultConfig.slideConfig.borderColor,
        });

        const styleElement = document.createElement('style');
        styleElement.id = styleId;
        styleElement.textContent = cssString;
        document.head.appendChild(styleElement);
      } catch (error) {
        console.error('Failed to inject presentation styles:', error);
        // Continue without custom styles - presentation should still work with default styles
      }

      // Cleanup function to remove the style element when the component unmounts.
      return () => {
        try {
          const styleTagToRemove = document.getElementById(styleId);
          if (styleTagToRemove && styleTagToRemove.parentNode) {
            styleTagToRemove.parentNode.removeChild(styleTagToRemove);
          }
        } catch (error) {
          console.warn('Failed to cleanup presentation styles:', error);
        }
      };
    }, [defaultConfig]);

    // Handle slide errors
    const handleSlideError = (error: Error, errorInfo: any, slideIndex: number) => {
      console.error(`Slide ${slideIndex} error:`, error, errorInfo);
      // Could implement slide-specific recovery here if needed
    };

    const handleSlideRetry = (slideIndex: number) => {
      console.log(`Retrying slide ${slideIndex}`);
      // Force re-render by updating a state or triggering recovery
      resetRecoveryState();
    };

    const handleSlideSkip = (slideIndex: number) => {
      console.log(`Skipping slide ${slideIndex}`);
      // Navigate to next slide
      handleNext();
    };

    return (
      <PresentationErrorBoundary
        onError={(error, errorInfo) => {
          console.error('Presentation error:', error, errorInfo);
        }}
        onRetry={() => {
          console.log('Retrying presentation');
          triggerManualRecovery();
        }}
      >
        <div
          ref={ref}
          className="fixed inset-0 bg-black z-50 outline-none"
          tabIndex={-1}
          role="application"
          aria-label="Presentation viewer"
        >
          {ref && <Toaster duration={5000} />}

          {/* Presentation Control Bar - appears on hover */}
          <PresentationControlBar
            currentSlide={currentSlideIndex + 1}
            currentSubSlide={currentSubSlideIndex + 1}
            totalSlides={visibleSlides.length}
            slideTitle={getLocalizedText(getCurrentSlide()?.title || visibleSlides[currentSlideIndex]?.title, 'Mở đầu')}
            isPlaying={isAutoPlaying && !isRecovering}
            onPlayPause={handlePlayPause}
            onPrevious={handlePrevious}
            onNext={handleNext}
            onClose={onClose}
            prevDisabled={!canGoPrev() || isRecovering}
            nextDisabled={!canGoNext() || isRecovering}
            slideType={getCurrentSlide()?.content.type}
            dataSlide={getCurrentSlide()}
          />
          <div className="h-full w-full" ref={emblaRef}>
            <div className="embla__container flex h-full w-full">
              {visibleSlides.map((dataSlide, index) => {
                return (
                  <SlideErrorBoundary
                    key={index}
                    slideIndex={index}
                    slideId={dataSlide.id}
                    onError={handleSlideError}
                    onRetry={handleSlideRetry}
                    onSkip={handleSlideSkip}
                  >
                    <div className="embla__slide flex-[0_0_100%] min-w-0 h-full">
                      <SubSlideRenderer
                        slide={dataSlide}
                        isIframeReady={true}
                        isTargeted={index === currentSlideIndex}
                        className={cn(
                          'flex flex-col justify-center items-center w-full h-full flex-shrink-0',
                          { 'font-inter': defaultConfig.slideConfig?.font_family === EFontSupport.Inter },
                          { 'font-montserrat': defaultConfig.slideConfig?.font_family === EFontSupport.Montserrat },
                          { 'font-tektur': defaultConfig.slideConfig?.font_family === EFontSupport.Tektur },
                          {
                            'font-jetbrains-mono':
                              defaultConfig.slideConfig?.font_family === EFontSupport['JetBrains Mono'],
                          },
                          {
                            'font-be-vietnam-pro':
                              defaultConfig.slideConfig?.font_family === EFontSupport['Be Vietnam Pro'],
                          },
                        )}
                      />
                    </div>
                  </SlideErrorBoundary>
                );
              })}
            </div>
          </div>
        </div>
      </PresentationErrorBoundary>
    );
  },
);

ContainerPresent.displayName = 'ContainerPresent';

export default ContainerPresent;
