import { cn } from '@/utils/tailwind';
import { 
  PlayIcon, 
  PauseIcon, 
  CaretLeftIcon, 
  CaretRightIcon 
} from '@phosphor-icons/react';
import { Button } from 'ui-components';

interface CarouselControlsProps {
  isPlaying: boolean;
  onPlayPause: () => void;
  onPrevious: () => void;
  onNext: () => void;
  prevDisabled?: boolean;
  nextDisabled?: boolean;
  className?: string;
}

const CarouselControls = ({
  isPlaying,
  onPlayPause,
  onPrevious,
  onNext,
  prevDisabled = false,
  nextDisabled = false,
  className,
}: CarouselControlsProps) => {
  return (
    <div className={cn(
      'flex items-center gap-2 bg-black/50 backdrop-blur-sm rounded-lg p-2',
      className
    )}>
      {/* Previous Button */}
      <Button
        variant="ghost"
        size="icon"
        type='button'
        onClick={onPrevious}
        disabled={prevDisabled}
        className="text-white hover:bg-white/20 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
        aria-label="Previous slide"
      >
        <CaretLeftIcon size={20} />
      </Button>

      {/* Play/Pause Button */}
      <Button
        variant="ghost"
        size="icon"
        type='button'
        onClick={onPlayPause}
        className="text-white hover:bg-white/20 hover:text-white"
        aria-label={isPlaying ? 'Pause presentation' : 'Play presentation'}
      >
        {isPlaying ? <PauseIcon size={20} /> : <PlayIcon size={20} />}
      </Button>

      {/* Next Button */}
      <Button
        variant="ghost"
        size="icon"
        type='button'
        onClick={onNext}
        disabled={nextDisabled}
        className="text-white hover:bg-white/20 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
        aria-label="Next slide"
      >
        <CaretRightIcon size={20} />
      </Button>
    </div>
  );
};

export default CarouselControls;