'use client';

import { routePath } from '@/constants';
import { useLedConfigStores, useLedStores } from '@/stores';
import { GearIcon } from '@phosphor-icons/react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import Demo from '../sessions/Demo';

export default function GuardLed() {
  const { isLogined, isInitConfigured, setRedirectTo } = useLedStores((store) => store);
  const { setIsLoginedConfig } = useLedConfigStores((store) => store);
  const router = useRouter();
  const params = useParams();
  const { led_key } = params as { led_key: string };

  useEffect(() => {
    setIsLoginedConfig(false);

    if (!isInitConfigured || !isLogined) {
      router.push(routePath.ledAuth(led_key));
    }
  }, [isInitConfigured, isLogined]);

  if (!isInitConfigured || !isLogined) return null;

  return (
    <div className="relative h-full w-full">
      <Demo />
      <div
        onClick={() => {
          const path = routePath.ledConfig(led_key);
          setRedirectTo(path);
          router.push(path);
        }}
        className="absolute bottom-5 right-5 cursor-pointer"
      >
        <GearIcon size={32} weight="regular" />
      </div>
    </div>
  );
}
