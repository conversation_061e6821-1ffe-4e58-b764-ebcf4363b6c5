import { initValueSlide } from '@/constants/slide';
import { ISlide, ISubSlide, SlideConfigDto } from '@/types/slide';
import { prepareDataSlide } from './slide';

export interface SlideValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface PresentationValidationResult {
  isValid: boolean;
  hasVisibleSlides: boolean;
  visibleSlidesCount: number;
  totalSlidesCount: number;
  errors: string[];
  warnings: string[];
}

/**
 * Validates a single slide for presentation readiness
 * @param slide - The slide to validate
 * @param index - The index of the slide for error reporting
 * @returns Validation result with errors and warnings
 */
export const validateSlide = (slide: ISlide, index: number): SlideValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if slide has required properties
  if (!slide.id) {
    errors.push(`Slide ${index + 1}: Missing required 'id' property`);
  }

  if (!slide.title) {
    warnings.push(`Slide ${index + 1}: Missing title`);
  }

  // Check if slide has valid content
  if (!slide.content) {
    errors.push(`Slide ${index + 1}: Missing content configuration`);
  } else {
    // Check content type and metadata
    if (slide.content.show && !slide.content.type) {
      errors.push(`Slide ${index + 1}: Content is set to show but no content type specified`);
    }

    if (slide.content.show && slide.content.type && !slide.content.metadata) {
      errors.push(`Slide ${index + 1}: Content type '${slide.content.type}' specified but no metadata provided`);
    }
  }

  // Validate sub-slides if they exist
  if (slide.subSlides && slide.subSlides.length > 0) {
    slide.subSlides.forEach((subSlide, subIndex) => {
      const subSlideValidation = validateSubSlide(subSlide, index, subIndex);
      errors.push(...subSlideValidation.errors);
      warnings.push(...subSlideValidation.warnings);
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Validates a single sub-slide
 * @param subSlide - The sub-slide to validate
 * @param slideIndex - The parent slide index for error reporting
 * @param subSlideIndex - The sub-slide index for error reporting
 * @returns Validation result with errors and warnings
 */
export const validateSubSlide = (
  subSlide: ISubSlide,
  slideIndex: number,
  subSlideIndex: number,
): SlideValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!subSlide.id) {
    errors.push(`Slide ${slideIndex + 1}, Sub-slide ${subSlideIndex + 1}: Missing required 'id' property`);
  }

  if (!subSlide.content) {
    warnings.push(`Slide ${slideIndex + 1}, Sub-slide ${subSlideIndex + 1}: Missing content`);
  }

  // Check duration if specified
  if (subSlide.duration !== undefined && (subSlide.duration <= 0 || subSlide.duration > 60000)) {
    warnings.push(
      `Slide ${slideIndex + 1}, Sub-slide ${
        subSlideIndex + 1
      }: Duration should be between 1ms and 60000ms (60 seconds)`,
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Validates an array of slides for presentation readiness
 * @param slides - Array of slides to validate
 * @returns Comprehensive validation result
 */
export const validateSlidesForPresentation = (slides: ISlide[]): PresentationValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if slides array is empty or null
  if (!slides || slides.length === 0) {
    errors.push('No slides provided for presentation');
    return {
      isValid: false,
      hasVisibleSlides: false,
      visibleSlidesCount: 0,
      totalSlidesCount: 0,
      errors,
      warnings,
    };
  }

  // Validate each slide
  slides.forEach((slide, index) => {
    const slideValidation = validateSlide(slide, index);
    errors.push(...slideValidation.errors);
    warnings.push(...slideValidation.warnings);
  });

  // Check for visible slides
  const visibleSlides = slides.filter((slide) => slide.show === true);
  const hasVisibleSlides = visibleSlides.length > 0;

  if (!hasVisibleSlides) {
    errors.push('No visible slides found. All slides are hidden (show: false)');
  }

  // Check for slides with valid content
  const slidesWithContent = visibleSlides.filter(
    (slide) => slide.content && slide.content.show && slide.content.type && slide.content.metadata,
  );

  if (hasVisibleSlides && slidesWithContent.length === 0) {
    warnings.push('No visible slides have valid content configuration');
  }

  // Check for duplicate slide IDs
  const slideIds = slides.map((slide) => slide.id).filter(Boolean);
  const duplicateIds = slideIds.filter((id, index) => slideIds.indexOf(id) !== index);
  if (duplicateIds.length > 0) {
    errors.push(`Duplicate slide IDs found: ${duplicateIds.join(', ')}`);
  }

  return {
    isValid: errors.length === 0,
    hasVisibleSlides,
    visibleSlidesCount: visibleSlides.length,
    totalSlidesCount: slides.length,
    errors,
    warnings,
  };
};

/**
 * Creates a fallback slide for error scenarios
 * @param errorMessage - The error message to display
 * @returns A fallback slide with error information
 */
export const createErrorSlide = (errorMessage: string): SlideConfigDto => {
  return prepareDataSlide({
    ...initValueSlide,
    id: 'error-slide',
  });
};

/**
 * Creates a fallback slide for empty presentation scenarios
 * @returns A fallback slide for empty presentations
 */
export const createEmptyPresentationSlide = (): SlideConfigDto => {
  return createErrorSlide('Không có slide nào để trình chiếu. Vui lòng thêm slide và đảm bảo chúng được hiển thị.');
};

/**
 * Checks if a slide array is safe for presentation
 * @param slides - Array of slides to check
 * @returns True if slides are safe for presentation, false otherwise
 */
export const isSafeForPresentation = (slides: ISlide[]): boolean => {
  const validation = validateSlidesForPresentation(slides);
  return validation.isValid && validation.hasVisibleSlides;
};
