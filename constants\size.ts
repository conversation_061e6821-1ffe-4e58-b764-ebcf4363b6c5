export const responsive = {
  grid: {
    // Breakpoint 320px (Extra Small)
    '320-2-2': {
      measureValue: '18px',
      measureName: '14px',
      measureUnit: '12px',
      limit: '12px',
    },
    '320-2-3': {
      measureValue: '18px',
      measureName: '14px',
      measureUnit: '12px',
      limit: '12px',
    },
    '320-2-4': {
      measureValue: '16px',
      measureName: '12px',
      measureUnit: '11px',
      limit: '11px',
    },
    '320-3-2': {
      measureValue: '16px',
      measureName: '12px',
      measureUnit: '11px',
      limit: '11px',
    },
    '320-3-3': {
      measureValue: '16px',
      measureName: '12px',
      measureUnit: '11px',
      limit: '11px',
    },
    '320-3-4': {
      measureValue: '13px',
      measureName: '11px',
      measureUnit: '9px',
      limit: '9px',
    },
    '320-4-2': {
      measureValue: '13px',
      measureName: '11px',
      measureUnit: '9px',
      limit: '9px',
    },
    '320-4-3': {
      measureValue: '13px',
      measureName: '11px',
      measureUnit: '9px',
      limit: '9px',
    },
    '320-4-4': {
      measureValue: '13px',
      measureName: '11px',
      measureUnit: '9px',
      limit: '9px',
    },
    '320-5-2': {
      measureValue: '12px',
      measureName: '10px',
      measureUnit: '8px',
      limit: '8px',
    },
    '320-5-3': {
      measureValue: '12px',
      measureName: '10px',
      measureUnit: '8px',
      limit: '8px',
    },
    '320-5-4': {
      measureValue: '12px',
      measureName: '10px',
      measureUnit: '8px',
      limit: '8px',
    },

    // Breakpoint 640px (Small)
    '640-2-2': {
      h1: { fontSize: '14px' },
      h2: { fontSize: '12px' },
      h3: { fontSize: '10px' },
      h4: { fontSize: '8px' },
    },
    '640-2-3': {
      h1: { fontSize: '12px' },
      h2: { fontSize: '10px' },
      h3: { fontSize: '8px' },
      h4: { fontSize: '6px' },
    },
    '640-2-4': {
      h1: { fontSize: '10px' },
      h2: { fontSize: '8px' },
      h3: { fontSize: '6px' },
      h4: { fontSize: '4px' },
    },

    // Breakpoint 768px (Medium)
    '768-2-2': {
      h1: { fontSize: '16px' },
      h2: { fontSize: '14px' },
      h3: { fontSize: '12px' },
      h4: { fontSize: '10px' },
    },
    '768-2-3': {
      h1: { fontSize: '14px' },
      h2: { fontSize: '12px' },
      h3: { fontSize: '10px' },
      h4: { fontSize: '8px' },
    },
    '768-2-4': {
      h1: { fontSize: '12px' },
      h2: { fontSize: '10px' },
      h3: { fontSize: '8px' },
      h4: { fontSize: '6px' },
    },
    '768-2-5': {
      h1: { fontSize: '10px' },
      h2: { fontSize: '8px' },
      h3: { fontSize: '6px' },
      h4: { fontSize: '4px' },
    },

    // Breakpoint 1024px (Large)
    '1024-2-2': {
      h1: { fontSize: '18px' },
      h2: { fontSize: '16px' },
      h3: { fontSize: '14px' },
      h4: { fontSize: '12px' },
    },
    '1024-2-3': {
      h1: { fontSize: '16px' },
      h2: { fontSize: '14px' },
      h3: { fontSize: '12px' },
      h4: { fontSize: '10px' },
    },
    '1024-2-4': {
      h1: { fontSize: '14px' },
      h2: { fontSize: '12px' },
      h3: { fontSize: '10px' },
      h4: { fontSize: '8px' },
    },
    '1024-2-5': {
      h1: { fontSize: '12px' },
      h2: { fontSize: '10px' },
      h3: { fontSize: '8px' },
      h4: { fontSize: '6px' },
    },

    // Breakpoint 1080px (Large+)
    '1080-2-2': {
      h1: { fontSize: '20px' },
      h2: { fontSize: '18px' },
      h3: { fontSize: '16px' },
      h4: { fontSize: '14px' },
    },
    '1080-2-3': {
      h1: { fontSize: '18px' },
      h2: { fontSize: '16px' },
      h3: { fontSize: '14px' },
      h4: { fontSize: '12px' },
    },
    '1080-2-4': {
      h1: { fontSize: '16px' },
      h2: { fontSize: '14px' },
      h3: { fontSize: '12px' },
      h4: { fontSize: '10px' },
    },
    '1080-2-5': {
      h1: { fontSize: '14px' },
      h2: { fontSize: '12px' },
      h3: { fontSize: '10px' },
      h4: { fontSize: '8px' },
    },

    // Breakpoint 1280px (X-Large)
    '1280-2-2': {
      h1: { fontSize: '22px' },
      h2: { fontSize: '20px' },
      h3: { fontSize: '18px' },
      h4: { fontSize: '16px' },
    },
    '1280-2-3': {
      h1: { fontSize: '20px' },
      h2: { fontSize: '18px' },
      h3: { fontSize: '16px' },
      h4: { fontSize: '14px' },
    },
    '1280-2-4': {
      h1: { fontSize: '18px' },
      h2: { fontSize: '16px' },
      h3: { fontSize: '14px' },
      h4: { fontSize: '12px' },
    },
    '1280-2-5': {
      h1: { fontSize: '16px' },
      h2: { fontSize: '14px' },
      h3: { fontSize: '12px' },
      h4: { fontSize: '10px' },
    },

    // Breakpoint 1920px (XX-Large)
    '1920-2-2': {
      h1: { fontSize: '24px' },
      h2: { fontSize: '22px' },
      h3: { fontSize: '20px' },
      h4: { fontSize: '18px' },
    },
    '1920-2-3': {
      h1: { fontSize: '22px' },
      h2: { fontSize: '20px' },
      h3: { fontSize: '18px' },
      h4: { fontSize: '16px' },
    },
    '1920-2-4': {
      h1: { fontSize: '20px' },
      h2: { fontSize: '18px' },
      h3: { fontSize: '16px' },
      h4: { fontSize: '14px' },
    },
    '1920-2-5': {
      h1: { fontSize: '18px' },
      h2: { fontSize: '16px' },
      h3: { fontSize: '14px' },
      h4: { fontSize: '12px' },
    },
  },
  table: {
    // Breakpoint 320px (Extra Small)
    '320-4': {
      fontSize: '16px',
    },
    '320-5': {
      fontSize: '15px',
    },
    '320-6': {
      fontSize: '14px',
    },
    '320-7': {
      fontSize: '14px',
    },
    '320-8': {
      fontSize: '13px',
    },
    '320-9': {
      fontSize: '11px',
    },
    '320-10': {
      fontSize: '10px',
    },

    // Breakpoint 640px (Small)
    '640-4': {
      fontSize: '10px',
    },
    '640-5': {
      fontSize: '8px',
    },
    '640-6': {
      fontSize: '6px',
    },
    '640-7': {
      fontSize: '5px',
    },
    '640-8': {
      fontSize: '4px',
    },
    '640-9': {
      fontSize: '3px',
    },
    '640-10': {
      fontSize: '2px',
    },

    // Breakpoint 768px (Medium)
    '768-4': {
      fontSize: '12px',
    },
    '768-5': {
      fontSize: '10px',
    },
    '768-6': {
      fontSize: '8px',
    },
    '768-7': {
      fontSize: '7px',
    },
    '768-8': {
      fontSize: '6px',
    },
    '768-9': {
      fontSize: '5px',
    },
    '768-10': {
      fontSize: '4px',
    },

    // Breakpoint 1024px (Large)
    '1024-4': {
      fontSize: '14px',
    },
    '1024-5': {
      fontSize: '12px',
    },
    '1024-6': {
      fontSize: '10px',
    },
    '1024-7': {
      fontSize: '9px',
    },
    '1024-8': {
      fontSize: '8px',
    },
    '1024-9': {
      fontSize: '7px',
    },
    '1024-10': {
      fontSize: '6px',
    },

    // Breakpoint 1080px (Large+)
    '1080-4': {
      fontSize: '16px',
    },
    '1080-5': {
      fontSize: '14px',
    },
    '1080-6': {
      fontSize: '12px',
    },
    '1080-7': {
      fontSize: '11px',
    },
    '1080-8': {
      fontSize: '10px',
    },
    '1080-9': {
      fontSize: '9px',
    },
    '1080-10': {
      fontSize: '8px',
    },

    // Breakpoint 1280px (X-Large)
    '1280-4': {
      fontSize: '18px',
    },
    '1280-5': {
      fontSize: '16px',
    },
    '1280-6': {
      fontSize: '14px',
    },
    '1280-7': {
      fontSize: '13px',
    },
    '1280-8': {
      fontSize: '12px',
    },
    '1280-9': {
      fontSize: '11px',
    },
    '1280-10': {
      fontSize: '10px',
    },

    // Breakpoint 1920px (XX-Large)
    '1920-4': {
      fontSize: '20px',
    },
    '1920-5': {
      fontSize: '18px',
    },
    '1920-6': {
      fontSize: '16px',
    },
    '1920-7': {
      fontSize: '15px',
    },
    '1920-8': {
      fontSize: '14px',
    },
    '1920-9': {
      fontSize: '13px',
    },
    '1920-10': {
      fontSize: '12px',
    },
  },
};
