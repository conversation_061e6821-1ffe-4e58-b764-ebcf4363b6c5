import { NextRequest, NextResponse } from 'next/server';
import { initLanguage } from './actions/detectLanguage';

export async function middleware(request: NextRequest) {
  const response = NextResponse.next();
  await initLanguage(request, response);
  const host = request.headers.get('host') || request.headers.get('x-forwarded-host') || '';
  const protocol = process.env.NODE_ENV === 'development' ? 'http' : 'https';
  const origin = `${protocol}://${host}`;
  response.headers.set('x-domain', origin);
  return response;
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|images|icons|favicon.ico|sitemap.xml|robots.txt).*)', { source: '/' }],
};
