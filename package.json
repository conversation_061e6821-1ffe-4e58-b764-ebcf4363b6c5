{"name": "web-public", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3004", "build": "next build", "start": "next start -p 3004", "lint": "next lint", "intl-extract": "formatjs extract 'components/**/*.{ts,tsx}' 'app/**/*.{ts,tsx}' 'constants/**/*.{ts,tsx}' 'hooks/**/*.{ts,tsx}' 'screens/**/*.{ts,tsx}' 'utils/**/*.{ts,tsx}' --ignore='**/*.d.ts' --out-file locales/en.json --extract-source-location --format formatter.js", "extract": "formatjs extract", "compile": "formatjs compile"}, "dependencies": {"@dnd-kit/abstract": "^0.1.20", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@formatjs/intl": "^3.1.6", "@formatjs/intl-localematcher": "^0.6.1", "@formatjs/ts-transformer": "^3.13.34", "@phosphor-icons/react": "^2.1.10", "@tanstack/react-query": "^5.80.2", "@tanstack/react-query-devtools": "^5.80.2", "axios": "^1.9.0", "dayjs": "^1.11.13", "embla-carousel": "^8.6.0", "embla-carousel-auto-height": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-class-names": "^8.6.0", "embla-carousel-fade": "^8.6.0", "embla-carousel-react": "^8.6.0", "negotiator": "^1.0.0", "next": "15.3.3", "next-runtime-env": "^3.3.0", "nuqs": "^2.4.3", "qs": "^6.14.0", "query-string": "^9.2.2", "rc-input-number": "^9.5.0", "react": "^19.0.0", "react-color-palette": "^7.3.0", "react-dom": "^19.0.0", "react-frame-component": "^5.2.7", "react-intl": "^7.1.11", "server-only": "^0.0.1", "ui-components": "gitlab:vietan-software/projects/ilotuslandx-document/ui-components#v2.1.20", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@formatjs/cli": "^6.7.1", "@types/negotiator": "^0.6.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.5.4", "tailwindcss": "^3.4.1", "typescript": "^5"}}