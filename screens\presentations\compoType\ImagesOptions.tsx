import { ImageMode } from '@/types/slide';
import { Fragment, useMemo } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Combobox, Form } from 'ui-components';

type PropsOptions = {
  focusedIndex: number;
};

export default function ImagesOptions({ focusedIndex }: PropsOptions) {
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';
  const modeImageOptions = useMemo(() => {
    return [
      {
        title: intl.formatMessage({
          defaultMessage: 'Vừa khung',
          id: 'screens.presentations.compoType.ImagesOptions.706739459',
        }),
        value: ImageMode.contain,
      },
      {
        title: intl.formatMessage({
          defaultMessage: 'Đầy khung',
          id: 'screens.presentations.compoType.ImagesOptions.312587117',
        }),
        value: ImageMode.cover,
      },
    ];
  }, [locale]);
  return (
    <Fragment>
      <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
        <span className="font-semibold text-base text-gray-700">
          <FormattedMessage defaultMessage="Cấu hình hiển thị" id="screens.presentations.ConfigSlide.206334904" />
        </span>
        <div className="flex flex-col gap-2">
          <span className="font-medium text-sm text-gray-700">
            <FormattedMessage
              defaultMessage="Chế độ hiển thị"
              id="screens.presentations.compoType.ImagesOptions.211939211"
            />
          </span>
          <div className="h-full w-full">
            <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'mode']}>
              <Combobox
                options={modeImageOptions}
                placeholder=""
                contentClassName="p-1 rounded-xl [&_div.selected]:bg-gray-100 [&_[cmdk-group-items]]:flex [&_[cmdk-group-items]]:flex-col [&_[cmdk-group-items]]:gap-1"
                itemClassName="bg-white hover:bg-gray-100 data-[selected=true]:bg-gray-100 text-gray-700 cursor-pointer p-2"
                sideOffset={-45}
                modalPopover
              />
            </Form.Field>
          </div>
        </div>
      </div>
    </Fragment>
  );
}
