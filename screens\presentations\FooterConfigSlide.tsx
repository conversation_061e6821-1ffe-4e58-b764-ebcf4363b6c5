import GroupBox from '@/components/sessions/GroupBox';
import InputLanguage from '@/components/ui/InputLanguage/InputLanguage';
import { NumberInput } from '@/components/ui/NumberInput';
import { PaletteColorPicker } from '@/components/ui/PaletteColorPicker';
import { footerType } from '@/constants';
import { useResponsiveFontSize } from '@/hooks/useResponsiveFontSize';
import { useSlideConfigStores } from '@/stores';
import { ISlide } from '@/types/slide';
import { cn } from '@/utils/tailwind';
import { useEffect, useMemo } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Combobox, Form, FormInstance, Switch } from 'ui-components';
import { PRIORITY_SCALE } from './components/ResponsiveTextWithOverride';
import { priorityText } from './compoType/constants';

type FormData = {
  width: number;
  height: number;
  presentations: ISlide[];
};

type Props = {
  focusedIndex: number;
  dataFooter: ISlide['footer'];
  form: FormInstance<FormData>;
  initFont: {
    [x: string]: number;
  };
};

export default function FooterConfigSlide({
  focusedIndex,
  dataFooter,
  form,
  initFont,
  ...props
}: Props & React.HTMLAttributes<HTMLDivElement>) {
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';
  const dataTypeOptions = useMemo(
    () => [
      {
        title: intl.formatMessage({
          defaultMessage: 'Banner chạy chữ',
          id: 'screens.presentations.FooterConfigSlide.1909588997',
        }),
        value: footerType.bannerText,
      },
      {
        title: intl.formatMessage({
          defaultMessage: 'Thời gian hiện tại',
          id: 'screens.presentations.FooterConfigSlide.1461773419',
        }),
        value: footerType.timestamp,
      },
    ],
    [locale],
  );

  return (
    <div {...props} className={cn('flex-1 min-w-0 flex flex-col gap-4 visible opacity-100', props.className)}>
      <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
        <span className="font-semibold text-base text-gray-700">
          <FormattedMessage defaultMessage="Cấu hình tổng quan" id="screens.presentations.ConfigSlide.257768898" />
        </span>
        <div className="flex flex-col gap-2">
          <span className="font-medium text-sm text-gray-700">
            <FormattedMessage defaultMessage="Loại dữ liệu" id="screens.presentations.ConfigSlide.1454429291" />
          </span>
          <div className="h-full w-full">
            <Form.Field name={['presentations', focusedIndex, 'footer', 'type']}>
              <Combobox
                options={dataTypeOptions}
                placeholder=""
                contentClassName="p-1 rounded-xl [&_div.selected]:bg-gray-100 [&_[cmdk-group-items]]:flex [&_[cmdk-group-items]]:flex-col [&_[cmdk-group-items]]:gap-1"
                itemClassName="bg-white hover:bg-gray-100 data-[selected=true]:bg-gray-100 text-gray-700 cursor-pointer p-2"
              />
            </Form.Field>
          </div>
        </div>
        {dataFooter.type === footerType.bannerText && (
          <div className="flex flex-col gap-2">
            <span className="font-medium text-sm text-gray-700">
              <FormattedMessage defaultMessage="Nội dung" id="screens.presentations.FooterConfigSlide.768903020" />
            </span>
            <div className="h-full w-full">
              <Form.Field
                name={['presentations', focusedIndex, 'footer', 'bannerText', 'content']}
                valuePropName="value"
                trigger="onBlur"
              >
                <InputLanguage
                  placeholder={intl.formatMessage({
                    defaultMessage: 'Nhập nội dung',
                    id: 'screens.presentations.FooterConfigSlide.901769905',
                  })}
                  name="title"
                  type="textarea"
                />
              </Form.Field>
            </div>
          </div>
        )}
      </div>
      <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
        <span className="font-semibold text-base text-gray-700">
          <FormattedMessage defaultMessage="Cấu hình hiển thị" id="screens.presentations.FooterConfigSlide.206334904" />
        </span>
        <div className="flex flex-col gap-2">
          <span className="font-medium text-sm text-gray-700">
            <FormattedMessage defaultMessage="Loại dữ liệu" id="screens.presentations.ConfigSlide.1454429291" />
          </span>
          <GroupBox>
            <div className="flex flex-row">
              <div className="h-11 flex-1 min-w-0 p-3 border-r border-gray-200">
                <span className="font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Màu nền"
                    id="app.led.[led_key].(configure).config.slide-shows.page.1845624381"
                  />
                </span>
              </div>
              <div className="w-[160px] flex-shrink-0">
                <Form.Field name={['presentations', focusedIndex, 'footer', 'config', 'background']}>
                  <PaletteColorPicker className="border-none" />
                </Form.Field>
              </div>
            </div>
            <div className="flex flex-row">
              <div className="h-11 flex-1 min-w-0 p-3 border-r border-gray-200">
                <span className="font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Màu chữ"
                    id="app.led.[led_key].(configure).config.slide-shows.page.1845380780"
                  />
                </span>
              </div>
              <div className="w-[160px] flex-shrink-0">
                <Form.Field name={['presentations', focusedIndex, 'footer', 'config', 'color']}>
                  <PaletteColorPicker className="border-none" />
                </Form.Field>
              </div>
            </div>
          </GroupBox>
        </div>
      </div>
      <Form.Field name={['presentations', focusedIndex, 'footer', 'fontSize', 'auto']}>
        {(control) => {
          return (
            <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
              <span className="font-semibold text-base text-gray-700">
                <FormattedMessage
                  defaultMessage="Kích thước kiểu chữ"
                  id="screens.presentations.ConfigSlide.1435964239"
                />
              </span>
              <div className="flex flex-col gap-2">
                <span className="font-medium text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage=" Cấu hình kích thước"
                    id="screens.presentations.ConfigSlide.641803552"
                  />
                </span>
                <div className="h-full w-full">
                  <GroupBox>
                    <div className="flex flex-row gap-3 p-3 items-center">
                      <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                        <FormattedMessage
                          defaultMessage="Tự động chọn kích thước phù hợp"
                          id="screens.presentations.ConfigSlide.320775361"
                        />
                      </span>
                      <Form.Field name={['presentations', focusedIndex, 'footer', 'fontSize', 'auto']}>
                        {(control) => (
                          <Switch
                            className="flex-shrink-0"
                            checked={control.value}
                            onCheckedChange={(checked) => {
                              control.onChange(checked);
                              if (checked) {
                                form.setFieldValue(
                                  ['presentations', focusedIndex, 'footer', 'fontSize', 'config'],
                                  initFont,
                                );
                              }
                            }}
                          />
                        )}
                      </Form.Field>
                    </div>
                  </GroupBox>
                </div>
                {control.value && (
                  <span className="font-normal text-xs text-gray-700">
                    <FormattedMessage
                      defaultMessage="Hệ thống sẽ tự động điều chỉnh kích thước nội dung để phù hợp với kích thước màn hình."
                      id="screens.presentations.HeaderConfigSlide.291032092"
                    />
                  </span>
                )}
              </div>
              {!control.value && (
                <div className="flex flex-col gap-2">
                  <span className="font-medium text-sm text-gray-700">
                    <FormattedMessage
                      defaultMessage="Tùy biến riêng"
                      id="screens.presentations.ConfigSlide.1975998546"
                    />
                  </span>
                  <div className="h-full w-full">
                    <GroupBox>
                      <div className="flex flex-row items-center bg-gray-50">
                        <span className="flex-1 min-w-0 p-3 font-semibold text-xs text-gray-700 uppercase">
                          <FormattedMessage
                            defaultMessage="Nội dung"
                            id="screens.presentations.HeaderConfigSlide.768903020"
                          />
                        </span>
                        <span className="w-[110px] flex-shrink-0 p-3 font-semibold text-xs text-gray-700 uppercase text-center">
                          <FormattedMessage
                            defaultMessage="Kích thước"
                            id="screens.presentations.HeaderConfigSlide.1145935829"
                          />
                        </span>
                      </div>
                      <div className="flex flex-row items-center">
                        <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                          <FormattedMessage
                            defaultMessage="Nội dung"
                            id="screens.presentations.HeaderConfigSlide.768903020"
                          />
                        </span>
                        <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                          <Form.Field
                            name={['presentations', focusedIndex, 'footer', 'fontSize', 'config', 'content']}
                            trigger="onBlurInput"
                          >
                            <NumberInput
                              precision={0}
                              placeholder=""
                              suffix="px"
                              defaultValue={dataFooter.fontSize.config.content}
                              classNameWrapper="h-[33px]"
                              clearable={false}
                              controls={false}
                              keyboard
                            />
                          </Form.Field>
                        </div>
                      </div>
                    </GroupBox>
                  </div>
                  <span className="font-normal text-xs text-gray-700">
                    <FormattedMessage
                      defaultMessage="Cho phép tùy chỉnh kích thước nội dung hiển thị trên màn hình. Hệ thống sẽ không tự động thay đổi kích thước."
                      id="screens.presentations.HeaderConfigSlide.595062375"
                    />
                  </span>
                </div>
              )}
            </div>
          );
        }}
      </Form.Field>
    </div>
  );
}
