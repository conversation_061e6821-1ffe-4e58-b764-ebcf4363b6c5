import { dataTypeObject } from '@/constants';
import { messageContentType } from '@/constants/defineMessages';
import { cn } from '@/utils/tailwind';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { CopyIcon, DotsSixVerticalIcon, EyeIcon, EyeSlashIcon, TrashIcon } from '@phosphor-icons/react';
import Image from 'next/image';
import { useMemo, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Form,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from 'ui-components';

type PropsCardSlider = {
  id: string;
  focusedIndex: number;
  name: number;
  isDeleted: boolean;
  isHidden: boolean;
  onChangeItem: (value: number) => void;
  onRemove?: (index: number) => void;
  onUpdateToggle: () => void;
  onDuplicate: () => void;
};

function CardSlider({
  id,
  focusedIndex,
  onChangeItem,
  name,
  onRemove,
  isDeleted,
  onUpdateToggle,
  isHidden,
  onDuplicate,
  ...props
}: PropsCardSlider & React.HTMLAttributes<HTMLDivElement>) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id,
    transition: {
      duration: 200,
      easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: isDragging ? 'none' : transition,
    zIndex: isDragging ? 1000 : 'auto',
  };

  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';

  const [isOpen, setIsOpen] = useState(false);
  const [isOpenModalDel, setIsOpenModalDel] = useState(false);

  // Biến để theo dõi thời gian và vị trí khi nhấn chuột
  const [dragTimeout, setDragTimeout] = useState<NodeJS.Timeout | null>(null);
  const [dragStartPosition, setDragStartPosition] = useState<{ x: number; y: number } | null>(null);
  const [isDragIntent, setIsDragIntent] = useState(false);

  // Xử lý khi nhấn chuột xuống
  const handleMouseDown = (event: React.MouseEvent) => {
    event.stopPropagation();

    // Lưu vị trí bắt đầu
    setDragStartPosition({ x: event.clientX, y: event.clientY });

    // Đặt timeout để phân biệt giữa click và drag
    const timeout = setTimeout(() => {
      // Nếu người dùng giữ chuột quá 200ms, coi như có ý định kéo
      setIsDragIntent(true);
    }, 10);

    setDragTimeout(timeout);
  };

  // Xử lý khi di chuyển chuột
  const handleMouseMove = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (!dragStartPosition) return;

    // Tính khoảng cách di chuyển
    const deltaX = Math.abs(event.clientX - dragStartPosition.x);
    const deltaY = Math.abs(event.clientY - dragStartPosition.y);

    // Nếu di chuyển quá 5px, coi như có ý định kéo
    if (deltaX > 5 || deltaY > 5) {
      setIsDragIntent(true);

      // Xóa timeout nếu có
      if (dragTimeout) {
        clearTimeout(dragTimeout);
        setDragTimeout(null);
      }
    }
  };

  // Xử lý khi thả chuột
  const handleMouseUp = (event: React.MouseEvent) => {
    event.stopPropagation();

    // Xóa timeout nếu có
    if (dragTimeout) {
      clearTimeout(dragTimeout);
      setDragTimeout(null);
    }

    // Nếu không có ý định kéo và không di chuyển nhiều, coi như click
    if (!isDragIntent && dragStartPosition) {
      const deltaX = Math.abs(event.clientX - dragStartPosition.x);
      const deltaY = Math.abs(event.clientY - dragStartPosition.y);

      if (deltaX < 5 && deltaY < 5) {
        setIsOpen(true);
      }
    }

    // Reset trạng thái
    setDragStartPosition(null);
    setIsDragIntent(false);
  };

  const dataTypeOptions = useMemo(() => {
    return [
      {
        title: intl.formatMessage(messageContentType.realtime),
        value: dataTypeObject.realtime,
      },
      {
        title: intl.formatMessage(messageContentType.histories),
        value: dataTypeObject.histories,
      },
      {
        title: intl.formatMessage(messageContentType.images),
        value: dataTypeObject.images,
      },
      {
        title: 'Video',
        value: dataTypeObject.video,
      },
      {
        title: intl.formatMessage(messageContentType.text),
        value: dataTypeObject.text,
      },
    ];
  }, [locale]);

  return (
    <div
      ref={setNodeRef}
      style={style}
      data-active={name === focusedIndex}
      className={cn(
        'relative min-h-[62px] group flex flex-row p-3 gap-2 rounded-lg cursor-pointer items-center bg-white overflow-hidden select-none border-[2px] border-transparent transition-[border-color,box-shadow] duration-200',
        {
          'bg-primary-500': name === focusedIndex,
          'hover:border-primary-200': name !== focusedIndex,
          'opacity-50 shadow-2xl': isDragging,
          'shadow-none': !isDragging,
        },
        props.className,
      )}
      onClick={(event) => {
        event.stopPropagation();
        if (name !== focusedIndex && !isDragging) {
          onChangeItem(name);
        }
      }}
    >
      {isHidden && (
        <div className="absolute top-0 left-0 h-full w-full flex items-center justify-center order-1 backdrop-blur-sm">
          <Tooltip>
            <TooltipTrigger asChild>
              <EyeSlashIcon
                size={20}
                weight="regular"
                className="text-current flex-shrink-0 group-data-[active=true]:text-white"
              />
            </TooltipTrigger>
            <TooltipContent
              className="p-2 text-xs font-normal text-gray-100 bg-gray-700 rounded-lg shadow max-w-[426px]"
              side="right"
              align="start"
            >
              <span className="font-normal text-xs leading-[150%] align-middle text-gray-300">
                <FormattedMessage
                  defaultMessage="Trang này sẽ không hiển thị khi ở chế độ trình chiếu."
                  id="screens.presentations.CardSlider.1907308715"
                />
              </span>
            </TooltipContent>
          </Tooltip>
        </div>
      )}
      <div className="flex flex-col gap-1 flex-1 min-w-0">
        <Form.Field name={[name, 'title', locale]}>
          {(control) => {
            return (
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="font-medium text-xs text-gray-700 group-data-[active=true]:text-white truncate w-fit max-w-full">
                    {control.value}
                  </span>
                </TooltipTrigger>
                <TooltipContent
                  className="p-2 text-xs font-normal max-w-[420px] text-gray-300 bg-gray-700 rounded-lg shadow"
                  side="right"
                  align="start"
                >
                  <span className="font-normal text-xs leading-[150%] align-middle text-gray-300">{control.value}</span>
                </TooltipContent>
              </Tooltip>
            );
          }}
        </Form.Field>
        <Form.Field name={[name, 'content', 'type']}>
          {(control) => {
            const content = dataTypeOptions.find(({ value }) => control.value === value)?.title || (
              <FormattedMessage
                defaultMessage="Chưa cấu hình nội dung"
                id="screens.presentations.CardSlider.1788853769"
              />
            );
            return (
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="font-medium text-xs text-gray-500 group-data-[active=true]:text-primary-200 truncate w-fit max-w-full">
                    {content}
                  </span>
                </TooltipTrigger>
                <TooltipContent
                  className="p-2 font-normal text-xs max-w-[420px] text-gray-300 bg-gray-700 rounded-lg shadow"
                  side="right"
                  align="start"
                >
                  <span className="font-normal text-xs leading-[150%] align-middle text-gray-300">
                    {dataTypeOptions.find(({ value }) => control.value === value)?.title || (
                      <FormattedMessage
                        defaultMessage="Chưa cấu hình nội dung"
                        id="screens.presentations.CardSlider.1788853769"
                      />
                    )}
                  </span>
                </TooltipContent>
              </Tooltip>
            );
          }}
        </Form.Field>
      </div>
      <div className="invisible w-6 h-full flex-shrink-0"></div>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div
            {...attributes}
            {...listeners}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onClick={(event) => {
              event.stopPropagation();
            }}
            className={cn(
              'absolute right-3 flex-shrink-0 hover:bg-gray-100 group-data-[active=true]:hover:bg-primary-400 rounded-[4px] py-[6px] order-2 cursor-grab active:cursor-grabbing',
              {
                'opacity-50 scale-105 shadow-lg z-50': isDragging,
              },
            )}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <DotsSixVerticalIcon
                  size={24}
                  weight="regular"
                  className={cn('transition-colors duration-200', {
                    'text-gray-500': name !== focusedIndex,
                    'text-white': name === focusedIndex,
                  })}
                />
              </TooltipTrigger>
              <TooltipContent
                className="px-3 py-2 text-sm font-normal text-gray-100 bg-gray-700 rounded-lg shadow max-w-[426px]"
                side="right"
                align="start"
              >
                <span className="font-normal text-xs leading-[150%] align-middle text-gray-300">
                  <FormattedMessage
                    defaultMessage="<span>Nhấn</span> để xem tuỳ chọn"
                    id="screens.presentations.CardSlider.1528339624"
                    values={{
                      span: (chunks: React.ReactNode) => (
                        <span className="font-semibold text-xs leading-[150%] align-middle">{chunks}</span>
                      ),
                    }}
                  />
                  <br />
                  <FormattedMessage
                    defaultMessage="<span>Kéo</span> để sắp xếp thứ tự"
                    id="screens.presentations.CardSlider.1548813057"
                    values={{
                      span: (chunks: React.ReactNode) => (
                        <span className="font-semibold text-xs leading-[150%] align-middle">{chunks}</span>
                      ),
                    }}
                  />
                </span>
              </TooltipContent>
            </Tooltip>
          </div>
        </PopoverTrigger>
        <PopoverContent
          side="right"
          align="start"
          className="bg-white min-w-[180px] w-auto flex flex-col rounded-xl p-2"
        >
          <div
            className="flex flex-row gap-2 p-2 items-center cursor-pointer rounded-lg hover:bg-gray-100 font-normal text-sm text-gray-700"
            onClick={(event) => {
              event.stopPropagation();
              onDuplicate();
              setIsOpen(false);
            }}
          >
            <CopyIcon size={20} weight="regular" className="text-current flex-shrink-0" />
            <span className="text-current font-normal text-sm leading-[150%]">
              <FormattedMessage defaultMessage="Nhân bản" id="screens.presentations.CardSlider.1410546157" />
            </span>
          </div>
          <div
            className="flex flex-row gap-2 p-2 items-center cursor-pointer rounded-lg hover:bg-gray-100 font-normal text-sm text-gray-700"
            onClick={(event) => {
              event.stopPropagation();
              onUpdateToggle();
              setIsOpen(false);
            }}
          >
            {!isHidden ? (
              <EyeIcon size={20} weight="regular" className="text-current flex-shrink-0" />
            ) : (
              <EyeSlashIcon size={20} weight="regular" className="text-current flex-shrink-0" />
            )}
            <span className="text-current font-normal text-sm leading-[150%]">
              {!isHidden ? (
                <FormattedMessage defaultMessage="Ẩn trang" id="screens.presentations.CardSlider.2027417406" />
              ) : (
                <FormattedMessage defaultMessage="Hiển thị trang" id="screens.presentations.CardSlider.410358751" />
              )}
            </span>
          </div>
          {isDeleted && (
            <div
              className="flex flex-row gap-2 p-2 items-center cursor-pointer rounded-lg hover:bg-gray-100 font-normal text-sm text-red-500"
              onClick={(event) => {
                event.stopPropagation();
                setIsOpenModalDel(true);
              }}
            >
              <TrashIcon size={20} weight="regular" className="text-current flex-shrink-0" />
              <span className="text-current font-normal text-sm leading-[150%]">
                <FormattedMessage defaultMessage="Xóa" id="screens.presentations.CardSlider.92198" />
              </span>
            </div>
          )}
        </PopoverContent>
      </Popover>

      <Dialog open={isOpenModalDel} onOpenChange={setIsOpenModalDel}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[420px] h-auto rounded-xl sm:rounded-xl">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <div className="flex flex-col">
              <div className="flex flex-col justify-center items-center p-5">
                <Image
                  src={`/images/commons/delete-image.svg`}
                  alt="deleted-image"
                  width={200}
                  height={200}
                  className="h-50 w-50 object-contain flex-shrink-0 aspect-square"
                  priority
                />
                <div className="flex-1 min-h-0 flex flex-col gap-1 items-center justify-center">
                  <span className="text-center font-semibold text-lg leading-[27px] text-gray-800">
                    <FormattedMessage
                      defaultMessage="Xác nhận xóa trang trình chiếu"
                      id="screens.presentations.CardSlider.448583877"
                    />
                  </span>
                  <span className="text-center font-normal text-sm leading-[21px] text-gray-700">
                    <FormattedMessage
                      defaultMessage="Bạn chắc chắn muốn xóa trang trình chiếu này? Hành động này sẽ xóa vĩnh viễn trang trình chiếu này và không thể khôi phục. Vui lòng xác nhận nếu bạn muốn tiếp tục."
                      id="screens.presentations.CardSlider.140579658"
                    />
                  </span>
                </div>
              </div>
              <div className="flex flex-row justify-end gap-3 p-[14px] border-t border-gray-200">
                <Button
                  type="button"
                  variant="gray"
                  className="flex-1 text-primary-500 text-sm font-normal"
                  onClick={() => {
                    setIsOpenModalDel(false);
                    setIsOpen(false);
                  }}
                >
                  <FormattedMessage defaultMessage="Hủy bỏ" id="components.ui.UploadFile.780985299" />
                </Button>
                <Button
                  type="button"
                  variant="danger"
                  className="flex-1 text-sm font-normal"
                  onClick={() => {
                    onRemove?.(name);
                    setIsOpen(false);
                  }}
                >
                  <FormattedMessage defaultMessage="Xác nhận" id="components.ui.UploadFile.559188735" />
                </Button>
              </div>
            </div>
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </div>
  );
}

export default CardSlider;
