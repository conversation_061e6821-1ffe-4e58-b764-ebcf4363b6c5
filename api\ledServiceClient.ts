import envConfig from '@/constants/env';
import { IInitData, ILedConfigData, ISlideConfigData, IStationType } from '@/types';
import queryString from 'query-string';
import apiPathName from './apiPathName';
import { BaseClient } from './baseClient';
import { BodyConfigSlide, DataHistory, DataResponse } from './type';

export class LedServiceClient extends BaseClient<Record<string, any>> {
  moduleName = 'LedServiceClient';
  constructor() {
    super(envConfig.LED_SERVICE_ENDPOINT + apiPathName.ledService.root);
  }

  loginLedBoard(
    key: string,
    password: string,
  ): Promise<
    DataResponse<{
      login: boolean;
    }>
  > {
    return this.api.post(apiPathName.ledService.login, { key, password });
  }

  getLedConfig(key?: string): Promise<DataResponse<ILedConfigData>> {
    return this.api.get(`${apiPathName.ledService.getLedConfig}/${key}`);
  }

  toggleShareLedBoard(key: string): Promise<DataResponse<ILedConfigData>> {
    return this.api.post(`${apiPathName.ledService.toggleShareLedBoard}/${key}`);
  }

  configSlide(key: string, body: BodyConfigSlide): Promise<DataResponse<ISlideConfigData>> {
    return this.api.post(`${apiPathName.ledService.configSlide}/${key}`, body);
  }

  configDefaultData(key: string, body: IInitData) {
    // Update config with multipart/form-data
    this.updateConfig({
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return this.api.post(`${apiPathName.ledService.defaultConfig}/${key}`, body);
  }

  getStationTypes(): Promise<DataResponse<IStationType[]>> {
    return this.api.get(apiPathName.ledService.stationTypes);
  }

  configurePresentations(key: string, body: FormData) {
    this.updateConfig({
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return this.api.post(`${apiPathName.ledService.configurePresentations}/${key}`, body);
  }

  getDataHistoriesForStation(body: {
    station: string;
    timeRange: string;
    typeTimeRange: string;
    measure: string[];
    calc: string;
  }): Promise<DataResponse<DataHistory[]>> {
    this.updateConfig({
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return this.api.get(`${apiPathName.ledService.dataHistoriesForStation}`, {
      params: body,
      paramsSerializer: (params) => {
        return queryString.stringify(params);
      },
    });
  }
}
