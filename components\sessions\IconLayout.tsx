'use client';

import { cn } from '@/utils/tailwind';
import {
  XIcon,
  CaretRightIcon,
  MagnifyingGlassIcon,
  WarningIcon,
  LockKeyOpenIcon,
  DotsThreeVerticalIcon,
  EyeIcon,
  EyeClosedIcon,
  PlusIcon,
  BuildingOfficeIcon,
  UserIcon,
  UsersFourIcon,
  AppStoreLogoIcon,
  GearIcon,
  ChartBarIcon,
  LockIcon,
  WrenchIcon,
  MagicWandIcon,
  ShareNetworkIcon,
  SlideshowIcon,
  ReceiptIcon,
  DeviceMobileSpeakerIcon,
  SquaresFourIcon,
  MonitorIcon,
  DeviceTabletSpeakerIcon,
} from '@phosphor-icons/react';
import React from 'react';

type WeightBold = 'bold';
type WeightDuotone = 'duotone';
type WeightFill = 'fill';
type WeightLight = 'light';
type WeightRegular = 'regular';
type WeightThin = 'thin';

export type Weight = WeightBold | WeightDuotone | WeightFill | WeightLight | WeightRegular | WeightThin;

interface IconProps {
  name: string;
  size?: number;
  color?: string;
  className?: string;
  weight?: Weight;
  onClick?: () => void;
}

const Icons = {
  XIcon: XIcon,
  CaretRightIcon: CaretRightIcon,
  MagnifyingGlassIcon: MagnifyingGlassIcon,
  WarningIcon: WarningIcon,
  LockKeyOpenIcon: LockKeyOpenIcon,
  DotsThreeVerticalIcon: DotsThreeVerticalIcon,
  LockIcon: LockIcon,
  EyeIcon: EyeIcon,
  EyeClosedIcon: EyeClosedIcon,
  PlusIcon: PlusIcon,
  BuildingOfficeIcon: BuildingOfficeIcon,
  UserIcon: UserIcon,
  UsersFourIcon: UsersFourIcon,
  AppStoreLogoIcon: AppStoreLogoIcon,
  GearIcon: GearIcon,
  ChartBarIcon: ChartBarIcon,
  WrenchIcon: WrenchIcon,
  MagicWandIcon: MagicWandIcon,
  ShareNetworkIcon: ShareNetworkIcon,
  SlideshowIcon: SlideshowIcon,
  ReceiptIcon: ReceiptIcon,
  DeviceMobileSpeakerIcon: DeviceMobileSpeakerIcon,
  SquaresFourIcon: SquaresFourIcon,
  MonitorIcon: MonitorIcon,
  DeviceTabletSpeakerIcon: DeviceTabletSpeakerIcon,
};

const IconCommon: React.FC<IconProps> = ({
  name,
  size = 24,
  className = '',
  weight = 'regular',
  onClick,
  ...props
}) => {
  const IconComponent = (Icons as any)[name];

  if (!IconComponent) {
    return <span>Icon not found</span>;
  }

  return (
    <IconComponent
      size={size}
      className={cn('block flex-shrink-0 text-current', className)}
      weight={weight}
      onClick={onClick}
      {...props}
    />
  );
};

export default IconCommon;
