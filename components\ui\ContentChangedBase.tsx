import { cn } from '@/utils/tailwind';
import React from 'react';
import { FormattedMessage } from 'react-intl';
import { Button } from 'ui-components';

type Props = {
  onCancel?: () => void;
  onSubmit: () => void;
  heading: string | React.ReactNode;
  description: string | React.ReactNode;
  hiddenButton?: boolean;
};
export default function ContentChangedBase({ onCancel, onSubmit, heading, description, hiddenButton = false }: Props) {
  return (
    <div className="flex flex-col">
      <div className="flex flex-col gap-2 p-5 items-center justify-center">
        <span className="font-semibold text-lg text-center text-gray-800">{heading}</span>
        <span className="font-normal text-sm text-center text-gray-700">{description}</span>
      </div>
      <div className="flex flex-row p-[14px] border-t border-gray-200 gap-3">
        <Button variant="gray" className={cn('flex-1 text-primary-500', { hidden: hiddenButton })} onClick={onCancel}>
          <FormattedMessage defaultMessage="Hủy bỏ" id="components.ui.ContentChangedBase.780985299" />
        </Button>
        <Button className="flex-1" onClick={onSubmit}>
          <FormattedMessage defaultMessage="Xác nhận" id="components.ui.ContentChangedBase.559188735" />
        </Button>
      </div>
    </div>
  );
}
