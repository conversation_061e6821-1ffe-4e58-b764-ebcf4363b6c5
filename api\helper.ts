function argToString(arg: any[]): string {
  function itemToString(item: any): string {
    if (Array.isArray(item)) {
      return item.map<any>((k: any) => itemToString(k)).join('-');
    }
    if (item && typeof item === 'object') {
      return Object.keys(item)
        .sort()
        .map<any>((k: any) => `${k}=${itemToString(item[k])}`)
        .join('-');
    }
    if (item === null || item === undefined) return 'null';
    return item.toString();
  }
  return arg.map((item) => itemToString(item)).join('|');
}

function props(obj: object): string[] {
  let p: string[] = [];
  for (; obj != null; obj = Object.getPrototypeOf(obj)) {
    let op = Object.getOwnPropertyNames(obj);
    for (let i = 0; i < op.length; i++) {
      if (p.indexOf(op[i]) == -1) {
        p.push(op[i]);
      }
    }
  }
  return p;
}

const getParamsOnly = (fTmp: string) => {
  fTmp = ~fTmp.indexOf(`(`) ? fTmp.slice(fTmp.indexOf(`(`)) : fTmp;
  fTmp = (fTmp.match(/(^[a-z_](?=(=>|=>{)))|((^\([^)].+\)|\(\))(?=(=>|{)))/i) || [fTmp])[0];
  return !fTmp.startsWith(`(`) ? `(${fTmp})` : fTmp;
};

// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
function getNumArgs(func: Function) {
  const str = func.toString();
  const fTmp = getParamsOnly(str.replace(/\s+/g, ``));

  // no parameters, no need for further parsing
  if (fTmp === `()`) {
    const matches = [...str.matchAll(/arguments\[(\d+)\]/g)];
    if (matches.length === 0) {
      return 0;
    } else {
      return Math.max(...matches.map((m) => parseInt(m[1]))) + 1;
    }
    // console.log(matches)
    // const maxIndex =
    // return 0;
  }

  let [paramStr, commaCount, bracketCount, lastParen, i, inStrSingle, inStrDouble, bOpen, bClose] = [
    fTmp,
    0,
    0,
    0,
    0,
    false,
    false,
    [...`([{`],
    [...`)]}`],
  ];

  for (; i < paramStr.length; i += 1) {
    if (bOpen.includes(paramStr[i]) && !inStrSingle && !inStrDouble) {
      bracketCount += 1;
      lastParen = i;
    }
    if (bClose.includes(paramStr[i]) && !inStrSingle && !inStrDouble) {
      bracketCount -= 1;
      if (bracketCount < 1) {
        break;
      }
    }
    if (paramStr[i] === "'" && !inStrDouble && paramStr[i - 1] !== '\\') {
      inStrSingle = !inStrSingle;
    }
    if (paramStr[i] === '"' && !inStrSingle && paramStr[i - 1] !== '\\') {
      inStrDouble = !inStrDouble;
    }
    if (paramStr[i] === ',' && bracketCount === 1 && !inStrSingle && !inStrDouble) {
      commaCount += 1;
    }
  }

  const matches = [...str.matchAll(/arguments\[(\d+)\]/g)];
  let argumentCount = 0;
  if (matches.length > 0) {
    argumentCount = Math.max(...matches.map((m) => parseInt(m[1]))) + 1;
  }

  return Math.max(
    commaCount === 0 && paramStr.substring(lastParen + 1, i).trim().length === 0 ? 0 : commaCount + 1,
    argumentCount,
  );
}

export { argToString, getNumArgs, props };

