import { usePathname, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export function useNavigationLoading() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isNavigating, setIsNavigating] = useState(false);

  // Reset loading state when route changes
  useEffect(() => {
    if (isNavigating) {
      // Add small delay to ensure UI updates are complete
      const timeoutId = setTimeout(() => {
        setIsNavigating(false);
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [pathname, searchParams, isNavigating]);

  return {
    isNavigating,
    startNavigation: () => setIsNavigating(true),
    stopNavigation: () => setIsNavigating(false),
  };
}
