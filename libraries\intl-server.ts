'server-only';

import { createIntl, IntlShape } from '@formatjs/intl';
import { MessageFormatElement } from 'react-intl';

type Messages = Record<string, string> | Record<string, MessageFormatElement[]>;
type NestedMessages = {
  [key: string]: string | MessageFormatElement[] | NestedMessages;
};

const getMessages = async (locale: string): Promise<Messages> => {
  return (await import(`@/locales/${locale}.json`)).default;
};

function flattenMessages(nestedMessages: NestedMessages, prefix: string = ''): Messages {
  return Object.keys(nestedMessages).reduce((messages: Messages, key: string) => {
    const value = nestedMessages[key];
    const prefixedKey = prefix ? `${prefix}.${key}` : key;

    if (typeof value === 'string' || Array.isArray(value)) {
      messages[prefixedKey] = value;
    } else {
      Object.assign(messages, flattenMessages(value, prefixedKey));
    }

    return messages;
  }, {});
}

export default async function getIntl(locale: string): Promise<IntlShape> {
  const messageFileJson: NestedMessages = await getMessages(locale);
  const messages: Messages = flattenMessages(messageFileJson);
  return createIntl({
    locale,
    messages,
  });
}
