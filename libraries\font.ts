import { Be_Vietnam_Pro, Inter, JetBrains_Mono, Montserrat, Tektur } from 'next/font/google';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  weight: ['400', '500', '600', '700', '800', '900'],
});
const montserrat = Montserrat({
  subsets: ['latin'],
  variable: '--font-montserrat',
  weight: ['400', '500', '600', '700', '800', '900'],
});

const tektur = Tektur({
  subsets: ['latin'],
  variable: '--font-tektur',
  weight: ['400', '500', '600', '700', '800', '900'],
});

const jetBrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-jetbrains-mono',
  weight: ['400', '500', '600', '700', '800'],
});

const beVietnamPro = Be_Vietnam_Pro({
  subsets: ['latin'],
  variable: '--font-be-vietnam-pro',
  weight: ['400', '500', '600', '700', '800', '900'],
});

export { beVietnamPro, inter, jetBrainsMono, montserrat, tektur };

