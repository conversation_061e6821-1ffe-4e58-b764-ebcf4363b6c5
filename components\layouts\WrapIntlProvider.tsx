'use client';

import { LANGUEGES_VI } from '@/constants';
import { ReactNode } from 'react';
import { IntlProvider, MessageFormatElement } from 'react-intl';

export default function WrapIntlProvider({
  messages,
  locale = LANGUEGES_VI,
  children,
}: {
  messages: Record<string, MessageFormatElement[]> | Record<string, string>;
  locale: string;
  children: ReactNode;
}) {
  return (
    <IntlProvider
      messages={messages}
      locale={locale ?? LANGUEGES_VI}
      defaultLocale={LANGUEGES_VI}
      onError={console.log}
    >
      {children}
    </IntlProvider>
  );
}
