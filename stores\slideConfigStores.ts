import { create } from 'zustand';

type SlideConfigState = {
  config: {
    height : number;
    width: number;
  };
};

type SlideConfigAction = {
  setConfig: (config: SlideConfigState['config']) => void;
};

const useSlideConfigStores = create<SlideConfigState & SlideConfigAction>((set) => ({
  config: {
    height: 1080,
    width: 1920,
  },
  setConfig: (config: { height: number; width: number }) => set(() => ({ config })),
}));

export { useSlideConfigStores };

