import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Button } from 'ui-components';
import { CaretLeftIcon, PlayIcon, CaretRightIcon, XIcon, PauseIcon } from '@phosphor-icons/react';
import { cn } from '@/utils/tailwind';
import { ContentType } from '@/types/slide';
import { messageContentType } from '@/constants/defineMessages';
import { dataTypeObject } from '@/constants';
import { FormattedMessage, useIntl } from 'react-intl';
import { ISlideWithSubSlides } from '@/stores/presentationStores';
import { ResponsiveTextWithOverride } from '@/screens/presentations/components/ResponsiveTextWithOverride';
import { useLedConfigStores } from '@/stores';

interface PresentationControlBarProps {
  currentSlide: number;
  currentSubSlide?: number;
  totalSlides: number;
  slideTitle?: string;
  isPlaying: boolean;
  onPlayPause: () => void;
  onPrevious: () => void;
  onNext: () => void;
  onClose: () => void;
  prevDisabled?: boolean;
  nextDisabled?: boolean;
  className?: string;
  slideType?: ContentType;
  dataSlide: ISlideWithSubSlides | null;
}

const PresentationControlBar: React.FC<PresentationControlBarProps> = ({
  currentSlide,
  totalSlides,
  currentSubSlide,
  slideTitle = '',
  isPlaying,
  onPlayPause,
  onPrevious,
  onNext,
  onClose,
  prevDisabled = false,
  nextDisabled = false,
  className,
  slideType,
  dataSlide,
}) => {
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );
  const [isVisible, setIsVisible] = useState(false);
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';

  const handleMouseMove = useCallback((e: MouseEvent) => {
    // Clear any existing hide timeout
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = null;
    }

    if (e.clientY < 180) {
      setIsVisible(true);
    } else {
      hideTimeoutRef.current = setTimeout(() => {
        setIsVisible(false);
      }, 1000);
    }
  }, []);

  const handleMouseLeave = useCallback(() => {
    // Hide with delay when mouse leaves the document
    hideTimeoutRef.current = setTimeout(() => {
      setIsVisible(false);
    }, 5000);
  }, []);

  useEffect(() => {
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseleave', handleMouseLeave);

      // Clear timeout on cleanup
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, [handleMouseMove, handleMouseLeave]);

  const dataTypeOptions = useMemo(() => {
    return [
      {
        title: intl.formatMessage(messageContentType.realtime),
        value: dataTypeObject.realtime,
      },
      {
        title: intl.formatMessage(messageContentType.histories),
        value: dataTypeObject.histories,
      },
      {
        title: intl.formatMessage(messageContentType.images),
        value: dataTypeObject.images,
      },
      {
        title: 'Video',
        value: dataTypeObject.video,
      },
      {
        title: intl.formatMessage(messageContentType.text),
        value: dataTypeObject.text,
      },
    ];
  }, [locale]);

  const currentType = dataTypeOptions.find((type) => type.value === slideType);

  return (
    <div
      className={cn(
        'fixed top-0 left-0 right-0 z-50 w-full transition-all duration-300 ease-in-out',
        isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0',
        className,
      )}
    >
      <div
        className="w-full backdrop-blur-[8px] px-4 py-[14px]"
        style={{
          background: 'linear-gradient(180deg, #000000 0%, rgba(0, 0, 0, 0.6) 60%, rgba(0, 0, 0, 0) 100%)',
        }}
      >
        <div className="flex flex-row items-center justify-between w-full gap-5">
          <div className="flex-1 min-w-0 flex flex-row gap-4 items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              icon
              className="text-white h-4 w-4 max-w-4 flex-shrink-0"
              type="button"
            >
              <XIcon size={24} className="flex-shrink-0" />
            </Button>
            <div className="flex-1 min-w-0 flex flex-col text-white">
              <div className="flex flex-row gap-1 items-center">
                <ResponsiveTextWithOverride
                  className="font-medium flex-shrink-0"
                  value={dataSlide?.header.fontSize.config.title}
                >
                  Trang {dataSlide?.subSlides?.length! > 1 ? `${currentSlide}.${currentSubSlide}` : currentSlide}/
                  {totalSlides}:
                </ResponsiveTextWithOverride>
                {slideTitle && (
                  <span className="flex-1 min-w-0 flex items-center">
                    <ResponsiveTextWithOverride
                      value={dataSlide?.header.fontSize.config.title}
                      className="font-medium block truncate"
                    >
                      {slideTitle}
                    </ResponsiveTextWithOverride>
                  </span>
                )}
              </div>
              <ResponsiveTextWithOverride value={dataSlide?.header.fontSize.config.description} className="font-normal">
                {currentType ? (
                  currentType.title
                ) : (
                  <FormattedMessage
                    defaultMessage="Chưa cấu hình nội dung"
                    id="screens.presentations.CardSlider.1788853769"
                  />
                )}
              </ResponsiveTextWithOverride>
            </div>
          </div>

          <div
            className="flex-shrink-0 flex items-center gap-[1px] rounded-lg border-[1px] overflow-hidden"
            style={{
              borderColor: defaultConfig.slideConfig?.borderColor,
              backgroundColor: defaultConfig.slideConfig?.borderColor,
            }}
          >
            <div
              style={{
                color: defaultConfig.slideConfig?.color,
                backgroundColor: defaultConfig.slideConfig?.background,
              }}
            >
              <Button
                variant="ghost"
                size="lg"
                icon
                onClick={onPrevious}
                // disabled={prevDisabled}
                className="p-2 rounded-none bg-transparent hover:bg-transparent text-center"
                type="button"
              >
                <CaretLeftIcon size={24} className="flex-shrink-0" />
              </Button>
            </div>
            <div
              style={{
                color: defaultConfig.slideConfig?.color,
                backgroundColor: defaultConfig.slideConfig?.background,
              }}
            >
              <Button
                variant="ghost"
                size="lg"
                icon
                onClick={onPlayPause}
                className="p-2 rounded-none bg-transparent hover:bg-transparent text-center"
                type="button"
              >
                {isPlaying ? (
                  <PauseIcon size={24} className="flex-shrink-0" />
                ) : (
                  <PlayIcon size={24} className="flex-shrink-0" />
                )}
              </Button>
            </div>
            <div
              style={{
                color: defaultConfig.slideConfig?.color,
                backgroundColor: defaultConfig.slideConfig?.background,
              }}
            >
              <Button
                variant="ghost"
                size="lg"
                icon
                onClick={onNext}
                // disabled={nextDisabled}
                className="p-2 rounded-none bg-transparent hover:bg-transparent text-center"
                type="button"
              >
                <CaretRightIcon size={24} className="flex-shrink-0" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PresentationControlBar;
