export const priorityGrid = {
  sm: {
    stationName: 4,
    measureCode: 3,
    limitThreshold: 3,
    measureValue: 0,
    measureUnit: 3,
    legendWarning: 5,
  },
  md: {
    stationName: 4,
    measureCode: 4,
    limitThreshold: 5,
    measureValue: 1,
    measureUnit: 4,
    legendWarning: 5,
  },
  lg: {
    stationName: 4,
    measureCode: 4,
    limitThreshold: 5,
    measureValue: 2,
    measureUnit: 5,
    legendWarning: 5,
  },
};

export const priorityTable = {
  sm: {
    headerTable: 4,
    stationName: 4,
    measureCode: 4,
    limitThreshold: 4,
    measureValue: 4,
    measureUnit: 4,
    legendWarning: 5,
  },
  md: {
    headerTable: 5,
    stationName: 4,
    measureCode: 5,
    limitThreshold: 5,
    measureValue: 5,
    measureUnit: 5,
    legendWarning: 5,
  },
  lg: {
    headerTable: 5,
    stationName: 4,
    measureCode: 5,
    limitThreshold: 5,
    measureValue: 5,
    measureUnit: 5,
    legendWarning: 5,
  },
};

export const priorityText = {
  content: 4,
  title: 4,
  description: 5,
  datetime: 5,
  subDescription: 6,
};

// Helper function to determine size based on grid configuration
export const getGridSize = (column: number, row: number): 'sm' | 'md' | 'lg' => {
  const totalCells = column * row;
  // Grid: column (2-4) x row (2-4) = totalCells (4-16)
  if (totalCells <= 4) return 'sm'; // 2x2
  if (totalCells <= 9) return 'md'; // 2x3, 3x2, 3x3
  return 'lg'; // 3x4, 4x2, 4x3, 4x4
};

// Helper function to determine size for table based on row configuration
export const getTableSize = (column: number, row: number): 'sm' | 'md' | 'lg' => {
  // Table cố định 4 columns: chỉ xét row (4-10)
  if (column === 4) {
    if (row <= 5) return 'sm'; // 4-5 rows
    if (row <= 7) return 'md'; // 6-7 rows
    return 'lg'; // 8-10 rows
  }

  // Table linh hoạt: columns (4-8), row (4-10)
  if (column <= 5 && row <= 6) return 'sm'; // 4-5 columns, 4-6 rows
  if (column <= 6 && row <= 8) return 'md'; // 4-6 columns, 4-8 rows
  return 'lg'; // 4-8 columns, 4-10 rows
};
