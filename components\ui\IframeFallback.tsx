/**
 * Fallback rendering components for iframe failures
 */

'use client';

import React from 'react';

export interface IframeFallbackProps {
  error?: Error | null;
  retryCount?: number;
  maxRetries?: number;
  onRetry?: () => void;
  onReset?: () => void;
  children?: React.ReactNode;
  className?: string;
}

/**
 * Basic error fallback component
 */
export function IframeErrorFallback({
  error,
  retryCount = 0,
  maxRetries = 3,
  onRetry,
  onReset,
  className = '',
}: IframeFallbackProps) {
  const canRetry = retryCount < maxRetries && onRetry;

  return (
    <div
      className={`flex items-center justify-center h-full w-full bg-red-50 border-2 border-dashed border-red-300 ${className}`}
    >
      <div className="text-center p-6 max-w-md">
        <div className="text-red-500 text-4xl mb-4">⚠️</div>
        <h3 className="text-lg font-semibold text-red-800 mb-2">Preview Error</h3>
        <p className="text-sm text-red-600 mb-4">{error?.message || 'Failed to load preview content'}</p>

        {retryCount > 0 && (
          <p className="text-xs text-red-500 mb-4">
            Attempt {retryCount + 1} of {maxRetries + 1}
          </p>
        )}

        <div className="flex gap-2 justify-center">
          {canRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors"
            >
              Retry
            </button>
          )}

          {onReset && (
            <button
              onClick={onReset}
              className="px-4 py-2 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
            >
              Reset
            </button>
          )}
        </div>

        {!canRetry && !onReset && <p className="text-xs text-gray-500 mt-4">Please refresh the page to try again</p>}
      </div>
    </div>
  );
}

/**
 * Loading state fallback component
 */
export function IframeLoadingFallback({
  retryCount = 0,
  maxRetries = 3,
  className = '',
}: Pick<IframeFallbackProps, 'retryCount' | 'maxRetries' | 'className'>) {
  return (
    <div className={`flex items-center justify-center h-full w-full bg-gray-50 ${className}`}>
      <div className="text-center p-6">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <h3 className="text-lg font-medium text-gray-700 mb-2">Loading Preview</h3>
        <p className="text-sm text-gray-500">
          {retryCount > 0 ? `Attempt ${retryCount + 1} of ${maxRetries + 1}...` : 'Preparing your slide preview...'}
        </p>

        <div className="mt-4">
          <div className="w-48 bg-gray-200 rounded-full h-2 mx-auto">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${Math.min(100, (retryCount + 1) * 25)}%` }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Timeout fallback component
 */
export function IframeTimeoutFallback({
  operation = 'loading',
  timeout = 10000,
  onRetry,
  onReset,
  className = '',
}: {
  operation?: string;
  timeout?: number;
  onRetry?: () => void;
  onReset?: () => void;
  className?: string;
}) {
  return (
    <div
      className={`flex items-center justify-center h-full w-full bg-yellow-50 border-2 border-dashed border-yellow-300 ${className}`}
    >
      <div className="text-center p-6 max-w-md">
        <div className="text-yellow-500 text-4xl mb-4">⏱️</div>
        <h3 className="text-lg font-semibold text-yellow-800 mb-2">Loading Timeout</h3>
        <p className="text-sm text-yellow-600 mb-4">
          The preview is taking longer than expected to load ({timeout / 1000}s timeout).
        </p>

        <div className="flex gap-2 justify-center">
          {onRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 transition-colors"
            >
              Try Again
            </button>
          )}

          {onReset && (
            <button
              onClick={onReset}
              className="px-4 py-2 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
            >
              Reset
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * CSS injection failure fallback
 */
export function CSSInjectionFallback({
  onRetry,
  onContinue,
  className = '',
}: {
  onRetry?: () => void;
  onContinue?: () => void;
  className?: string;
}) {
  return (
    <div
      className={`flex items-center justify-center h-full w-full bg-blue-50 border-2 border-dashed border-blue-300 ${className}`}
    >
      <div className="text-center p-6 max-w-md">
        <div className="text-blue-500 text-4xl mb-4">🎨</div>
        <h3 className="text-lg font-semibold text-blue-800 mb-2">Styling Issue</h3>
        <p className="text-sm text-blue-600 mb-4">
          Some styles may not display correctly. The preview will work but might look different.
        </p>

        <div className="flex gap-2 justify-center">
          {onRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
            >
              Retry Styling
            </button>
          )}

          {onContinue && (
            <button
              onClick={onContinue}
              className="px-4 py-2 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
            >
              Continue Anyway
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Direct render fallback - renders content without iframe
 */
export function DirectRenderFallback({
  children,
  width,
  height,
  className = '',
}: {
  children: React.ReactNode;
  width: number;
  height: number;
  className?: string;
}) {
  return (
    <div className={`relative overflow-hidden bg-white border border-gray-300 ${className}`}>
      <div className="absolute top-2 left-2 z-10">
        <div className="bg-yellow-100 border border-yellow-300 rounded px-2 py-1 text-xs text-yellow-700">
          Fallback Mode
        </div>
      </div>

      <div
        className="w-full h-full"
        style={{
          width: `${width}px`,
          height: `${height}px`,
          transform: 'scale(1)',
          transformOrigin: 'top left',
        }}
      >
        {children}
      </div>
    </div>
  );
}

/**
 * Comprehensive fallback component that handles different error states
 */
export function ComprehensiveFallback({
  error,
  retryCount = 0,
  maxRetries = 3,
  isLoading = false,
  isTimeout = false,
  isCSSError = false,
  operation = 'loading',
  timeout = 10000,
  onRetry,
  onReset,
  onContinue,
  children,
  width,
  height,
  className = '',
}: IframeFallbackProps & {
  isLoading?: boolean;
  isTimeout?: boolean;
  isCSSError?: boolean;
  operation?: string;
  timeout?: number;
  onContinue?: () => void;
  width?: number;
  height?: number;
}) {
  // Loading state
  if (isLoading && !error) {
    return <IframeLoadingFallback retryCount={retryCount} maxRetries={maxRetries} className={className} />;
  }

  // Timeout state
  if (isTimeout) {
    return (
      <IframeTimeoutFallback
        operation={operation}
        timeout={timeout}
        onRetry={onRetry}
        onReset={onReset}
        className={className}
      />
    );
  }

  // CSS injection error
  if (isCSSError) {
    return <CSSInjectionFallback onRetry={onRetry} onContinue={onContinue} className={className} />;
  }

  // Direct render fallback if we have content and dimensions
  if (children && width && height && !error) {
    return (
      <DirectRenderFallback width={width} height={height} className={className}>
        {children}
      </DirectRenderFallback>
    );
  }

  // General error state
  return (
    <IframeErrorFallback
      error={error}
      retryCount={retryCount}
      maxRetries={maxRetries}
      onRetry={onRetry}
      onReset={onReset}
      className={className}
    />
  );
}
