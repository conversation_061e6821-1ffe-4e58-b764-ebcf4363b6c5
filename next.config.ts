import type { NextConfig } from 'next';

const imageDomain = process.env.NEXT_PUBLIC_IMAGE_DOMAIN ?? 'qa-camera.ilotusland.asia,qa-minio.ilotusland.asia';

const nextConfig: NextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  output: 'standalone',
  images: {
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    remotePatterns: imageDomain.split(',').map((domain) => {
      return {
        hostname: domain,
        protocol: 'https',
        port: '',
        pathname: '/**',
      };
    }),
  },
};

export default nextConfig;
