import { EStatus } from '@/types/slide';

type EntityKeys<E> = keyof E;

type AndOptions<K> = {
  $and: FilterOptions<K>[];
};

type OrOptions<K> = {
  $or: FilterOptions<K>[];
};

type Operator =
  | '$eq'
  | '$gt'
  | '$gte'
  | '$lt'
  | '$lte'
  | '$ne'
  | '$like'
  | '$re'
  | '$fulltext'
  | '$ilike'
  | '$overlap'
  | '$contains'
  | '$contained'
  | '$hasKey'
  | '$hasSomeKeys'
  | '$hasKeys';

type OperatorArray = '$in' | '$nin';

type ValueFilter<T> = FilterOptions<T> | Partial<{ [name in Operator]: T } & { [name in OperatorArray]: T[] }>;

type FilterOptions<E> = AndOptions<E> | OrOptions<E> | Partial<{ [K in EntityKeys<E>]: ValueFilter<E[K]> }>;

type PaginationOptions = {
  pageSize: number;
  pageIndex: number;
};

type ExtractClientMethods<TClient> = {
  [K in keyof TClient]: TClient[K] extends (...args: any[]) => Promise<any> ? TClient[K] : never;
};

export enum SortEnum {
  ASC = 'asc',
  DESC = 'desc',
}

export enum FilterStatusEnum {
  ACTIVATE = 'true',
  INACTIVATE = 'false',
}

type SortValue<E> = Partial<{ [K in EntityKeys<E>]: SortEnum }>;

type DataWithPagination<T> = {
  list: T[];
  pagination: PaginationData;
};

type PaginationData = {
  pageIndex: number;
  pageSize: number;
  totalPages: number;
  totalRecords: number;
  totalItems: number;
};

type DataResponse<T> = {
  data: T;
  message: string;
  timestamp?: string;
};

type BodyConfigSlide = {
  duration: number;
  font_family: string;
  background: string;
  color: string;
};

type DataHistory = {
  date: string;
  measuringLogs: {
    [key: string]: {
      value: number;
      status: EStatus;
    };
  };
};

export type {
  AndOptions,
  BodyConfigSlide,
  DataHistory,
  DataResponse,
  DataWithPagination,
  EntityKeys,
  ExtractClientMethods,
  FilterOptions,
  Operator,
  OperatorArray,
  OrOptions,
  PaginationOptions,
  SortValue,
  ValueFilter
};

