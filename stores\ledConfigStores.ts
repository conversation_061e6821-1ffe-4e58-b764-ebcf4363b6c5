import { ILedConfigData, IStationType } from '@/types';
import { create } from 'zustand';

type LedConfigStoreState = {
  isLoginedConfig: boolean;
  dataConfig: ILedConfigData | null;
  stationTypes: IStationType[];
};

type LedConfigStoreAction = {
  setIsLoginedConfig: (isLoginedConfig: LedConfigStoreState['isLoginedConfig']) => void;
  setDataConfig: (dataConfig: LedConfigStoreState['dataConfig']) => void;
  setStationTypes: (stationTypes: LedConfigStoreState['stationTypes']) => void;
};

const useLedConfigStores = create<LedConfigStoreState & LedConfigStoreAction>((set) => ({
  isLoginedConfig: false,
  setIsLoginedConfig: (isLoginedConfig: boolean) => set({ isLoginedConfig }),
  dataConfig: null,
  setDataConfig: (dataConfig: ILedConfigData | null) => set({ dataConfig }),

  stationTypes: [],
  setStationTypes: (stationTypes: IStationType[]) => set({ stationTypes }),
}));

export { useLedConfigStores };
