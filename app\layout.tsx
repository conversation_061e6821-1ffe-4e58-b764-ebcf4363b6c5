import type { Metadata } from 'next';
import { PublicEnvScript } from 'next-runtime-env';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import { cookies } from 'next/headers';
import { unstable_noStore as noStore } from 'next/cache';
import InitialLayout from '@/components/layouts/InitialLayout';
import WrapIntlProvider from '@/components/layouts/WrapIntlProvider';
import getIntl from '@/libraries/intl-server';
import WrapTooltip from '@/components/layouts/WrapTooltip';
import { CONFIG_LANGUAGE } from '@/constants';
import ToasterWrapper from '@/components/layouts/ToasterWrapper';
import { inter } from '@/libraries/font';
import '@/styles/main.css';

export const metadata: Metadata = {
  title: 'iLotusLand for Public',
  description: 'iLotusLand for Public',
  icons: {
    icon: '/images/branding/logo-ill-public.svg',
  },
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  noStore();
  const cookieStore = await cookies();
  const locale = cookieStore.get(CONFIG_LANGUAGE.cookieName)?.value || CONFIG_LANGUAGE.defaultLocale;
  const { messages } = await getIntl(locale);
  return (
    <html lang={locale} suppressHydrationWarning className={inter.className}>
      <head>
        <PublicEnvScript nextScriptProps={{ strategy: 'beforeInteractive' }} />
      </head>
      <body suppressHydrationWarning>
        <NuqsAdapter>
          <InitialLayout>
            <WrapIntlProvider locale={locale} messages={messages}>
              <WrapTooltip>{children}</WrapTooltip>
              <ToasterWrapper />
            </WrapIntlProvider>
          </InitialLayout>
        </NuqsAdapter>
      </body>
    </html>
  );
}
