import { dataTypeObject } from '@/constants';
import envConfig from '@/constants/env';
import { usePagination } from '@/hooks/usePagination';
import { SlideConfigDto, VideoContent } from '@/types/slide';
import { cn } from '@/utils/tailwind';
import { EmblaCarouselType } from 'embla-carousel';
import Fade from 'embla-carousel-fade';
import useEmblaCarousel from 'embla-carousel-react';
import { useEffect, useMemo, useRef } from 'react';
import { FormattedMessage } from 'react-intl';
import NotConfigure from '../components/NotConfigure';

type Props = {
  dataSlide: SlideConfigDto;
  isIframeReady: boolean;
  setEmblaApi: (emblaApi: EmblaCarouselType) => void;
  isTargeted: boolean; // New prop to indicate if this video is currently targeted
};
export default function ContentVideo({ dataSlide, isIframeReady, setEmblaApi, isTargeted }: Props) {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: false, watchDrag: false, containScroll: false }, [Fade()]);
  // Use pagination hook but we don't need selectedIndex for now
  usePagination(emblaApi);
  const videoContentSlide = useMemo(() => (dataSlide.content.metadata as VideoContent)?.video, [dataSlide]);
  const videoRef = useRef<HTMLVideoElement>(null);

  const pages = useMemo(() => {
    if (dataSlide.content.type === dataTypeObject.video && videoContentSlide) {
      if (videoContentSlide instanceof File) {
        const url = URL.createObjectURL(videoContentSlide);
        return [url];
      }

      if (typeof videoContentSlide === 'string') {
        return [videoContentSlide];
      }
    }
    return [];
  }, [videoContentSlide]);

  useEffect(() => {
    if (emblaApi && isIframeReady) {
      setEmblaApi(emblaApi);
    }
  }, [emblaApi, setEmblaApi, isIframeReady]);

  useEffect(() => {
    if (videoRef.current) {
      if (!isTargeted || !dataSlide.content.show) {
        videoRef.current.pause();
        videoRef.current.currentTime = 0;
      }
    }
  }, [isTargeted, dataSlide.content]);

  return (
    <div className="h-full w-full overflow-hidden" ref={emblaRef}>
      <div className={cn('embla__container flex h-full w-full')}>
        {pages.length > 0 ? (
          pages.map((page, index) => (
            <div key={index} className="embla__slide flex-[0_0_100%] min-w-0 h-full">
              <video
                ref={videoRef}
                src={page.startsWith('blob') ? page : `${envConfig.MEDIA_ENDPOINT}/${page}`}
                controls
                className="w-full h-full aspect-video bg-black"
                playsInline
                disablePictureInPicture
              />
            </div>
          ))
        ) : (
          <NotConfigure
            title={
              <FormattedMessage
                defaultMessage="Chưa có video hiển thị"
                id="screens.presentations.PreviewPresentation.859523826"
              />
            }
            description={
              <FormattedMessage
                defaultMessage="Vui lòng chọn video để hiển thị nội dung trên màn hình."
                id="screens.presentations.PreviewPresentation.165867170"
              />
            }
          />
        )}
      </div>
    </div>
  );
}
