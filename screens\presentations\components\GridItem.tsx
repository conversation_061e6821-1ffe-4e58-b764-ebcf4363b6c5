import { useStateThreshold } from '@/hooks/useStateThreshold';
import { useLedConfigStores, useLedStores, useSlideConfigStores } from '@/stores';
import { IMeasuring } from '@/types';
import { EStatus, ISlide, RealtimeContent, RealTimeMetadata, SlideConfigDto } from '@/types/slide';
import { cn } from '@/utils/tailwind';
import { useRef } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { ResponsiveTextWithOverride } from './ResponsiveTextWithOverride';
import { priorityGrid } from '../compoType/constants';

type Props = {
  className?: string;
  dataItem: IMeasuring & { value: number | null };
  dataSlide: SlideConfigDto;
  totalItem: number;
};

export default function GridItem({
  dataItem,
  className,
  dataSlide,
  totalItem,
  ...props
}: Props & React.HTMLAttributes<HTMLDivElement>) {
  const dataLedDetail = useLedStores((store) => store.dataLedDetail);
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const intl = useIntl();
  const itemGridRef = useRef<HTMLDivElement>(null);
  const decimalFormat = dataLedDetail?.decimal ?? 2;
  const locale = intl.locale as 'vi' | 'en';
  const dataMetadata = dataSlide.content.metadata as RealTimeMetadata;
  const { styleThreshold, isStateThreshold } = useStateThreshold(dataItem, dataMetadata);

  const getLimitText = () => {
    if (dataItem.maxLimit !== null && dataItem.minLimit !== null) {
      return (
        <span className="text-[100%] text-current truncate block font-medium">
          <FormattedMessage
            defaultMessage="Giới hạn: {minLimit} -> {maxLimit}"
            id="screens.presentations.components.GridItem.1038629890"
            values={{
              minLimit: dataItem.minLimit,
              maxLimit: dataItem.maxLimit,
            }}
          />
        </span>
      );
    }

    if (dataItem.minLimit !== null) {
      return (
        <span className="text-[100%] text-current truncate block font-medium">
          <FormattedMessage
            defaultMessage="Giới hạn: ≥ {value}"
            id="screens.presentations.components.GridItem.810771583"
            values={{
              value: dataItem.minLimit,
            }}
          />
        </span>
      );
    }

    if (dataItem.maxLimit !== null) {
      return (
        <span className="text-[100%] text-current truncate block font-medium">
          <FormattedMessage
            defaultMessage="Giới hạn: ≤ {value}"
            id="screens.presentations.components.GridItem.996682880"
            values={{
              value: dataItem.maxLimit,
            }}
          />
        </span>
      );
    }

    return (
      <span className="text-[100%] text-current truncate block font-medium">
        <FormattedMessage defaultMessage="Không có giới hạn" id="screens.presentations.components.GridItem.235960741" />
      </span>
    );
  };

  return (
    <div
      {...props}
      ref={itemGridRef}
      className={cn(
        'flex flex-col w-full h-full',
        'px-1',
        'md:px-2',
        'lg:px-3',
        'xl:px-[10px]',
        '2xl:px-4',
        '3xl:px-5',
        className,
      )}
      style={{ ...styleThreshold }}
    >
      <div className="w-full flex flex-row flex-shrink-0 items-center justify-between">
        <div className="flex-shrink-0 max-w-[66.6667%]">
          <ResponsiveTextWithOverride
            className="text-current truncate font-bold block"
            value={dataMetadata.layout.config.fontSize.config.measureCode}
          >
            {dataItem.name[locale]}
          </ResponsiveTextWithOverride>
        </div>
        {dataItem?.unit?.length ? (
          <div className="flex-1 min-w-0 flex justify-end overflow-hidden">
            <ResponsiveTextWithOverride
              className="text-current font-semibold"
              value={dataMetadata.layout.config.fontSize.config.measureUnit}
            >
              (
            </ResponsiveTextWithOverride>
            <ResponsiveTextWithOverride
              value={dataMetadata.layout.config.fontSize.config.measureUnit}
              className="truncate text-current font-semibold"
            >
              {dataItem.unit}
            </ResponsiveTextWithOverride>
            <ResponsiveTextWithOverride
              value={dataMetadata.layout.config.fontSize.config.measureUnit}
              className="text-current font-semibold"
            >
              )
            </ResponsiveTextWithOverride>
          </div>
        ) : null}
      </div>
      <div
        className="flex-1 min-h-0 w-full flex items-center justify-center"
        style={{ ...(isStateThreshold === EStatus.Good && { color: dataConfig?.init.colors.color_within_limit }) }}
      >
        <ResponsiveTextWithOverride
          value={dataMetadata.layout.config.fontSize.config.measureValue}
          className="text-current truncate font-semibold block"
        >
          {dataItem.value !== null
            ? new Intl.NumberFormat('en-US', {
                minimumFractionDigits: decimalFormat,
                maximumFractionDigits: decimalFormat,
              }).format(dataItem.value)
            : '---'}
        </ResponsiveTextWithOverride>
      </div>
      <div className="flex-shrink-0 w-full flex items-center justify-center">
        <ResponsiveTextWithOverride
          value={dataMetadata.layout.config.fontSize.config.limitThreshold}
          className="text-current truncate font-semibold block"
        >
          {getLimitText()}
        </ResponsiveTextWithOverride>
      </div>
    </div>
  );
}
