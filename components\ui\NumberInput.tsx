import { cn } from '@/utils/tailwind';
import { MinusIcon, PlusIcon, XIcon } from '@phosphor-icons/react';
import type { InputNumberProps as RcInputNumberProps, ValueType } from 'rc-input-number';
import RcInputNumber from 'rc-input-number';
import React, { useEffect } from 'react';
import { VariantProps, cva } from 'class-variance-authority';
import clsx from 'clsx';

const inputVariants = cva(
  clsx('group flex items-center gap-2 w-full rounded-lg transition-all duration-300 ease-in overflow-hidden px-4'),
  {
    variants: {
      variant: {
        default: clsx(
          'default bg-white text-gray-800 border border-gray-200 hover:ring-2 hover:ring-primary-300 hover:bg-white active:ring-2 active:ring-primary-300 active:bg-white',
        ),
        success: clsx('success border border-green-500'),
        error: clsx('error border border-red-500'),
      },
      size: {
        sm: clsx('h-10 text-sm leading-[21px]'),
        md: clsx('h-11 text-sm leading-[21px]'),
        lg: clsx('h-12 text-base leading-6'),
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  },
);

const controlVariants = cva(
  clsx(
    '[&_.rc-input-number-handler]:flex [&_.rc-input-number-handler]:items-center [&_.rc-input-number-handler]:justify-center [&_.rc-input-number-handler]:text-primary-500 [&_.rc-input-number-handler]:flex-shink-0',
    '[&_.rc-input-number-handler]:bg-gray-100 [&_.rc-input-number-handler]:border-l [&_.rc-input-number-handler]:rounded-l-gray-200 [&_.rc-input-number-handler:hover]:bg-gray-200',
  ),
  {
    variants: {
      size: {
        sm: clsx('[&_.rc-input-number-handler]:h-10 [&_.rc-input-number-handler]:w-10'),
        md: clsx('[&_.rc-input-number-handler]:h-11 [&_.rc-input-number-handler]:w-11'),
        lg: clsx('[&_.rc-input-number-handler]:h-12 [&_.rc-input-number-handler]:w-12'),
      },
    },
    defaultVariants: {
      size: 'md',
    },
  },
);

type InputStatus = '' | 'warning' | 'error';

export interface InputNumberProps<T extends ValueType = ValueType>
  extends Omit<RcInputNumberProps<T>, 'prefix' | 'size' | 'controls'>,
    VariantProps<typeof inputVariants> {
  prefixCls?: string;
  rootClassName?: string;
  addonBefore?: React.ReactNode;
  addonAfter?: React.ReactNode;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  disabled?: boolean;
  status?: InputStatus;
  controls?: boolean | { upIcon?: React.ReactNode; downIcon?: React.ReactNode };
  /**
   * @since 5.13.0
   * @default "outlined"
   */
  isError?: boolean;
  classNameWrapper?: string;
  readOnly?: boolean;
  clearable?: boolean;
  onBlurInput?: (value: ValueType | null) => void;
}

const NumberInput = React.forwardRef<HTMLInputElement, InputNumberProps>((props, ref) => {
  const inputRef = React.useRef<HTMLInputElement>(null);

  React.useImperativeHandle(ref, () => inputRef.current!);

  const {
    className,
    rootClassName,
    size,
    disabled,
    prefixCls,
    addonBefore,
    addonAfter,
    prefix,
    suffix,
    readOnly,
    status,
    controls = false,
    keyboard = false,
    variant,
    classNames,
    placeholder = 'Nhập giá trị',
    classNameWrapper,
    clearable = true,
    value,
    defaultValue,
    onBlurInput,
    min,
    max,
    ...others
  } = props;
  let upIcon = <PlusIcon size={20} weight="regular" className={`${prefixCls}-handler-up-inner`} />;
  let downIcon = <MinusIcon size={20} weight="regular" className={`${prefixCls}-handler-down-inner`} />;
  const controlsTemp = typeof controls === 'boolean' ? controls : undefined;
  if (typeof controls === 'object') {
    upIcon =
      typeof controls.upIcon === 'undefined' ? (
        upIcon
      ) : (
        <span className={`${prefixCls}-handler-up-inner`}>{controls.upIcon}</span>
      );
    downIcon =
      typeof controls.downIcon === 'undefined' ? (
        downIcon
      ) : (
        <span className={`${prefixCls}-handler-down-inner`}>{controls.downIcon}</span>
      );
  }

  const [currentValue, setCurrentValue] = React.useState<ValueType | null>(value ?? null);

  useEffect(() => {
    setCurrentValue(value ?? null);
  }, [value]);

  return (
    <div
      className={cn(
        inputVariants({ variant, size }),
        {
          'bg-gray-50 hover:bg-gray-50 hover:ring-0 active:ring-0 opacity-50': props.disabled,
        },
        { 'px-0 pl-4': controls },
        classNameWrapper,
      )}
    >
      <RcInputNumber
        {...others}
        ref={inputRef}
        disabled={disabled}
        className={cn(
          'peer flex flex-row gap-2 w-full h-full bg-inherit overflow-hidden',
          'p-0 m-0 border-none border-0 outline-none outline-0',
          '[&_.rc-input-number-prefix]:flex-shrink-0 [&_.rc-input-number-prefix]:w-auto [&_.rc-input-number-prefix]:flex [&_.rc-input-number-prefix]:items-center [&_.rc-input-number-prefix]:justify-center',
          '[&_.rc-input-number-suffix]:flex-shrink-0 [&_.rc-input-number-suffix]:w-auto [&_.rc-input-number-suffix]:flex [&_.rc-input-number-suffix]:items-center [&_.rc-input-number-suffix]:justify-center',
          '[&_.rc-input-number]:w-full  [&_.rc-input-number]:flex-1 [&_.rc-input-number]:min-w-0 [&_.rc-input-number]:flex [&_.rc-input-number]:flex-row-reverse [&_.rc-input-number]:items-center',
          '[&_input]:w-full [&_input]:h-full [&_input]:bg-inherit [&_input]:text-gray-700 [&_input]:text-sm [&_input]:leading-[21px] [&_input]:outline-0',
          '[&_input]:focus-visible:border-none [&_input]:focus-visible:border-0 [&_input]:focus-visible:outline-none [&_input]:focus-visible:outline-0',
          '[&_input]:placeholder:text-sm [&_input]:placeholder:leading-[21px] [&_input]:placeholder:text-gray-500',
          '[&_.rc-input-number-input-wrap]:flex-1',
          '[&_.rc-input-number-handler-wrap]:flex [&_.rc-input-number-handler-wrap]:flex-row-reverse',
          { 'flex-row-reverse': controls },
          controlVariants({ size }),
          className,
        )}
        classNames={classNames}
        upHandler={upIcon}
        downHandler={downIcon}
        prefixCls={prefixCls}
        readOnly={readOnly}
        controls={controlsTemp}
        keyboard={keyboard}
        prefix={prefix}
        placeholder={placeholder}
        suffix={
          suffix && (
            <div className="flex flex-row gap-2 items-center">
              {currentValue !== '' &&
                currentValue !== null &&
                currentValue !== undefined &&
                !disabled &&
                !readOnly &&
                clearable && (
                  <span
                    className="block cursor-pointer"
                    onClick={() => {
                      others.onChange?.(null);
                      setCurrentValue(null);
                      if (inputRef.current) {
                        inputRef.current.focus();
                      }
                    }}
                  >
                    <XIcon className="cursor-pointer text-gray-500" size={14} />
                  </span>
                )}
              {suffix}
            </div>
          )
        }
        addonBefore={addonBefore}
        addonAfter={addonAfter}
        value={currentValue}
        defaultValue={value!}
        onBlur={(event) => {
          if (others.onBlur) {
            return others.onBlur?.(event);
          }
          const inputVal =
            props.precision === 0
              ? Math.round(parseFloat(event.target.value))
              : Number(Number(parseFloat(event.target.value)).toFixed(props.precision));

          // Handle empty or null values
          if (isNaN(inputVal) || inputVal === null || inputVal === undefined) {
            setCurrentValue(defaultValue || min || null);
            if (onBlurInput) {
              onBlurInput(defaultValue || min || null);
            }
            return;
          }

          // Convert to number and validate
          const numValue = Number(inputVal);
          let nextValue: ValueType | null = numValue;

          // Apply min/max constraints
          if (min !== undefined && numValue < Number(min)) {
            nextValue = Number(defaultValue) || Number(min);
          } else if (max !== undefined && numValue > Number(max)) {
            nextValue = Number(defaultValue) || Number(max);
          }
          setCurrentValue(nextValue);

          // Call appropriate blur handlers
          if (onBlurInput) {
            onBlurInput(nextValue);
          }
        }}
        onStep={(value) => {
          if (min && value < min) {
            setCurrentValue(min);
            others.onChange?.(min);
          } else if (max && value > max) {
            setCurrentValue(max);
            others.onChange?.(max);
          } else {
            setCurrentValue(value);
            others.onChange?.(value);
          }
        }}
        onKeyDown={(event) => {
          if (event.key === 'Enter') {
            event.preventDefault();
          }

          // Chỉ cho phép số nguyên dương
          const allowedKeys = [
            'Backspace',
            'Delete',
            'Tab',
            'Escape',
            'Enter',
            'ArrowLeft',
            'ArrowRight',
            'ArrowUp',
            'ArrowDown',
            'Home',
            'End',
          ];

          const isNumber = /^[0-9]$/.test(event.key);

          if (!allowedKeys.includes(event.key) && !isNumber) {
            event.preventDefault();
          }
        }}
      />
    </div>
  );
});

NumberInput.displayName = 'NumberInput';
export { NumberInput };
