'use server';

import { CONFIG_LANGUAGE, LANGUEGES_EN } from '@/constants';
import { match } from '@formatjs/intl-localematcher';
import Negotiator from 'negotiator';
import { NextRequest, NextResponse } from 'next/server';

const config = CONFIG_LANGUAGE;

export const detectLanguage = async (request: NextRequest) => {
  const negotiatorHeaders: Record<string, string> = {};
  request.headers.forEach((value, key) => (negotiatorHeaders[key] = value));

  const { locales, defaultLocale } = config;
  const languages = new Negotiator({ headers: negotiatorHeaders }).languages();
  try {
    return match(languages, locales, LANGUEGES_EN);
  } catch (e) {
    return LANGUEGES_EN;
  }
};

export const initLanguage = async (request: NextRequest, response: NextResponse) => {
  const langCookie = request.cookies.get(config.cookieName)?.value;

  const language = langCookie ?? (await detectLanguage(request));
  response.cookies.set(config.cookieName, language, {
    httpOnly: true,
    secure: true,
    maxAge: 14 * 24 * 3600,
    sameSite: 'none',
  });
};
