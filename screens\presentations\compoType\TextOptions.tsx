import GroupBox from '@/components/sessions/GroupBox';
import { NumberInput } from '@/components/ui/NumberInput';
import { PaletteColorPicker } from '@/components/ui/PaletteColorPicker';
import { useResponsiveFontSize } from '@/hooks/useResponsiveFontSize';
import { useSlideConfigStores } from '@/stores';
import { ISlide, TextContent } from '@/types/slide';
import { useEffect, useMemo } from 'react';
import { FormattedMessage } from 'react-intl';
import { Form, FormInstance, Switch } from 'ui-components';
import { PRIORITY_SCALE } from '../components/ResponsiveTextWithOverride';

type FormData = {
  width: number;
  height: number;
  presentations: ISlide[];
};

type PropsTextOptions = {
  focusedIndex: number;
  form: FormInstance;
  dataContent: {
    metadata: TextContent;
  };
  initFont: {
    text: {
      [x: string]: number;
    };
  };
};

export default function TextOptions({ focusedIndex, form, dataContent, initFont }: PropsTextOptions) {
  const configFontSize = useMemo(() => {
    return dataContent?.metadata?.fontSize?.config;
  }, [dataContent]);

  const isAuto = useMemo(() => dataContent?.metadata?.fontSize?.auto, [dataContent]);

  useEffect(() => {
    if (isAuto) {
      return form.setFieldValue(
        ['presentations', focusedIndex, 'content', 'metadata', 'fontSize', 'config'],
        initFont.text,
      );
    }
  }, [isAuto, initFont, focusedIndex, form]);

  return (
    <>
      <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
        <span className="font-semibold text-base text-gray-700">
          <FormattedMessage defaultMessage="Cấu hình hiển thị" id="screens.presentations.FooterConfigSlide.206334904" />
        </span>
        <div className="flex flex-col gap-2">
          <span className="font-medium text-sm text-gray-700">
            <FormattedMessage defaultMessage="Loại dữ liệu" id="screens.presentations.ConfigSlide.1454429291" />
          </span>
          <GroupBox>
            <div className="flex flex-row">
              <div className="h-11 flex-1 min-w-0 p-3 border-r border-gray-200">
                <span className="font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Màu nền"
                    id="app.led.[led_key].(configure).config.slide-shows.page.1845624381"
                  />
                </span>
              </div>
              <div className="w-[160px] flex-shrink-0">
                <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'config', 'background']}>
                  <PaletteColorPicker className="border-none" />
                </Form.Field>
              </div>
            </div>
            <div className="flex flex-row">
              <div className="h-11 flex-1 min-w-0 p-3 border-r border-gray-200">
                <span className="font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Màu chữ"
                    id="app.led.[led_key].(configure).config.slide-shows.page.1845380780"
                  />
                </span>
              </div>
              <div className="w-[160px] flex-shrink-0">
                <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'config', 'color']}>
                  <PaletteColorPicker className="border-none" />
                </Form.Field>
              </div>
            </div>
          </GroupBox>
        </div>
      </div>

      {/* Font Size Configuration Section */}
      <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'fontSize', 'auto']}>
        {(control) => {
          return (
            <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
              <span className="font-semibold text-base text-gray-700">
                <FormattedMessage
                  defaultMessage="Kích thước kiểu chữ"
                  id="screens.presentations.ConfigSlide.1435964239"
                />
              </span>
              <div className="flex flex-col gap-2">
                <span className="font-medium text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage=" Cấu hình kích thước"
                    id="screens.presentations.ConfigSlide.641803552"
                  />
                </span>
                <div className="h-full w-full">
                  <GroupBox>
                    <div className="flex flex-row gap-3 p-3 items-center">
                      <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                        <FormattedMessage
                          defaultMessage="Tự động chọn kích thước phù hợp"
                          id="screens.presentations.ConfigSlide.320775361"
                        />
                      </span>
                      <Form.Field
                        name={['presentations', focusedIndex, 'content', 'metadata', 'fontSize', 'auto']}
                        trigger="onCheckedChange"
                        valuePropName="checked"
                      >
                        <Switch className="flex-shrink-0" />
                      </Form.Field>
                    </div>
                  </GroupBox>
                </div>
                {control.value && (
                  <span className="font-normal text-xs text-gray-700">
                    <FormattedMessage
                      defaultMessage="Hệ thống sẽ tự động điều chỉnh kích thước nội dung để phù hợp với kích thước màn hình."
                      id="screens.presentations.HeaderConfigSlide.291032092"
                    />
                  </span>
                )}
              </div>
              {!control.value && (
                <div className="flex flex-col gap-2">
                  <span className="font-medium text-sm text-gray-700">
                    <FormattedMessage
                      defaultMessage="Tùy biến riêng"
                      id="screens.presentations.ConfigSlide.1975998546"
                    />
                  </span>
                  <div className="h-full w-full">
                    <GroupBox>
                      <div className="flex flex-row items-center bg-gray-50">
                        <span className="flex-1 min-w-0 p-3 font-semibold text-xs text-gray-700 uppercase">
                          <FormattedMessage
                            defaultMessage="Nội dung"
                            id="screens.presentations.HeaderConfigSlide.768903020"
                          />
                        </span>
                        <span className="w-[110px] flex-shrink-0 p-3 font-semibold text-xs text-gray-700 uppercase text-center">
                          <FormattedMessage
                            defaultMessage="Kích thước"
                            id="screens.presentations.HeaderConfigSlide.1145935829"
                          />
                        </span>
                      </div>
                      <div className="flex flex-row items-center">
                        <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                          <FormattedMessage
                            defaultMessage="Nội dung"
                            id="screens.presentations.HeaderConfigSlide.768903020"
                          />
                        </span>
                        <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                          <Form.Field
                            name={[
                              'presentations',
                              focusedIndex,
                              'content',
                              'metadata',
                              'fontSize',
                              'config',
                              'content',
                            ]}
                            trigger="onBlurInput"
                          >
                            <NumberInput
                              precision={0}
                              placeholder=""
                              suffix="px"
                              defaultValue={configFontSize.content}
                              classNameWrapper="h-[33px]"
                              clearable={false}
                              controls={false}
                              keyboard
                            />
                          </Form.Field>
                        </div>
                      </div>
                    </GroupBox>
                  </div>
                  <span className="font-normal text-xs text-gray-700">
                    <FormattedMessage
                      defaultMessage="Cho phép tùy chỉnh kích thước nội dung hiển thị trên màn hình. Hệ thống sẽ không tự động thay đổi kích thước."
                      id="screens.presentations.HeaderConfigSlide.595062375"
                    />
                  </span>
                </div>
              )}
            </div>
          );
        }}
      </Form.Field>
    </>
  );
}
