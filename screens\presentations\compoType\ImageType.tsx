import { NumberInput } from '@/components/ui/NumberInput';
import { ImageContent } from '@/types/slide';
import { FormattedMessage } from 'react-intl';
import { Form } from 'ui-components';
import ImageUpload from '../components/ImageUpload';

type PropsImage = {
  focusedIndex: number;
  dataContent: {
    metadata: ImageContent;
  };
};
export const ImageType = ({ focusedIndex, dataContent }: PropsImage) => {
  const config = dataContent.metadata.config;
  return (
    <>
      <div className="flex-1 flex flex-row gap-4">
        <div className="flex flex-col gap-2">
          <span className="font-medium text-sm text-gray-700">
            <FormattedMessage
              defaultMessage="Số cột tối đa {value}"
              id="screens.presentations.components.TableConfigTemplate.1314136401"
              values={{
                value: '(1-3)',
              }}
            />
          </span>
          <Form.Field
            name={['presentations', focusedIndex, 'content', 'metadata', 'config', 'column']}
            trigger="onBlurInput"
          >
            <NumberInput
              placeholder=""
              precision={0}
              size="sm"
              keyboard
              controls
              clearable={false}
              min={1}
              max={3}
              defaultValue={3}
            />
          </Form.Field>
        </div>
        <div className="flex flex-col gap-2">
          <span className="font-medium text-sm text-gray-700">
            <FormattedMessage
              defaultMessage="Số hàng tối đa {value}"
              id="screens.presentations.components.TableConfigTemplate.281785528"
              values={{
                value: '(1-3)',
              }}
            />
          </span>
          <Form.Field
            name={['presentations', focusedIndex, 'content', 'metadata', 'config', 'row']}
            trigger="onBlurInput"
          >
            <NumberInput
              placeholder=""
              precision={0}
              size="sm"
              keyboard
              controls
              clearable={false}
              min={1}
              max={3}
              defaultValue={3}
            />
          </Form.Field>
        </div>
      </div>
      <div
        className="grid gap-2 h-[320px]"
        style={{
          gridTemplateColumns: `repeat(${config.column}, minmax(0, 1fr))`,
          gridTemplateRows: `repeat(${config.row}, minmax(0, 1fr))`,
        }}
      >
        {Array.from({ length: config?.column * config?.row }).map((_, index) => {
          return (
            <div key={index} className="flex-1">
              <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'images', index]}>
                <ImageUpload />
              </Form.Field>
            </div>
          );
        })}
      </div>
    </>
  );
};
