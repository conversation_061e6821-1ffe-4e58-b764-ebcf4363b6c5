import Autoplay, { AutoplayType } from 'embla-carousel-autoplay';
import useEmblaCarousel from 'embla-carousel-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';

const slides = [
  {
    type: 'group',
    header: 'Header Group 1',
    footer: 'Footer Group 1',
    slides: [
      { type: 'content', content: 'Slide 1.1: Nội dung 1' },
      { type: 'content', content: 'Slide 1.2: Nội dung 2' },
      { type: 'content', content: 'Slide 1.3: Nội dung 3' },
      { type: 'content', content: 'Slide 1.4: Nội dung 4' },
    ],
  },
  {
    type: 'video',
    src: 'https://www.w3schools.com/html/mov_bbb.mp4',
  },
  {
    type: 'group',
    header: 'Header Group 2',
    slides: [
      { type: 'content', content: 'Slide 3.1: Nội dung 5' },
      { type: 'content', content: 'Slide 3.2: Nội dung 6' },
    ],
  },
  {
    type: 'content',
    content: 'Slide 4: Nội dung bất kỳ',
  },
];

// Helper để flatten và mapping index
function getFlatSlidesWithGroup(slides: any[]) {
  const flat: any[] = [];
  slides.forEach((slide, groupIdx) => {
    if (slide.type === 'group') {
      slide.slides.forEach((sub: any[], contentIdx: number) => {
        flat.push({
          ...sub,
          groupIdx,
          contentIdx,
          groupHeader: slide.header,
          groupFooter: slide.footer,
          isGroup: true,
        });
      });
    } else {
      flat.push({ ...slide, groupIdx: null, contentIdx: null, isGroup: false });
    }
  });
  return flat;
}

const flatSlides = getFlatSlidesWithGroup(slides);

export default function Demo() {
  // State mới: selectedFlatIndex
  const [selectedFlatIndex, setSelectedFlatIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [loopTime, setLoopTime] = useState(3000); // ms
  const [videoHover, setVideoHover] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const autoplayRef = useRef<AutoplayType>(null);
  const [progressKey, setProgressKey] = useState(0);

  // Embla với autoplay plugin
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true, watchDrag: false }, [
    Autoplay({
      delay: loopTime,
      stopOnInteraction: false,
      stopOnMouseEnter: true,
      stopOnLastSnap: false,
      active: !isPaused && flatSlides[selectedFlatIndex]?.type !== 'video',
    }),
  ]);

  // Reset progressStart khi slide mới, resume, hoặc đổi delay
  useEffect(() => {
    setProgressKey((prev) => prev + 1);
  }, [selectedFlatIndex, isPaused, loopTime]);

  // Khi là video, dừng autoplay, khi hết video thì resume
  useEffect(() => {
    const slide = flatSlides[selectedFlatIndex];
    if (slide.type === 'video' && autoplayRef.current) {
      autoplayRef.current.stop();
    } else if (autoplayRef.current && !isPaused) {
      autoplayRef.current.play();
    }
  }, [selectedFlatIndex, isPaused, loopTime]);

  // Khi video kết thúc thì next
  function handleVideoEnded() {
    if (!isPaused && emblaApi) emblaApi.scrollNext();
    if (autoplayRef.current && !isPaused) autoplayRef.current.play();
  }

  // Khi chuyển slide, nếu là video thì play lại
  useEffect(() => {
    const slide = flatSlides[selectedFlatIndex];
    if (slide.type === 'video' && videoRef.current) {
      videoRef.current.currentTime = 0;
      videoRef.current.play();
    } else {
      const videoElement = document.querySelectorAll('video');
      if (videoElement.length > 0) {
        videoElement.forEach((video) => {
          video.currentTime = 0;
          video.pause();
        });
      }
    }
  }, [selectedFlatIndex]);

  // Lắng nghe sự kiện Embla để cập nhật selectedIndex
  useEffect(() => {
    if (!emblaApi) return;
    const onSelect = () => {
      setSelectedFlatIndex(emblaApi.selectedScrollSnap());
    };
    emblaApi.on('select', onSelect);
    setSelectedFlatIndex(emblaApi.selectedScrollSnap());
    return () => {
      emblaApi.off('select', onSelect);
    };
  }, [emblaApi]);

  // Next/Prev logic
  const scrollTo = useCallback(
    (idx: number) => {
      setSelectedFlatIndex(idx);
      if (emblaApi) emblaApi.scrollTo(idx);
    },
    [emblaApi],
  );

  const scrollNext = useCallback(() => {
    const current = flatSlides[selectedFlatIndex];
    // Nếu là group, kiểm tra còn content trong group không
    if (current.isGroup) {
      // Tìm group gốc
      const group = slides[current?.groupIdx];
      if (current.contentIdx < group.slides!.length - 1) {
        // Next trong group
        const nextFlatIdx = flatSlides.findIndex(
          (s) => s.isGroup && s.groupIdx === current.groupIdx && s.contentIdx === current.contentIdx + 1,
        );
        if (nextFlatIdx !== -1) {
          setSelectedFlatIndex(nextFlatIdx);
          if (emblaApi) emblaApi.scrollTo(nextFlatIdx);
          return;
        }
      }
    }
    // Nếu không phải group hoặc đã hết group, next bình thường
    if (selectedFlatIndex < flatSlides.length - 1) {
      setSelectedFlatIndex(selectedFlatIndex + 1);
      if (emblaApi) emblaApi.scrollTo(selectedFlatIndex + 1);
    }
  }, [selectedFlatIndex, emblaApi]);

  const scrollPrev = useCallback(() => {
    const current = flatSlides[selectedFlatIndex];
    // Nếu là group, kiểm tra còn content trước trong group không
    if (current.isGroup) {
      if (current.contentIdx > 0) {
        const prevFlatIdx = flatSlides.findIndex(
          (s) => s.isGroup && s.groupIdx === current.groupIdx && s.contentIdx === current.contentIdx - 1,
        );
        if (prevFlatIdx !== -1) {
          setSelectedFlatIndex(prevFlatIdx);
          if (emblaApi) emblaApi.scrollTo(prevFlatIdx);
          return;
        }
      }
    }
    // Nếu không phải group hoặc đã hết group, prev bình thường
    if (selectedFlatIndex > 0) {
      setSelectedFlatIndex(selectedFlatIndex - 1);
      if (emblaApi) emblaApi.scrollTo(selectedFlatIndex - 1);
    }
  }, [selectedFlatIndex, emblaApi]);

  // Render slide
  function renderSlide(slide: any, idx: number) {
    if (slide.type === 'video') {
      return (
        <div key={idx} className="w-full h-full flex flex-col items-center justify-center min-h-[200px]">
          <div
            className="relative group h-full w-full"
            onMouseEnter={() => setVideoHover(true)}
            onMouseLeave={() => setVideoHover(false)}
          >
            <video
              ref={videoRef}
              src={slide.src}
              controls={videoHover}
              autoPlay
              muted
              playsInline
              onEnded={handleVideoEnded}
              className="w-full h-full aspect-video transition-all duration-200"
              style={{ outline: videoHover ? '2px solid #3b82f6' : 'none' }}
            />
          </div>
        </div>
      );
    }
    return (
      <div
        key={idx}
        className="w-full h-full text-2xl font-bold p-8 bg-neutral-800 min-w-[300px] min-h-[200px] flex items-center justify-center"
      >
        {slide.content}
      </div>
    );
  }

  // Hiển thị vị trí slide
  function renderProgress() {
    return (
      <div className="text-sm text-neutral-400 mb-2">
        Slide {selectedFlatIndex + 1} / {flatSlides.length}
      </div>
    );
  }

  // Thanh progress bar custom
  function renderTimerBar() {
    const slide = flatSlides[selectedFlatIndex];
    let delayValue = 0;
    if (slide.type === 'video' && videoRef.current) {
      delayValue = (videoRef.current.duration - videoRef.current.currentTime) * 1000;
    } else {
      const autoplay = emblaApi?.plugins().autoplay;
      delayValue = (autoplay?.options.delay as number) || loopTime;
    }
    return (
      <div className="w-full h-1 bg-neutral-700 rounded overflow-hidden mb-2">
        <div
          key={progressKey}
          className="h-full bg-blue-500"
          style={{
            width: '0%',
            animation: !isPaused ? `progressBarAnim ${delayValue}ms linear forwards` : 'none',
          }}
        />
        <style>
          {`
            @keyframes progressBarAnim {
              from { width: 0%; }
              to { width: 100%; }
            }
          `}
        </style>
      </div>
    );
  }

  // Render header/footer/content
  function renderHeader() {
    const slide = flatSlides[selectedFlatIndex];
    if (slide.isGroup)
      return (
        <div className="h-[90px] shrink-0 w-full bg-amber-200 text-black">
          <div className="h-full w-full text-xl font-semibold text-center">{slide.groupHeader}</div>
        </div>
      );
    return null;
  }
  function renderFooter() {
    const slide = flatSlides[selectedFlatIndex];
    if (slide.isGroup)
      return (
        <div className="h-[60px] shrink-0 w-full bg-lime-200 text-black">
          <div className="w-full h-full text-base text-center">{slide.groupFooter}</div>
        </div>
      );
    return null;
  }
  function renderContent() {
    return (
      <div className="w-full h-full">
        <div className="overflow-hidden h-full w-full" ref={emblaRef}>
          <div className="flex h-full w-full">
            {flatSlides.map((slide, idx) => (
              <div className="flex-[0_0_100%] min-w-0 w-full h-full flex items-center justify-center" key={idx}>
                {renderSlide(slide, idx)}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full flex flex-col items-center bg-back text-white">
      <div className="flex flex-1 min-h-0 flex-col items-center w-full border">
        {renderHeader()}
        <div className="flex-1 min-h-0 w-full border-y-1">{renderContent()}</div>
        {renderFooter()}
      </div>
      {renderTimerBar()}
      <div className="flex flex-shrink-0 gap-2 mt-6">
        <button
          className="px-4 py-2 rounded bg-gray-600 hover:bg-gray-700 text-white font-semibold text-lg"
          onClick={scrollPrev}
        >
          Prev
        </button>
        <button
          className={`px-4 py-2 rounded ${
            isPaused ? 'bg-green-600 hover:bg-green-700' : 'bg-yellow-600 hover:bg-yellow-700'
          } text-white font-semibold text-lg`}
          onClick={() => setIsPaused((p) => !p)}
        >
          {isPaused ? 'Resume' : 'Pause'}
        </button>
        <button
          className="px-6 py-2 rounded bg-blue-600 hover:bg-blue-700 text-white font-semibold text-lg"
          onClick={scrollNext}
        >
          Next
        </button>
      </div>
      <div className="mt-4 flex-shrink-0 flex items-center gap-2">
        <label htmlFor="loopTime" className="text-sm text-neutral-300">
          Loop time (ms):
        </label>
        <input
          id="loopTime"
          type="number"
          min={500}
          step={100}
          value={loopTime}
          onChange={(e) => setLoopTime(Number(e.target.value))}
          className="px-2 py-1 rounded bg-neutral-800 border border-neutral-600 text-white w-24"
        />
      </div>
    </div>
  );
}
