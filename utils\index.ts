export const generateNums = (idString: string = '') => {
  let hash = 0;
  for (let i = 0; i < idString.length; i++) {
    hash = (hash << 5) - hash + idString.charCodeAt(i);
    hash |= 0;
  }

  const seed = Math.abs(hash);

  function seededRandom(seed: number) {
    const x = Math.sin(seed++) * 10000;
    return Math.floor((x - Math.floor(x)) * 6 + 1);
  }

  return seededRandom(seed);
};
export const getPagination = (page: number = 0, size: number = 10) => {
  const limit = +size;
  const from = page * limit;
  const to = page ? from + size - 1 : size - 1;

  return { from, to };
};

export const cleanObject = (obj: Record<string, any>) => {
  const result: Record<string, any> = {};
  if (obj) {
    Object.keys(obj).forEach((key) => {
      if ((!Array.isArray(obj[key]) && obj[key]) || typeof obj[key] === 'number' || obj[key]?.length)
        result[key] = obj[key];
    });
  }
  return result;
};

export const removeAccents = (str: string) => str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');

export const replaceAll = (str: string, find: string, replace: string) => str.replace(new RegExp(find, 'g'), replace);

export const extractAfterSegments = (path: string, skipSegments: number = 2): string => {
  const segments = path.split('/').filter(Boolean);
  const remain = segments.slice(skipSegments);
  return '/' + remain.join('/');
};

export const capitalizeFirstLetter = (str: string, split?: string) => {
  if (!str) return str;

  // Split string by dot and handle multiple sentences
  return str
    .split(split || '.')
    .map((sentence) => {
      // Trim each sentence and capitalize first letter
      const trimmed = sentence.trim();
      return trimmed ? trimmed.charAt(0).toUpperCase() + trimmed.slice(1).toLowerCase() : '';
    })
    .join(split + ' ' || '. '); // Join with dot and space
};

export function paginateArray<T>(arr: T[], itemsPerPage: number): T[][] {
  const pages = [];
  for (let i = 0; i < arr.length; i += itemsPerPage) {
    pages.push(arr.slice(i, i + itemsPerPage));
  }
  return pages;
}

// Tạo Map để lưu trữ breakpoint và breakpoint tiếp theo
const breakpointRanges = new Map<number, number>([
  [320, 640],
  [640, 768],
  [768, 1024],
  [1024, 1080],
  [1080, 1280],
  [1280, 1920],
]);

// Cache để lưu trữ kết quả đã tính toán
const breakpointCache = new Map<number, number>();

/**
 * Find the closest breakpoint for a given width
 * @param width The width to find the breakpoint for
 * @returns The closest breakpoint value
 */
export function getClosestBreakpoint(width: number): number {
  // Kiểm tra trong cache trước
  if (breakpointCache.has(width)) {
    return breakpointCache.get(width)!;
  }

  // Kiểm tra nếu width nằm trong khoảng của breakpoint
  for (const [start, end] of breakpointRanges) {
    if (width >= start && width < end) {
      const result = start;
      breakpointCache.set(width, result);
      return result;
    }
  }

  // Nếu width nhỏ hơn breakpoint nhỏ nhất
  if (width < 320) {
    const result = 320;
    breakpointCache.set(width, result);
    return result;
  }

  // Nếu width lớn hơn breakpoint lớn nhất
  const result = 1920;
  breakpointCache.set(width, result);
  return result;
}

export const hexToRgba = (hex: string, alpha = 1): string => {
  // Remove "#" if present
  const sanitizedHex = hex.replace(/^#/, '');

  // Convert shorthand hex to full form (e.g. "#03F" => "#0033FF")
  const fullHex =
    sanitizedHex.length === 3
      ? sanitizedHex
          .split('')
          .map((c) => c + c)
          .join('')
      : sanitizedHex;

  if (!/^([0-9a-f]{6})$/i.test(fullHex)) {
    throw new Error('Invalid HEX color');
  }

  const r = parseInt(fullHex.slice(0, 2), 16);
  const g = parseInt(fullHex.slice(2, 4), 16);
  const b = parseInt(fullHex.slice(4, 6), 16);

  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

/**
 * Creates a debounced version of a function that delays invoking func until after wait milliseconds
 * have elapsed since the last time the debounced function was invoked.
 * 
 * @param func The function to debounce
 * @param wait The number of milliseconds to delay
 * @param immediate If true, trigger the function on the leading edge instead of the trailing edge
 * @returns A debounced version of the function with a cancel method
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate: boolean = false
): T & { cancel: () => void } {
  let timeout: NodeJS.Timeout | null = null;
  let result: ReturnType<T>;

  const debounced = function (this: any, ...args: Parameters<T>) {
    const callNow = immediate && !timeout;
    
    const later = () => {
      timeout = null;
      if (!immediate) {
        result = func.apply(this, args);
      }
    };

    if (timeout) {
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(later, wait);
    
    if (callNow) {
      result = func.apply(this, args);
    }
    
    return result;
  } as T & { cancel: () => void };

  debounced.cancel = () => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
  };

  return debounced;
}
// Export error handling and logging utilities
export * from './iframeErrorHandling';
export * from './logger';
// Export slide filtering utilities
export * from './slideFilter';
// Export subSlides generation utilities
export * from './subSlidesGeneration';
/**
 * Generates a unique ID string
 * @returns A unique ID string
 */
export function generateUniqueId(): string {
  return 'id-' + Date.now().toString(36) + '-' + Math.random().toString(36).substring(2, 9);
}