'use client';

import React, { useRef, useEffect, useState, useMemo, useCallback } from 'react';
import Frame from 'react-frame-component';
import { useLedConfigStores } from '@/stores';
import { useAppStores } from '@/stores/appStores';
import { injectAllCSS, createIframeSpecificStyles, type CSSInjectionConfig } from '@/utils/cssInjection';
import { debounce } from '@/utils';
import { IframeErrorHandler, errorRecoveryUtils, type ErrorRecoveryConfig } from '@/utils/iframeErrorHandling';
import { logIframeOperation, logScalingOperation } from '@/utils/logger';
import { ComprehensiveFallback } from './IframeFallback';

interface AutoAspectRatioProps {
  width: number;
  height: number;
  children: React.ReactNode;
  onIframeReady?: () => void; // Callback khi iframe sẵn sàng
  onCSSError?: (error: Error) => void; // Callback for CSS injection errors
  onIframeError?: (error: Error) => void; // Callback for iframe initialization errors
  fallbackCSS?: string; // Fallback CSS if injection fails
  maxRetries?: number; // Maximum retry attempts for iframe initialization
  initTimeout?: number; // Timeout for iframe initialization
  errorRecoveryConfig?: Partial<ErrorRecoveryConfig>; // Error recovery configuration
  enableDirectFallback?: boolean; // Enable direct rendering fallback
  enableDebugLogging?: boolean; // Enable debug logging
}

// Enhanced iframe state management
interface IframeState {
  isLoading: boolean;
  isIframeReady: boolean;
  isSizeCalculated: boolean;
  error: Error | null;
  retryCount: number;
  cssInjectionComplete: boolean;
}

// Error boundary component for iframe failures
class IframeErrorBoundary extends React.Component<
  { children: React.ReactNode; onError?: (error: Error) => void },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: { children: React.ReactNode; onError?: (error: Error) => void }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Iframe Error Boundary caught an error:', error, errorInfo);
    if (this.props.onError) {
      this.props.onError(error);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex items-center justify-center h-full w-full bg-gray-100 border-2 border-dashed border-gray-300">
          <div className="text-center p-4">
            <div className="text-red-500 mb-2">⚠️ Iframe Error</div>
            <div className="text-sm text-gray-600 mb-2">Failed to load preview</div>
            <button
              onClick={() => this.setState({ hasError: false, error: null })}
              className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export function AutoAspectRatio({
  width,
  height,
  children,
  onIframeReady,
  onCSSError,
  onIframeError,
  fallbackCSS,
  maxRetries = 3,
  initTimeout = 10000,
  errorRecoveryConfig,
  enableDirectFallback = true,
  enableDebugLogging = false,
  ...props
}: AutoAspectRatioProps & React.HTMLAttributes<HTMLDivElement>) {
  const isPreviewing = useAppStores((store) => store.isPreviewing);
  const containerRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const initTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const cssInjectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [iframeState, setIframeState] = useState<IframeState>({
    isLoading: true,
    isIframeReady: false,
    isSizeCalculated: false,
    error: null,
    retryCount: 0,
    cssInjectionComplete: false,
  });

  // Initialize error handler with custom config
  const errorHandler = useMemo(() => {
    return new IframeErrorHandler({
      maxRetries,
      fallbackCSS,
      enableFallbackRender: enableDirectFallback,
      timeouts: {
        initialization: initTimeout,
        cssInjection: 8000,
        recovery: 5000,
      },
      ...errorRecoveryConfig,
    });
  }, [maxRetries, fallbackCSS, enableDirectFallback, initTimeout, errorRecoveryConfig]);

  const dataConfig = useLedConfigStores((store) => store.dataConfig);

  if (!dataConfig) return null;

  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );

  // Enhanced scaling calculation with comprehensive error handling
  const calculateScaleAndPosition = useCallback(
    (containerWidth: number, containerHeight: number) => {
      return errorRecoveryUtils.safeScaleCalculation(
        () => {
          // Log scaling operation if debug logging is enabled
          if (enableDebugLogging) {
            logScalingOperation.calculation({ containerWidth, containerHeight, width, height }, null);
          }

          // Bounds checking for container dimensions
          if (containerWidth <= 0 || containerHeight <= 0 || !isFinite(containerWidth) || !isFinite(containerHeight)) {
            logScalingOperation.invalidDimensions({ containerWidth, containerHeight, type: 'container' });
            throw new Error('Invalid container dimensions');
          }

          // Bounds checking for content dimensions
          if (width <= 0 || height <= 0 || !isFinite(width) || !isFinite(height)) {
            logScalingOperation.invalidDimensions({ width, height, type: 'content' });
            throw new Error('Invalid content dimensions');
          }

          // Calculate scale ratios
          const scaleX = containerWidth / width;
          const scaleY = containerHeight / height;

          // Use the smaller scale to maintain aspect ratio
          let calculatedScale = Math.min(scaleX, scaleY);

          // Apply scale bounds (prevent too small or too large scaling)
          const minScale = 0.1; // Minimum 10% of original size
          const maxScale = 1.0; // Maximum 200% of original size

          if (calculatedScale < minScale || calculatedScale > maxScale) {
            logScalingOperation.boundsExceeded(calculatedScale, { min: minScale, max: maxScale });
          }

          calculatedScale = Math.max(minScale, Math.min(maxScale, calculatedScale));

          // Ensure scale is a valid number
          if (!isFinite(calculatedScale) || isNaN(calculatedScale)) {
            throw new Error('Invalid scale calculation result');
          }

          // Calculate centering translation
          const translateX = (containerWidth - width) / 2;
          const translateY = (containerHeight - height) / 2;

          const result = {
            scale: calculatedScale,
            translateX,
            translateY,
            isValid: true,
          };

          if (enableDebugLogging) {
            logScalingOperation.applied(result);
          }

          return result;
        },
        {
          scale: 1,
          translateX: 0,
          translateY: 0,
          isValid: false,
        },
        errorHandler,
      );
    },
    [width, height, enableDebugLogging, errorHandler],
  );

  // Enhanced error handling function with comprehensive logging
  const handleIframeError = useCallback(
    (error: Error, context?: any) => {
      if (enableDebugLogging) {
        logIframeOperation.error('Iframe error occurred', error, context);
      }

      const recoveryResult = errorHandler.handleInitializationError(error, iframeState.retryCount, context);

      setIframeState((prev) => ({
        ...prev,
        error,
        isLoading: recoveryResult.action === 'retry',
      }));

      if (onIframeError) {
        onIframeError(error);
      }

      // Handle recovery actions
      if (recoveryResult.action === 'retry' && recoveryResult.nextRetryDelay) {
        setTimeout(() => {
          retryIframeInitialization();
        }, recoveryResult.nextRetryDelay);
      }
    },
    [onIframeError, errorHandler, iframeState.retryCount, enableDebugLogging],
  );

  // Retry mechanism for iframe initialization
  const retryIframeInitialization = useCallback(() => {
    if (iframeState.retryCount >= maxRetries) {
      const error = new Error(`Iframe initialization failed after ${maxRetries} attempts`);
      handleIframeError(error);
      return;
    }

    setIframeState((prev) => ({
      ...prev,
      retryCount: prev.retryCount + 1,
      isLoading: true,
      error: null,
      isIframeReady: false,
      cssInjectionComplete: false,
    }));

    // Retry after a delay
    retryTimeoutRef.current = setTimeout(() => {
      // Force iframe reload by changing its key or re-mounting
      const iframe = iframeRef.current;
      if (iframe) {
        // Reset iframe state
        iframe.src = 'about:blank';
        setTimeout(() => {
          iframe.src = '';
        }, 100);
      }
    }, 1000 * Math.pow(2, iframeState.retryCount)); // Exponential backoff
  }, [iframeState.retryCount, maxRetries, handleIframeError]);

  // Create debounced resize handler with useCallback to prevent recreation on every render
  const debouncedResize = useCallback(
    debounce(() => {
      // Check if currently in fullscreen mode
      const isFullscreen = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement
      );

      // If in preview mode or fullscreen, do not perform any resize logic.
      if (isPreviewing || isFullscreen) {
        return;
      }
      const container = containerRef.current;
      if (!container) {
        // Component has unmounted, do nothing.
        return;
      }
      const iframe = iframeRef.current;
      if (!iframe || iframeState.error) return;

      const containerRect = container.getBoundingClientRect();
      const containerWidth = containerRect.width;
      const containerHeight = containerRect.height;

      // If container has no size, just ignore this resize event.
      // This can happen when the component is hidden (e.g., by an overlay or display: none).
      if (containerWidth === 0 || containerHeight === 0) {
        return;
      }

      // Calculate scale and position with bounds checking
      const {
        scale: newScale,
        translateX,
        translateY,
        isValid,
      } = calculateScaleAndPosition(containerWidth, containerHeight);

      // Only apply changes if calculation is valid
      if (!isValid) {
        console.warn('Invalid scale calculation, using fallback values');
        return;
      }

      // Set iframe dimensions first to prevent layout shifts
      iframe.style.width = `${width}px`;
      iframe.style.height = `${height}px`;

      // Set transform origin to center for proper scaling
      iframe.style.transformOrigin = 'center center';

      // Apply transform with accurate positioning
      // Use translate3d for better performance and to avoid layout shifts
      iframe.style.transform = `translate3d(${translateX}px, ${translateY}px, 0) scale(${newScale})`;

      // Ensure iframe is positioned absolutely to prevent affecting parent layout
      iframe.style.position = 'absolute';
      iframe.style.top = '0';
      iframe.style.left = '0';

      // Mark size as calculated only after first successful calculation
      if (!iframeState.isSizeCalculated) {
        setIframeState((prev) => ({ ...prev, isSizeCalculated: true }));
      }
    }, 150), // 150ms debounce delay for optimal performance
    [width, height, calculateScaleAndPosition, iframeState.isSizeCalculated, iframeState.error],
  );

  // Enhanced resize effect with error handling
  useEffect(() => {
    const container = containerRef.current;
    const iframe = iframeRef.current;

    // Check if currently in fullscreen mode
    const isFullscreen = !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement
    );

    // If in preview or fullscreen, we pause all resize observations.
    if (isPreviewing || isFullscreen || !iframe || !container || iframeState.error) {
      return;
    }

    // Initial resize calculation (immediate, not debounced)
    const initialResize = () => {
      try {
        const containerRect = container.getBoundingClientRect();
        const containerWidth = containerRect.width;
        const containerHeight = containerRect.height;

        const {
          scale: newScale,
          translateX,
          translateY,
          isValid,
        } = calculateScaleAndPosition(containerWidth, containerHeight);

        if (!isValid) {
          console.warn('Invalid scale calculation, using fallback values');
          return;
        }

        iframe.style.width = `${width}px`;
        iframe.style.height = `${height}px`;
        iframe.style.transformOrigin = 'center center';
        iframe.style.transform = `translate3d(${translateX}px, ${translateY}px, 0) scale(${newScale})`;
        iframe.style.position = 'absolute';
        iframe.style.top = '0';
        iframe.style.left = '0';

        if (!iframeState.isSizeCalculated) {
          setIframeState((prev) => ({ ...prev, isSizeCalculated: true }));
        }
      } catch (error) {
        console.error('Error in initial resize calculation:', error);
        handleIframeError(error as Error);
      }
    };

    // Perform initial resize calculation
    initialResize();

    // Set up ResizeObserver with debounced resize handler
    let observer: ResizeObserver | null = null;
    let windowResizeHandler: (() => void) | null = null;
    let fullscreenChangeHandler: (() => void) | null = null;

    try {
      observer = new ResizeObserver(() => {
        // Use debounced resize for subsequent resize events
        debouncedResize();
      });
      observer.observe(container);
    } catch (error) {
      console.error('Failed to create ResizeObserver:', error);
      // Fallback to window resize event with debouncing
      windowResizeHandler = () => {
        debouncedResize();
      };
      window.addEventListener('resize', windowResizeHandler);
    }

    // Add fullscreen change listener to trigger resize when exiting fullscreen
    fullscreenChangeHandler = () => {
      // Small delay to ensure fullscreen state has updated
      setTimeout(() => {
        debouncedResize();
      }, 100);
    };

    document.addEventListener('fullscreenchange', fullscreenChangeHandler);
    document.addEventListener('webkitfullscreenchange', fullscreenChangeHandler);
    document.addEventListener('mozfullscreenchange', fullscreenChangeHandler);
    document.addEventListener('MSFullscreenChange', fullscreenChangeHandler);

    // Cleanup function
    return () => {
      // Cancel any pending debounced calls
      debouncedResize.cancel();

      // Clean up ResizeObserver
      if (observer && container) {
        try {
          observer.unobserve(container);
          observer.disconnect();
        } catch (error) {
          console.error('Error cleaning up ResizeObserver:', error);
        }
      }

      // Clean up window resize event listener
      if (windowResizeHandler) {
        window.removeEventListener('resize', windowResizeHandler);
      }

      // Clean up fullscreen change event listeners
      if (fullscreenChangeHandler) {
        document.removeEventListener('fullscreenchange', fullscreenChangeHandler);
        document.removeEventListener('webkitfullscreenchange', fullscreenChangeHandler);
        document.removeEventListener('mozfullscreenchange', fullscreenChangeHandler);
        document.removeEventListener('MSFullscreenChange', fullscreenChangeHandler);
      }
    };
  }, [
    width,
    height,
    debouncedResize,
    calculateScaleAndPosition,
    iframeState.isSizeCalculated,
    iframeState.error,
    handleIframeError,
    isPreviewing, // Re-run this effect when preview mode changes
  ]);

  // Enhanced iframe loading effect with comprehensive error handling and timeout management
  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe || iframeState.error) return;

    // Clear any existing timeouts
    if (initTimeoutRef.current) {
      clearTimeout(initTimeoutRef.current);
    }
    if (cssInjectionTimeoutRef.current) {
      clearTimeout(cssInjectionTimeoutRef.current);
    }

    // Set up initialization timeout
    initTimeoutRef.current = setTimeout(() => {
      if (!iframeState.isIframeReady) {
        const timeoutError = new Error(`Iframe initialization timeout after ${initTimeout}ms`);
        console.error('Iframe initialization timeout');

        if (iframeState.retryCount < maxRetries) {
          retryIframeInitialization();
        } else {
          handleIframeError(timeoutError);
        }
      }
    }, initTimeout);

    const onLoad = async () => {
      try {
        const doc = iframe.contentDocument;
        if (!doc) {
          throw new Error('Unable to access iframe document');
        }

        // Clear initialization timeout since iframe loaded successfully
        if (initTimeoutRef.current) {
          clearTimeout(initTimeoutRef.current);
          initTimeoutRef.current = null;
        }

        // Set up CSS injection timeout
        cssInjectionTimeoutRef.current = setTimeout(() => {
          if (!iframeState.cssInjectionComplete) {
            console.warn('CSS injection timeout, proceeding with fallback');

            // Apply fallback CSS if provided
            if (fallbackCSS) {
              const fallbackStyleElement = doc.createElement('style');
              fallbackStyleElement.id = 'iframe-fallback-styles';
              fallbackStyleElement.textContent = fallbackCSS;
              doc.head.appendChild(fallbackStyleElement);
            }

            // Mark as complete to prevent infinite waiting
            setIframeState((prev) => ({
              ...prev,
              cssInjectionComplete: true,
              isIframeReady: true,
              isLoading: false,
            }));

            if (onIframeReady) {
              setTimeout(onIframeReady, 100);
            }
          }
        }, 8000); // 8 second timeout for CSS injection

        // Configure CSS injection with proper settings
        const cssConfig: CSSInjectionConfig = {
          timeout: 5000,
          retryAttempts: 2, // Reduced retries for faster fallback
          retryDelay: 500,
          includeInlineStyles: true,
          includeCSSVariables: true,
          preserveOrder: true,
        };

        // Inject all CSS using the comprehensive system
        const injectionResult = await injectAllCSS(doc, cssConfig);

        // Clear CSS injection timeout since injection completed
        if (cssInjectionTimeoutRef.current) {
          clearTimeout(cssInjectionTimeoutRef.current);
          cssInjectionTimeoutRef.current = null;
        }

        if (!injectionResult.success) {
          const error = new Error(`CSS injection failed: ${injectionResult.errors.map((e) => e.message).join(', ')}`);
          console.warn('CSS injection completed with errors:', injectionResult.errors);

          // Call error callback if provided
          if (onCSSError) {
            onCSSError(error);
          }

          // Apply fallback CSS if provided
          if (fallbackCSS) {
            const fallbackStyleElement = doc.createElement('style');
            fallbackStyleElement.id = 'iframe-fallback-styles';
            fallbackStyleElement.textContent = fallbackCSS;
            doc.head.appendChild(fallbackStyleElement);
          }
        }

        // Add iframe-specific styles
        const iframeStyles = createIframeSpecificStyles({
          background: defaultConfig.slideConfig?.background,
          color: defaultConfig.slideConfig?.color,
          borderColor: defaultConfig.slideConfig?.borderColor,
        });

        const iframeStyleElement = doc.createElement('style');
        iframeStyleElement.id = 'iframe-specific-styles';
        iframeStyleElement.textContent = iframeStyles;
        doc.head.appendChild(iframeStyleElement);

        // Update state to indicate successful initialization
        setIframeState((prev) => ({
          ...prev,
          cssInjectionComplete: true,
          isIframeReady: true,
          isLoading: false,
          error: null,
        }));

        // Call callback if provided
        if (onIframeReady) {
          setTimeout(onIframeReady, 100);
        }

        console.log('CSS injection completed:', {
          success: injectionResult.success,
          injected: injectionResult.injectedCount,
          failed: injectionResult.failedCount,
          errors: injectionResult.errors.length,
          retryCount: iframeState.retryCount,
        });
      } catch (error) {
        console.error('Failed to initialize iframe:', error);

        // Clear timeouts
        if (initTimeoutRef.current) {
          clearTimeout(initTimeoutRef.current);
          initTimeoutRef.current = null;
        }
        if (cssInjectionTimeoutRef.current) {
          clearTimeout(cssInjectionTimeoutRef.current);
          cssInjectionTimeoutRef.current = null;
        }

        // Attempt retry if within retry limit
        if (iframeState.retryCount < maxRetries) {
          console.log(`Retrying iframe initialization (attempt ${iframeState.retryCount + 1}/${maxRetries})`);
          retryIframeInitialization();
        } else {
          // Final fallback - mark as ready with error state
          handleIframeError(error as Error);

          // Still try to apply fallback CSS if available
          const doc = iframe.contentDocument;
          if (doc && fallbackCSS) {
            try {
              const fallbackStyleElement = doc.createElement('style');
              fallbackStyleElement.id = 'iframe-fallback-styles';
              fallbackStyleElement.textContent = fallbackCSS;
              doc.head.appendChild(fallbackStyleElement);
            } catch (fallbackError) {
              console.error('Failed to apply fallback CSS:', fallbackError);
            }
          }
        }
      }
    };

    const onError = (event: Event) => {
      console.error('Iframe load error:', event);

      // Clear timeouts
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current);
        initTimeoutRef.current = null;
      }
      if (cssInjectionTimeoutRef.current) {
        clearTimeout(cssInjectionTimeoutRef.current);
        cssInjectionTimeoutRef.current = null;
      }

      const loadError = new Error('Iframe failed to load');

      if (iframeState.retryCount < maxRetries) {
        retryIframeInitialization();
      } else {
        handleIframeError(loadError);
      }
    };

    iframe.addEventListener('load', onLoad);
    iframe.addEventListener('error', onError);

    return () => {
      iframe.removeEventListener('load', onLoad);
      iframe.removeEventListener('error', onError);

      // Clear all timeouts on cleanup
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current);
        initTimeoutRef.current = null;
      }
      if (cssInjectionTimeoutRef.current) {
        clearTimeout(cssInjectionTimeoutRef.current);
        cssInjectionTimeoutRef.current = null;
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }
    };
  }, [
    defaultConfig.slideConfig,
    onIframeReady,
    onCSSError,
    fallbackCSS,
    iframeState.error,
    iframeState.retryCount,
    iframeState.isIframeReady,
    iframeState.cssInjectionComplete,
    maxRetries,
    initTimeout,
    handleIframeError,
    retryIframeInitialization,
  ]);

  // Enhanced retry function with comprehensive error handling
  const handleRetry = useCallback(() => {
    if (enableDebugLogging) {
      logIframeOperation.retry(iframeState.retryCount + 1, maxRetries, 'User initiated retry');
    }

    setIframeState({
      isLoading: true,
      isIframeReady: false,
      isSizeCalculated: false,
      error: null,
      retryCount: 0,
      cssInjectionComplete: false,
    });
  }, [iframeState.retryCount, maxRetries, enableDebugLogging]);

  // Enhanced reset function
  const handleReset = useCallback(() => {
    if (enableDebugLogging) {
      logIframeOperation.initialization('User initiated reset');
    }

    // Clear error handler history
    errorHandler.clearErrorHistory();

    setIframeState({
      isLoading: true,
      isIframeReady: false,
      isSizeCalculated: false,
      error: null,
      retryCount: 0,
      cssInjectionComplete: false,
    });
  }, [errorHandler, enableDebugLogging]);

  // Determine if we should show direct fallback
  const shouldShowDirectFallback = iframeState.error && enableDirectFallback && iframeState.retryCount >= maxRetries;

  return (
    <IframeErrorBoundary onError={handleIframeError}>
      <div ref={containerRef} className="relative w-full h-full">
        {/* Use comprehensive fallback system */}
        {(iframeState.isLoading || iframeState.error || shouldShowDirectFallback) && (
          <ComprehensiveFallback
            error={iframeState.error}
            retryCount={iframeState.retryCount}
            maxRetries={maxRetries}
            isLoading={iframeState.isLoading && !iframeState.error}
            isTimeout={iframeState.error?.message.includes('timeout')}
            isCSSError={iframeState.error?.message.includes('CSS')}
            operation="iframe initialization"
            timeout={initTimeout}
            onRetry={handleRetry}
            onReset={handleReset}
            onContinue={() => {
              // For CSS errors, continue with current state
              setIframeState((prev) => ({ ...prev, error: null, isLoading: false }));
            }}
            width={shouldShowDirectFallback ? width : undefined}
            height={shouldShowDirectFallback ? height : undefined}
            className="absolute inset-0 z-10"
          >
            {shouldShowDirectFallback ? children : undefined}
          </ComprehensiveFallback>
        )}

        {/* Main iframe content */}
        <Frame ref={iframeRef} className="h-full w-full relative">
          {/* Only render children when iframe is fully ready */}
          {!iframeState.isLoading &&
            iframeState.isIframeReady &&
            iframeState.isSizeCalculated &&
            iframeState.cssInjectionComplete &&
            !iframeState.error && (
              <div {...props} className="h-full w-full">
                {children}
              </div>
            )}
        </Frame>

        {/* Debug information (only in development) */}
        {enableDebugLogging && process.env.NODE_ENV === 'development' && (
          <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs p-2 rounded max-w-xs">
            <div>State: {iframeState.isLoading ? 'Loading' : iframeState.error ? 'Error' : 'Ready'}</div>
            <div>
              Retries: {iframeState.retryCount}/{maxRetries}
            </div>
            <div>CSS: {iframeState.cssInjectionComplete ? 'Complete' : 'Pending'}</div>
            {iframeState.error && <div>Error: {iframeState.error.message.substring(0, 50)}...</div>}
          </div>
        )}
      </div>
    </IframeErrorBoundary>
  );
}
