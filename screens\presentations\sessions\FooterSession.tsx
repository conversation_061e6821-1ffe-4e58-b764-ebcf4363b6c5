import { DATETIME_FORMAT, footerType } from '@/constants';
import { ResponsiveTextWithOverride } from '../components/ResponsiveTextWithOverride';
import { SlideConfigDto } from '@/types/slide';
import { FormattedMessage, useIntl } from 'react-intl';
import { useLedConfigStores, useLedStores } from '@/stores';
import { useMemo } from 'react';
import { cn } from '@/utils/tailwind';
import { hexToRgba } from '@/utils';
import dayjs from 'dayjs';

type FooterProps = {
  slide: SlideConfigDto;
  mode?: 'config' | 'preview';
};

export default function FooterSession({ slide, mode = 'config' }: FooterProps) {
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const dataLedDetail = useLedStores((store) => store.dataLedDetail);
  if (!dataConfig || !dataLedDetail) return null;

  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );
  return (
    <div
      className={cn(
        'w-full flex-shrink-0 text-current flex items-center justify-center',
        'p-1 sm:py-1 sm:px-[6px] md:py-[6px] md:px-2 lg:py-2 lg:px-[10px] xl:py-[10px] xl:px-3 2xl:py-3 2xl:px-4 3xl:py-4 3xl:px-5',
        'mobile-short-height:hidden',
      )}
      style={{
        backgroundColor: slide.footer.config.background as string,
      }}
    >
      <ResponsiveTextWithOverride
        className="w-full h-full flex items-center justify-center"
        value={slide.footer.fontSize.config.content}
      >
        {slide.footer.type === footerType.bannerText ? (
          slide.footer.content?.[locale]?.length ? (
            <span
              className="font-semibold truncate text-[100%] block"
              style={{
                color: slide.footer.config.color,
              }}
            >
              {slide.footer.content?.[locale]}
            </span>
          ) : (
            <span
              className={cn('font-medium text-center text-[100%]', { invisible: mode === 'preview' })}
              style={{
                color: hexToRgba(slide.footer.config.color, 0.7),
              }}
            >
              <FormattedMessage
                defaultMessage="Chưa cấu hình nội dung"
                id="screens.presentations.ContentSlider.1788853769"
              />
            </span>
          )
        ) : (
          <span
            className="font-semibold text-center text-[100%]"
            style={{
              color: slide.footer.config.color,
            }}
          >
            {dayjs().format(DATETIME_FORMAT)}
          </span>
        )}
      </ResponsiveTextWithOverride>
    </div>
  );
}
