import { ISubSlide, SlideConfigDto } from '@/types/slide';
import { getVisibleSlides } from '@/utils/slideFilter';
import {
  calculateNextPosition,
  calculatePrevPosition,
  getCurrentNavigationStep,
  getTotalNavigationSteps,
  isValidPosition,
} from '@/utils/subSlideNavigation';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface ISlideWithSubSlides extends SlideConfigDto {
  subSlides?: ISubSlide[];
}

type PresentationState = {
  // Slide data
  slides: ISlideWithSubSlides[];
  visibleSlides: ISlideWithSubSlides[];

  // Navigation state
  currentSlideIndex: number;
  currentSubSlideIndex: number;
  totalSlides: number;
  totalSubSlides: number;
  totalNavigationSteps: number;
  currentNavigationStep: number;

  // Auto-play state
  isAutoPlaying: boolean;
  autoPlayInterval: number;

  // Presentation state
  isPresenting: boolean;

  // State persistence
  lastPresentationId?: string;
  presentationStartTime?: number;
  presentationDuration: number;
};

type PresentationActions = {
  // Slide management
  setSlides: (slides: ISlideWithSubSlides[]) => void;
  setVisibleSlides: (slides: ISlideWithSubSlides[]) => void;

  // Enhanced navigation actions
  setCurrentSlideIndex: (index: number) => void;
  setCurrentSubSlideIndex: (index: number) => void;
  nextSlide: () => boolean; // Returns true if navigation was successful
  prevSlide: () => boolean; // Returns true if navigation was successful
  goToSlide: (slideIndex: number, subSlideIndex?: number) => boolean; // Returns true if navigation was successful
  goToFirstSlide: () => void;
  goToLastSlide: () => void;
  goToNextSlideSkipSubSlides: () => boolean; // Skip to next main slide, ignoring sub-slides
  goToPrevSlideSkipSubSlides: () => boolean; // Skip to prev main slide, ignoring sub-slides

  // Sub-slide specific navigation
  nextSubSlide: () => boolean; // Navigate only within current slide's sub-slides
  prevSubSlide: () => boolean; // Navigate only within current slide's sub-slides
  goToSubSlide: (subSlideIndex: number) => boolean; // Navigate to specific sub-slide in current slide

  // Auto-play actions
  setAutoPlaying: (isPlaying: boolean) => void;
  toggleAutoPlay: () => void;
  setAutoPlayInterval: (interval: number) => void;

  // Presentation actions
  startPresentation: (slides: ISlideWithSubSlides[], presentationId?: string) => void;
  stopPresentation: () => void;
  pausePresentation: () => void;
  resumePresentation: () => void;

  // State management
  updateNavigationCounts: () => void;
  persistPresentationState: () => void;

  // Navigation state queries
  canGoNext: () => boolean;
  canGoPrev: () => boolean;
  canGoToSlide: (slideIndex: number, subSlideIndex?: number) => boolean;
  getCurrentSlide: () => ISlideWithSubSlides | null;
  getCurrentSubSlide: () => ISubSlide | null;

  // Utility actions
  reset: () => void;
};

// Helper functions for calculating slide and sub-slide counts
const calculateTotalSubSlides = (visibleSlides: ISlideWithSubSlides[]): number => {
  return visibleSlides.reduce((total, slide) => {
    const visibleSubSlides = slide.subSlides?.filter((subSlide) => subSlide.show) || [];
    return total + visibleSubSlides.length;
  }, 0);
};

const updateNavigationState = (
  currentSlideIndex: number,
  currentSubSlideIndex: number,
  visibleSlides: ISlideWithSubSlides[],
) => {
  const totalSlides = visibleSlides.length;
  const totalSubSlides = calculateTotalSubSlides(visibleSlides);
  const totalNavigationSteps = getTotalNavigationSteps(visibleSlides);
  const currentNavigationStep = getCurrentNavigationStep(currentSlideIndex, currentSubSlideIndex, visibleSlides);

  return {
    totalSlides,
    totalSubSlides,
    totalNavigationSteps,
    currentNavigationStep,
  };
};

const initialState: PresentationState = {
  slides: [],
  visibleSlides: [],
  currentSlideIndex: 0,
  currentSubSlideIndex: 0,
  totalSlides: 0,
  totalSubSlides: 0,
  totalNavigationSteps: 0,
  currentNavigationStep: 0,
  isAutoPlaying: true,
  autoPlayInterval: 3000, // 3 seconds default
  isPresenting: true,
  presentationDuration: 0,
};

const usePresentationStores = create<PresentationState & PresentationActions>()(
  persist(
    (set, get) =>
      ({
        ...initialState,

        // Slide management
        setSlides: (slides: ISlideWithSubSlides[]) => {
          const visibleSlides = getVisibleSlides(slides);
          const { currentSlideIndex, currentSubSlideIndex } = get();
          const navigationState = updateNavigationState(currentSlideIndex, currentSubSlideIndex, visibleSlides);

          set({
            slides,
            visibleSlides,
            ...navigationState,
          });
        },

        setVisibleSlides: (visibleSlides: ISlideWithSubSlides[]) => {
          const { currentSlideIndex, currentSubSlideIndex } = get();
          const navigationState = updateNavigationState(currentSlideIndex, currentSubSlideIndex, visibleSlides);

          set({
            visibleSlides,
            ...navigationState,
          });
        },

        // Navigation actions
        setCurrentSlideIndex: (index: number) => {
          const { visibleSlides, currentSubSlideIndex } = get();
          if (index >= 0 && index < visibleSlides.length) {
            const navigationState = updateNavigationState(index, currentSubSlideIndex, visibleSlides);
            set({
              currentSlideIndex: index,
              ...navigationState,
            });
          }
        },

        setCurrentSubSlideIndex: (index: number) => {
          const { visibleSlides, currentSlideIndex } = get();
          const navigationState = updateNavigationState(currentSlideIndex, index, visibleSlides);
          set({
            currentSubSlideIndex: index,
            ...navigationState,
          });
        },

        nextSlide: () => {
          const { visibleSlides, currentSlideIndex, currentSubSlideIndex } = get();

          // Handle empty slides gracefully
          if (!visibleSlides || visibleSlides.length === 0) {
            console.warn('Cannot navigate: No visible slides available');
            return false;
          }

          // Validate current position before navigation
          if (currentSlideIndex < 0 || currentSlideIndex >= visibleSlides.length) {
            console.error('Invalid current slide index:', currentSlideIndex);
            // Try to recover by going to first slide
            const navigationState = updateNavigationState(0, 0, visibleSlides);
            set({
              currentSlideIndex: 0,
              currentSubSlideIndex: 0,
              ...navigationState,
            });
            return true;
          }

          const navigationState = {
            currentSlideIndex,
            currentSubSlideIndex,
            visibleSlides,
          };

          try {
            const result = calculateNextPosition(navigationState);

            if (result.hasChanged) {
              // Validate the new position before setting it
              if (result.slideIndex >= 0 && result.slideIndex < visibleSlides.length) {
                const updatedNavigationState = updateNavigationState(
                  result.slideIndex,
                  result.subSlideIndex,
                  visibleSlides,
                );
                set({
                  currentSlideIndex: result.slideIndex,
                  currentSubSlideIndex: result.subSlideIndex,
                  ...updatedNavigationState,
                });
                return true;
              } else {
                console.error('Invalid navigation result:', result);
                return false;
              }
            }
          } catch (error) {
            console.error('Error during next slide navigation:', error);
            // Try to recover by staying at current position
            const updatedNavigationState = updateNavigationState(
              currentSlideIndex,
              currentSubSlideIndex,
              visibleSlides,
            );
            set(updatedNavigationState);
            return false;
          }

          // If no change, loop back to the first slide/sub-slide
          const updatedNavigationState = updateNavigationState(0, 0, visibleSlides);
          set({
            currentSlideIndex: 0,
            currentSubSlideIndex: 0,
            ...updatedNavigationState,
          });
          return true; // Indicate that a "loop" navigation occurred
        },

        prevSlide: () => {
          const { visibleSlides, currentSlideIndex, currentSubSlideIndex } = get();

          // Handle empty slides gracefully
          if (!visibleSlides || visibleSlides.length === 0) {
            console.warn('Cannot navigate: No visible slides available');
            return false;
          }

          // Validate current position before navigation
          if (currentSlideIndex < 0 || currentSlideIndex >= visibleSlides.length) {
            console.error('Invalid current slide index:', currentSlideIndex);
            // Try to recover by going to last slide
            const lastSlideIndex = visibleSlides.length - 1;
            const lastSlide = visibleSlides[lastSlideIndex];
            const visibleSubSlides = lastSlide.subSlides?.filter((subSlide: ISubSlide) => subSlide.show) || [];
            const lastSubSlideIndex = Math.max(0, visibleSubSlides.length - 1);

            const navigationState = updateNavigationState(lastSlideIndex, lastSubSlideIndex, visibleSlides);
            set({
              currentSlideIndex: lastSlideIndex,
              currentSubSlideIndex: lastSubSlideIndex,
              ...navigationState,
            });
            return true;
          }

          const navigationState = {
            currentSlideIndex,
            currentSubSlideIndex,
            visibleSlides,
          };

          try {
            const result = calculatePrevPosition(navigationState);

            if (result.hasChanged) {
              // Validate the new position before setting it
              if (result.slideIndex >= 0 && result.slideIndex < visibleSlides.length) {
                const updatedNavigationState = updateNavigationState(
                  result.slideIndex,
                  result.subSlideIndex,
                  visibleSlides,
                );
                set({
                  currentSlideIndex: result.slideIndex,
                  currentSubSlideIndex: result.subSlideIndex,
                  ...updatedNavigationState,
                });
                return true;
              } else {
                console.error('Invalid navigation result:', result);
                return false;
              }
            }
          } catch (error) {
            console.error('Error during previous slide navigation:', error);
            // Try to recover by staying at current position
            const updatedNavigationState = updateNavigationState(
              currentSlideIndex,
              currentSubSlideIndex,
              visibleSlides,
            );
            set(updatedNavigationState);
            return false;
          }

          return false;
        },

        goToSlide: (slideIndex: number, subSlideIndex: number = 0) => {
          const { visibleSlides } = get();

          if (isValidPosition(slideIndex, subSlideIndex, visibleSlides)) {
            const navigationState = updateNavigationState(slideIndex, subSlideIndex, visibleSlides);
            set({
              currentSlideIndex: slideIndex,
              currentSubSlideIndex: subSlideIndex,
              ...navigationState,
            });
            return true;
          } else if (slideIndex >= 0 && slideIndex < visibleSlides.length) {
            // If sub-slide index is invalid, go to first valid sub-slide of the target slide
            const targetSlide = visibleSlides[slideIndex];
            const visibleSubSlides = targetSlide.subSlides?.filter((subSlide: ISubSlide) => subSlide.show) || [];
            const validSubSlideIndex = visibleSubSlides.length > 0 ? 0 : 0;

            const navigationState = updateNavigationState(slideIndex, validSubSlideIndex, visibleSlides);
            set({
              currentSlideIndex: slideIndex,
              currentSubSlideIndex: validSubSlideIndex,
              ...navigationState,
            });
            return true;
          }
          return false;
        },

        goToFirstSlide: () => {
          const { visibleSlides } = get();
          if (visibleSlides.length > 0) {
            const navigationState = updateNavigationState(0, 0, visibleSlides);
            set({
              currentSlideIndex: 0,
              currentSubSlideIndex: 0,
              ...navigationState,
            });
          }
        },

        goToLastSlide: () => {
          const { visibleSlides } = get();
          if (visibleSlides.length > 0) {
            const lastSlideIndex = visibleSlides.length - 1;
            const lastSlide = visibleSlides[lastSlideIndex];
            const visibleSubSlides = lastSlide.subSlides?.filter((subSlide: ISubSlide) => subSlide.show) || [];
            const lastSubSlideIndex = Math.max(0, visibleSubSlides.length - 1);

            const navigationState = updateNavigationState(lastSlideIndex, lastSubSlideIndex, visibleSlides);
            set({
              currentSlideIndex: lastSlideIndex,
              currentSubSlideIndex: lastSubSlideIndex,
              ...navigationState,
            });
          }
        },

        goToNextSlideSkipSubSlides: () => {
          const { visibleSlides, currentSlideIndex } = get();

          if (visibleSlides.length === 0) return false;

          const nextSlideIndex = currentSlideIndex < visibleSlides.length - 1 ? currentSlideIndex + 1 : 0;
          const navigationState = updateNavigationState(nextSlideIndex, 0, visibleSlides);

          set({
            currentSlideIndex: nextSlideIndex,
            currentSubSlideIndex: 0,
            ...navigationState,
          });

          return nextSlideIndex !== currentSlideIndex;
        },

        goToPrevSlideSkipSubSlides: () => {
          const { visibleSlides, currentSlideIndex } = get();

          if (visibleSlides.length === 0) return false;

          const prevSlideIndex = currentSlideIndex > 0 ? currentSlideIndex - 1 : visibleSlides.length - 1;
          const navigationState = updateNavigationState(prevSlideIndex, 0, visibleSlides);

          set({
            currentSlideIndex: prevSlideIndex,
            currentSubSlideIndex: 0,
            ...navigationState,
          });

          return prevSlideIndex !== currentSlideIndex;
        },

        // Sub-slide specific navigation
        nextSubSlide: () => {
          const { visibleSlides, currentSlideIndex, currentSubSlideIndex } = get();

          if (visibleSlides.length === 0 || currentSlideIndex >= visibleSlides.length) return false;

          const currentSlide = visibleSlides[currentSlideIndex];
          const visibleSubSlides = currentSlide.subSlides?.filter((subSlide: ISubSlide) => subSlide.show) || [];

          if (visibleSubSlides.length === 0) return false;

          if (currentSubSlideIndex < visibleSubSlides.length - 1) {
            const newSubSlideIndex = currentSubSlideIndex + 1;
            const navigationState = updateNavigationState(currentSlideIndex, newSubSlideIndex, visibleSlides);

            set({
              currentSubSlideIndex: newSubSlideIndex,
              ...navigationState,
            });
            return true;
          }

          return false;
        },

        prevSubSlide: () => {
          const { visibleSlides, currentSlideIndex, currentSubSlideIndex } = get();

          if (visibleSlides.length === 0 || currentSlideIndex >= visibleSlides.length) return false;

          if (currentSubSlideIndex > 0) {
            const newSubSlideIndex = currentSubSlideIndex - 1;
            const navigationState = updateNavigationState(currentSlideIndex, newSubSlideIndex, visibleSlides);

            set({
              currentSubSlideIndex: newSubSlideIndex,
              ...navigationState,
            });
            return true;
          }

          return false;
        },

        goToSubSlide: (subSlideIndex: number) => {
          const { visibleSlides, currentSlideIndex } = get();

          if (visibleSlides.length === 0 || currentSlideIndex >= visibleSlides.length) return false;

          const currentSlide = visibleSlides[currentSlideIndex];
          const visibleSubSlides = currentSlide.subSlides?.filter((subSlide: ISubSlide) => subSlide.show) || [];

          if (subSlideIndex >= 0 && subSlideIndex < visibleSubSlides.length) {
            const navigationState = updateNavigationState(currentSlideIndex, subSlideIndex, visibleSlides);

            set({
              currentSubSlideIndex: subSlideIndex,
              ...navigationState,
            });
            return true;
          }

          return false;
        },

        // Auto-play actions
        setAutoPlaying: (isPlaying: boolean) => {
          set({ isAutoPlaying: isPlaying });
        },

        toggleAutoPlay: () => {
          set((state: PresentationState) => ({ isAutoPlaying: !state.isAutoPlaying }));
        },

        setAutoPlayInterval: (interval: number) => {
          if (interval > 0) {
            set({ autoPlayInterval: interval });
          }
        },

        // Presentation actions
        startPresentation: (slides: ISlideWithSubSlides[], presentationId?: string) => {
          // Handle empty or invalid slides
          if (!slides || slides.length === 0) {
            console.error('Cannot start presentation: No slides provided');
            return;
          }

          const visibleSlides = getVisibleSlides(slides);

          // Handle case where no visible slides exist
          if (visibleSlides.length === 0) {
            console.warn('Cannot start presentation: No visible slides found');
            return;
          }

          const navigationState = updateNavigationState(0, 0, visibleSlides);
          const startTime = Date.now();

          set({
            slides,
            visibleSlides,
            currentSlideIndex: 0,
            currentSubSlideIndex: 0,
            isPresenting: true,
            isAutoPlaying: true, // Start auto-playing by default
            lastPresentationId: presentationId,
            presentationStartTime: startTime,
            presentationDuration: 0,
            ...navigationState,
          });
        },

        stopPresentation: () => {
          const { presentationStartTime } = get();
          const duration = presentationStartTime ? Date.now() - presentationStartTime : 0;

          set({
            isPresenting: false,
            isAutoPlaying: false,
            currentSlideIndex: 0,
            currentSubSlideIndex: 0,
            presentationDuration: duration,
            presentationStartTime: undefined,
          });
        },

        pausePresentation: () => {
          set({ isAutoPlaying: false });
        },

        resumePresentation: () => {
          set({ isAutoPlaying: true });
        },

        // State management
        updateNavigationCounts: () => {
          const { currentSlideIndex, currentSubSlideIndex, visibleSlides } = get();
          const navigationState = updateNavigationState(currentSlideIndex, currentSubSlideIndex, visibleSlides);
          set(navigationState);
        },

        persistPresentationState: () => {
          const { presentationStartTime } = get();
          if (presentationStartTime) {
            const duration = Date.now() - presentationStartTime;
            set({ presentationDuration: duration });
          }
        },

        // Navigation state queries
        canGoNext: () => {
          const { visibleSlides, currentSlideIndex, currentSubSlideIndex } = get();

          if (!visibleSlides || visibleSlides.length === 0) return false;

          try {
            const navigationState = {
              currentSlideIndex,
              currentSubSlideIndex,
              visibleSlides,
            };

            const result = calculateNextPosition(navigationState);
            return result.hasChanged;
          } catch (error) {
            console.error('Error checking if can go next:', error);
            return false;
          }
        },

        canGoPrev: () => {
          const { visibleSlides, currentSlideIndex, currentSubSlideIndex } = get();

          if (!visibleSlides || visibleSlides.length === 0) return false;

          try {
            const navigationState = {
              currentSlideIndex,
              currentSubSlideIndex,
              visibleSlides,
            };

            const result = calculatePrevPosition(navigationState);
            return result.hasChanged;
          } catch (error) {
            console.error('Error checking if can go prev:', error);
            return false;
          }
        },

        canGoToSlide: (slideIndex: number, subSlideIndex: number = 0) => {
          const { visibleSlides } = get();
          return isValidPosition(slideIndex, subSlideIndex, visibleSlides);
        },

        getCurrentSlide: () => {
          const { visibleSlides, currentSlideIndex } = get();

          if (
            !visibleSlides ||
            visibleSlides.length === 0 ||
            currentSlideIndex < 0 ||
            currentSlideIndex >= visibleSlides.length
          ) {
            return null;
          }

          return visibleSlides[currentSlideIndex];
        },

        getCurrentSubSlide: () => {
          const { visibleSlides, currentSlideIndex, currentSubSlideIndex } = get();

          if (
            !visibleSlides ||
            visibleSlides.length === 0 ||
            currentSlideIndex < 0 ||
            currentSlideIndex >= visibleSlides.length
          ) {
            return null;
          }

          const currentSlide = visibleSlides[currentSlideIndex];
          if (!currentSlide || !currentSlide.subSlides) {
            return null;
          }

          const visibleSubSlides = currentSlide.subSlides.filter((subSlide: ISubSlide) => subSlide.show) || [];

          if (
            visibleSubSlides.length === 0 ||
            currentSubSlideIndex < 0 ||
            currentSubSlideIndex >= visibleSubSlides.length
          ) {
            return null;
          }

          return visibleSubSlides[currentSubSlideIndex];
        },

        // Utility actions
        reset: () => {
          set(initialState);
        },
      } as PresentationState & PresentationActions),
    {
      name: 'presentation-storage',
      partialize: (state) => ({
        // Only persist essential state for recovery
        lastPresentationId: state.lastPresentationId,
        currentSlideIndex: state.currentSlideIndex,
        currentSubSlideIndex: state.currentSubSlideIndex,
        autoPlayInterval: state.autoPlayInterval,
        presentationDuration: state.presentationDuration,
      }),
    },
  ),
);

export { usePresentationStores };
export type { ISlideWithSubSlides };

