import GroupBox from '@/components/sessions/GroupBox';
import { NumberInput } from '@/components/ui/NumberInput';
import { Fragment, useEffect, useMemo } from 'react';
import { FormattedMessage } from 'react-intl';
import { Form, FormInstance, Switch } from 'ui-components';
import { HistoriesContent } from '@/types/slide';

type PropsOptions = {
  focusedIndex: number;
  form: FormInstance;
  dataContent: {
    metadata: HistoriesContent;
  };
  initFont: {
    table: {
      [x: string]: number;
    };
  };
};

export default function HistoriesOptions({ focusedIndex, form, dataContent, initFont }: PropsOptions) {
  const configFontSize = useMemo(() => {
    return dataContent?.metadata?.fontSize?.config;
  }, [dataContent]);

  const isAuto = useMemo(() => dataContent?.metadata?.fontSize?.auto, [dataContent]);

  useEffect(() => {
    if (isAuto) {
      return form.setFieldValue(
        ['presentations', focusedIndex, 'content', 'metadata', 'fontSize', 'config'],
        initFont.table,
      );
    }
  }, [isAuto, initFont, focusedIndex, form]);

  return (
    <Fragment>
      <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
        <span className="font-semibold text-base text-gray-700">
          <FormattedMessage defaultMessage="Cấu hình hiển thị" id="screens.presentations.ConfigSlide.206334904" />
        </span>
        <div className="flex flex-col gap-2">
          <span className="font-medium text-sm text-gray-700">
            <FormattedMessage
              defaultMessage="Hiển thị thành phần"
              id="screens.presentations.ContentConfigSlide.11543690"
            />
          </span>
          <div className="h-full w-full">
            <GroupBox>
              <div className="flex flex-row gap-3 p-3 items-center">
                <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Tên trạm quan trắc"
                    id="screens.presentations.ContentConfigSlide.1036745043"
                  />
                </span>
                <Form.Field
                  name={[
                    'presentations',
                    focusedIndex,
                    'content',
                    'metadata',
                    'config',
                    'showOptions',
                    'isShowStationName',
                  ]}
                  trigger="onCheckedChange"
                  valuePropName="checked"
                >
                  <Switch className="flex-shrink-0" />
                </Form.Field>
              </div>
              <div className="flex flex-row gap-3 p-3 items-center">
                <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Đơn vị thông số"
                    id="screens.presentations.compoType.HistoriesOptions.2020945267"
                  />
                </span>
                <Form.Field
                  name={['presentations', focusedIndex, 'content', 'metadata', 'config', 'showOptions', 'isShowUnit']}
                  trigger="onCheckedChange"
                  valuePropName="checked"
                >
                  <Switch className="flex-shrink-0" />
                </Form.Field>
              </div>
              <div className="flex flex-row gap-3 p-3 items-center">
                <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Cột đánh giá"
                    id="screens.presentations.compoType.HistoriesOptions.1192966251"
                  />
                </span>
                <Form.Field
                  name={[
                    'presentations',
                    focusedIndex,
                    'content',
                    'metadata',
                    'config',
                    'showOptions',
                    'isShowColumnEvaluate',
                  ]}
                  trigger="onCheckedChange"
                  valuePropName="checked"
                >
                  <Switch className="flex-shrink-0" />
                </Form.Field>
              </div>
              <div className="flex flex-row gap-3 p-3 items-center">
                <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Ngưỡng giới hạn"
                    id="screens.presentations.compoType.HistoriesOptions.1242488578"
                  />
                </span>
                <Form.Field
                  name={[
                    'presentations',
                    focusedIndex,
                    'content',
                    'metadata',
                    'config',
                    'showOptions',
                    'isShowLimitThreshold',
                  ]}
                  trigger="onCheckedChange"
                  valuePropName="checked"
                >
                  <Switch className="flex-shrink-0" />
                </Form.Field>
              </div>
            </GroupBox>
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <span className="font-medium text-sm text-gray-700">
            <FormattedMessage
              defaultMessage="Hiển thị màu cảnh báo giá trị"
              id="screens.presentations.ContentConfigSlide.1298622127"
            />
          </span>
          <div className="h-full w-full">
            <GroupBox>
              <div className="flex flex-row gap-3 p-3 items-center">
                <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Cảnh báo chuẩn bị vượt"
                    id="screens.presentations.ContentConfigSlide.355114431"
                  />
                </span>
                <Form.Field
                  name={[
                    'presentations',
                    focusedIndex,
                    'content',
                    'metadata',
                    'config',
                    'warningColor',
                    'isThresholdApproaching',
                  ]}
                  trigger="onCheckedChange"
                  valuePropName="checked"
                >
                  <Switch className="flex-shrink-0" />
                </Form.Field>
              </div>
              <div className="flex flex-row gap-3 p-3 items-center">
                <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Cảnh báo vượt ngưỡng"
                    id="screens.presentations.ContentConfigSlide.261939202"
                  />
                </span>
                <Form.Field
                  name={[
                    'presentations',
                    focusedIndex,
                    'content',
                    'metadata',
                    'config',
                    'warningColor',
                    'isThresholdExceeded',
                  ]}
                  trigger="onCheckedChange"
                  valuePropName="checked"
                >
                  <Switch className="flex-shrink-0" />
                </Form.Field>
              </div>
            </GroupBox>
          </div>
        </div>
      </div>

      <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'fontSize', 'auto']}>
        {(control) => {
          return (
            <div className="w-full p-4 flex flex-col gap-3 bg-white rounded-xl">
              <span className="font-semibold text-base text-gray-700">
                <FormattedMessage
                  defaultMessage="Kích thước kiểu chữ"
                  id="screens.presentations.ConfigSlide.1435964239"
                />
              </span>
              <div className="flex flex-col gap-2">
                <span className="font-medium text-sm text-gray-700">
                  <FormattedMessage
                    defaultMessage="Cấu hình kích thước"
                    id="screens.presentations.ContentConfigSlide.444330240"
                  />
                </span>
                <div className="h-full w-full">
                  <GroupBox>
                    <div className="flex flex-row gap-3 p-3 items-center">
                      <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                        <FormattedMessage
                          defaultMessage="Tự động chọn kích thước phù hợp"
                          id="screens.presentations.ContentConfigSlide.320775361"
                        />
                      </span>
                      <Form.Field
                        name={['presentations', focusedIndex, 'content', 'metadata', 'fontSize', 'auto']}
                        trigger="onCheckedChange"
                        valuePropName="checked"
                      >
                        <Switch className="flex-shrink-0" />
                      </Form.Field>
                    </div>
                  </GroupBox>
                </div>
                {control.value && (
                  <span className="font-normal text-xs text-gray-700">
                    <FormattedMessage
                      defaultMessage="Hệ thống sẽ tự động điều chỉnh kích thước nội dung để phù hợp với kích thước màn hình."
                      id="screens.presentations.ContentConfigSlide.291032092"
                    />
                  </span>
                )}
              </div>
              {!control.value && (
                <div className="flex flex-col gap-2">
                  <span className="font-medium text-sm text-gray-700">
                    <FormattedMessage
                      defaultMessage="Tùy biến riêng"
                      id="screens.presentations.ConfigSlide.1975998546"
                    />
                  </span>
                  <div className="h-full w-full">
                    <GroupBox>
                      <div className="flex flex-row items-center bg-gray-50">
                        <span className="flex-1 min-w-0 p-3 font-semibold text-xs text-gray-700 uppercase">
                          <FormattedMessage
                            defaultMessage="Nội dung"
                            id="screens.presentations.HeaderConfigSlide.768903020"
                          />
                        </span>
                        <span className="w-[110px] flex-shrink-0 p-3 font-semibold text-xs text-gray-700 uppercase text-center">
                          <FormattedMessage
                            defaultMessage="Kích thước"
                            id="screens.presentations.HeaderConfigSlide.1145935829"
                          />
                        </span>
                      </div>
                      <div className="flex flex-row items-center">
                        <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                          <FormattedMessage
                            defaultMessage="Tên trạm quan trắc"
                            id="screens.presentations.ContentConfigSlide.1036745043"
                          />
                        </span>
                        <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                          <Form.Field
                            name={[
                              'presentations',
                              focusedIndex,
                              'content',
                              'metadata',
                              'fontSize',
                              'config',
                              'stationName',
                            ]}
                            trigger="onBlurInput"
                          >
                            <NumberInput
                              precision={0}
                              placeholder=""
                              suffix="px"
                              classNameWrapper="h-[33px]"
                              defaultValue={configFontSize.stationName}
                              clearable={false}
                              controls={false}
                              keyboard
                            />
                          </Form.Field>
                        </div>
                      </div>
                      <div className="flex flex-row items-center">
                        <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                          <FormattedMessage
                            defaultMessage="Tên cột"
                            id="screens.presentations.ContentConfigSlide.251967402"
                          />
                        </span>
                        <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                          <Form.Field
                            name={[
                              'presentations',
                              focusedIndex,
                              'content',
                              'metadata',
                              'fontSize',
                              'config',
                              'headerTable',
                            ]}
                            trigger="onBlurInput"
                          >
                            <NumberInput
                              precision={0}
                              placeholder=""
                              suffix="px"
                              classNameWrapper="h-[33px]"
                              defaultValue={configFontSize.headerTable}
                              clearable={false}
                              controls={false}
                              keyboard
                            />
                          </Form.Field>
                        </div>
                      </div>
                      <div className="flex flex-row items-center">
                        <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                          <FormattedMessage
                            defaultMessage="Giá trị"
                            id="screens.presentations.compoType.HistoriesOptions.1803181196"
                          />
                        </span>
                        <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                          <Form.Field
                            name={[
                              'presentations',
                              focusedIndex,
                              'content',
                              'metadata',
                              'fontSize',
                              'config',
                              'measureValue',
                            ]}
                            trigger="onBlurInput"
                          >
                            <NumberInput
                              precision={0}
                              placeholder=""
                              suffix="px"
                              classNameWrapper="h-[33px]"
                              defaultValue={configFontSize.measureValue}
                              clearable={false}
                              controls={false}
                              keyboard
                            />
                          </Form.Field>
                        </div>
                      </div>
                      <div className="flex flex-row items-center">
                        <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                          <FormattedMessage
                            defaultMessage="Đơn vị thông số"
                            id="screens.presentations.ContentConfigSlide.2020945267"
                          />
                        </span>
                        <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                          <Form.Field
                            name={[
                              'presentations',
                              focusedIndex,
                              'content',
                              'metadata',
                              'fontSize',
                              'config',
                              'measureUnit',
                            ]}
                            trigger="onBlurInput"
                          >
                            <NumberInput
                              precision={0}
                              placeholder=""
                              suffix="px"
                              classNameWrapper="h-[33px]"
                              defaultValue={configFontSize.measureUnit}
                              clearable={false}
                              controls={false}
                              keyboard
                            />
                          </Form.Field>
                        </div>
                      </div>
                      <div className="flex flex-row items-center">
                        <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                          <FormattedMessage
                            defaultMessage="Chú thích màu cảnh báo"
                            id="screens.presentations.ContentConfigSlide.1913531627"
                          />
                        </span>
                        <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                          <Form.Field
                            name={[
                              'presentations',
                              focusedIndex,
                              'content',
                              'metadata',
                              'fontSize',
                              'config',
                              'legendWarning',
                            ]}
                            trigger="onBlurInput"
                          >
                            <NumberInput
                              precision={0}
                              placeholder=""
                              suffix="px"
                              classNameWrapper="h-[33px]"
                              defaultValue={configFontSize.legendWarning}
                              clearable={false}
                              controls={false}
                              keyboard
                            />
                          </Form.Field>
                        </div>
                      </div>
                      <div className="flex flex-row items-center">
                        <span className="flex-1 min-w-0 p-3 font-normal text-sm text-gray-700">
                          <FormattedMessage
                            defaultMessage="Ngưỡng giới hạn"
                            id="screens.presentations.ContentConfigSlide.1242488578"
                          />
                        </span>
                        <div className="w-[110px] flex-shrink-0 pt-1.5 pr-2 pb-1.5 pl-2">
                          <Form.Field
                            name={[
                              'presentations',
                              focusedIndex,
                              'content',
                              'metadata',
                              'fontSize',
                              'config',
                              'limitThreshold',
                            ]}
                            trigger="onBlurInput"
                          >
                            <NumberInput
                              precision={0}
                              placeholder=""
                              suffix="px"
                              classNameWrapper="h-[33px]"
                              defaultValue={configFontSize.limitThreshold}
                              clearable={false}
                              controls={false}
                              keyboard
                            />
                          </Form.Field>
                        </div>
                      </div>
                    </GroupBox>
                  </div>
                  <span className="font-normal text-xs text-gray-700">
                    <FormattedMessage
                      defaultMessage="Cho phép tùy chỉnh kích thước nội dung hiển thị trên màn hình. Hệ thống sẽ không tự động thay đổi kích thước."
                      id="screens.presentations.HeaderConfigSlide.595062375"
                    />
                  </span>
                </div>
              )}
            </div>
          );
        }}
      </Form.Field>
    </Fragment>
  );
}
