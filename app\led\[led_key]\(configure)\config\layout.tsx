'use client';

import ItemNav from '@/components/sessions/ItemNav';
import { segmentsPath } from '@/constants';
import { AsideItemProps, navLedChildConfig } from '@/constants/nav';
import { useLedStores } from '@/stores';
import { extractAfterSegments } from '@/utils';
import { usePathname } from 'next/navigation';
import { PropsWithChildren, useMemo } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';

export default function LayoutLedConfig({ children }: PropsWithChildren) {
   const dataLedDetail = useLedStores((store) => store.dataLedDetail);
  const intl = useIntl();
  const pathName = usePathname();
  const segments = extractAfterSegments(pathName);
  const locale = intl.locale as 'vi' | 'en';
  const subTitle = useMemo(() => {
    switch (segments) {
      case segmentsPath.ledConfig:
        return (
          <FormattedMessage
            defaultMessage="Cấu hình chia sẻ bảng LED"
            id="components.sessions.HeaderConfig.1116517647"
          />
        );
      case segmentsPath.ledPresentations:
        return (
          <FormattedMessage
            defaultMessage="Thiết kế trang trình chiếu"
            id="components.sessions.HeaderConfig.2096504904"
          />
        );
      case segmentsPath.ledConfigSlideShows:
        return (
          <FormattedMessage
            defaultMessage="Cấu hình trang trình chiếu"
            id="components.sessions.HeaderConfig.126322537"
          />
        );
      case segmentsPath.ledConfigDefaultData:
        return (
          <FormattedMessage
            defaultMessage="Cấu hình dữ liệu mặc định"
            id="components.sessions.HeaderConfig.164931504"
          />
        );
      default:
        return '';
    }
  }, [segments]);
  return (
    <div className="flex flex-col h-full w-full">
      <div className="h-[68px] flex-shrink-0 border-b border-gray-200 py-4 pl-5 pr-4 flex flex-row justify-between items-center">
        <div className="flex flex-col">
          <span className="font-semibold text-lg text-gray-700">{dataLedDetail?.name[locale]}</span>
          {subTitle && <span className="font-normal text-xs text-gray-600">{subTitle}</span>}
        </div>
      </div>
      <div className="flex-1 min-h-0">
        <div className="flex flex-row h-full">
          <div className="w-[260px] flex-shrink-0 bg-gray-50 border-r border-gray-200 p-5 flex flex-col gap-2">
            <span className="font-medium text-base text-gray-700">
              <FormattedMessage
                defaultMessage="Cấu hình chung"
                id="app.led.[led_key].(configure).config.layout.1669473708"
              />
            </span>
            <div className="flex flex-col gap-2">
              {navLedChildConfig.map((itemAside: AsideItemProps, indexItem: number) => (
                <ItemNav key={indexItem} itemAside={itemAside} isToggle={false} />
              ))}
            </div>
          </div>
          <div className="flex-1 min-w-0 bg-gray-100 h-full p-5 block">
            <div className="flex flex-col h-full max-h-full">{children}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
