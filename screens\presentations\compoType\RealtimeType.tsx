'use client';

import GroupBox from '@/components/sessions/GroupBox';
import { NumberInput } from '@/components/ui/NumberInput';
import { ContentLayout, dataTypeObject, GridView } from '@/constants';
import { useLedConfigStores, useLedStores } from '@/stores';
import { SquaresFourIcon, TableIcon, WarningIcon } from '@phosphor-icons/react';
import { title } from 'process';
import { useEffect, useMemo, useState, useRef, useCallback } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import {
  Alert,
  Combobox,
  Form,
  FormInstance,
  MultipleSelect,
  OptionItemType,
  RadioGroup,
  RadioGroupItem,
  Switch,
} from 'ui-components';
import GridConfigTemplate from '../components/GridConfigTemplate';
import { ContentType, ISlide, RealtimeContent, RealTimeMetadata } from '@/types/slide';
import TalbeConfigTemplate from '../components/TableConfigTemplate';
import { paginateArray } from '@/utils';
type Props = {
  focusedIndex: number;
  form: FormInstance;
  type: ContentType;
  cameraOptions: OptionItemType[];
  measuringOptions: OptionItemType[];
  stationOptions: OptionItemType[];
  stationTypeOptions: OptionItemType[];
  dataContent: {
    metadata: RealtimeContent;
  };
};
enum Tag {
  desktop = 'desktop',
  tablet = 'tablet',
  mobile = 'mobile',
}
export const RealtimeType = ({ focusedIndex, form, stationTypeOptions, dataContent, ...props }: Props) => {
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const dataLedDetail = useLedStores((store) => store.dataLedDetail);
  const [stationOptions, setStationOptions] = useState<OptionItemType[]>(props.stationOptions ?? []);
  const [measuringOptions, setMeasuringOptions] = useState<OptionItemType[]>(props.measuringOptions ?? []);
  const [cameraOptions, setCameraOptions] = useState<OptionItemType[]>(props.cameraOptions ?? []);
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';

  useEffect(() => {
    setStationOptions(props.stationOptions);
  }, [props.stationOptions]);
  useEffect(() => {
    setMeasuringOptions(props.measuringOptions);
  }, [props.measuringOptions]);
  useEffect(() => {
    setCameraOptions(props.cameraOptions);
  }, [props.cameraOptions]);

  const handleStationTypeChange = (stationType: string) => {
    const stationFilter = dataLedDetail?.dataStations.filter((station) => station.stationTypeKey === stationType) ?? [];
    const stations = stationFilter.map((station) => {
      return {
        title: station?.name[locale],
        value: station.key,
      };
    });

    setStationOptions(stations ?? []);
    const stationSelected = stations.length > 0 ? stations[0] : { title: '', value: '' };

    const cameraList = stationFilter
      .find((station) => station.key === stationSelected.value)
      ?.cameras?.map((camera) => {
        return {
          title: camera.name,
          value: camera.cameraId,
        };
      });
    setCameraOptions(cameraList ?? []);
    const cameraSelected = cameraList?.map((camera) => camera.value) ?? [];

    const measurings = stationFilter
      .find((station) => station.key === stationSelected.value)
      ?.measuringList.map((measure) => {
        return {
          title: measure?.name[locale],
          value: measure?.key,
        };
      });
    setMeasuringOptions(measurings ?? []);
    const measureSelected = measurings?.map((measure) => measure.value) ?? [];
    form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'station'], stationSelected.value);
    form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'camera', 'list'], cameraSelected);
    form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'measure'], measureSelected);
  };

  const handleStationChange = (station: string) => {
    const stationType = form.getFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'stationType']);
    const stationFilter = dataLedDetail?.dataStations.filter((station) => station.stationTypeKey === stationType) ?? [];

    const stations = stationFilter.map((station) => {
      return {
        title: station?.name[locale],
        value: station.key,
      };
    });

    setStationOptions(stations ?? []);
    const stationSelected = stations.find((data) => data.value === station) || { title: '', value: '' };
    const cameraList = stationFilter
      .find((station) => station.key === stationSelected.value)
      ?.cameras?.map((camera) => {
        return {
          title: camera.name,
          value: camera.cameraId,
        };
      });
    setCameraOptions(cameraList ?? []);
    const cameraSelected = cameraList?.map((camera) => camera.value) ?? [];

    const measurings = stationFilter
      .find((station) => station.key === stationSelected.value)
      ?.measuringList.map((measure) => {
        return {
          title: measure?.name[locale],
          value: measure?.key,
        };
      });
    setMeasuringOptions(measurings ?? []);
    const measureSelected = measurings?.map((measure) => measure.value) ?? [];
    form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'station'], stationSelected.value);
    form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'camera', 'list'], cameraSelected);
    form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'measure'], measureSelected);
  };

  const getDeviceFromSize = useCallback((width: number): Tag => {
    if (width <= 480) {
      return Tag.mobile;
    }
    if (width > 480 && width <= 960) {
      return Tag.tablet;
    }
    return Tag.desktop;
  }, []);

  return (
    <>
      <div className="flex flex-col gap-2">
        <span className="font-medium text-sm text-gray-700">
          <FormattedMessage defaultMessage="Loại trạm" id="screens.presentations.compoType.RealtimeType.64631071" />
        </span>
        <div className="h-full w-full">
          <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'stationType']}>
            {(control) => (
              <Combobox
                value={control?.value}
                onChange={(value) => {
                  control.onChange(value);
                  handleStationTypeChange(value);
                }}
                options={stationTypeOptions}
                placeholder={intl.formatMessage({
                  defaultMessage: 'Chọn loại trạm',
                  id: 'screens.presentations.compoType.RealtimeType.214170661',
                })}
                contentClassName="p-1 rounded-xl [&_div.selected]:bg-gray-100 [&_[cmdk-group-items]]:flex [&_[cmdk-group-items]]:flex-col [&_[cmdk-group-items]]:gap-1"
                itemClassName="bg-white hover:bg-gray-100 data-[selected=true]:bg-gray-100 text-gray-700 cursor-pointer p-2"
                sideOffset={-45}
                modalPopover
                textEmpty={
                  <span className="text-current p-2 block w-full h-full">
                    <FormattedMessage
                      defaultMessage="Không có dữ liệu"
                      id="screens.presentations.compoType.RealtimeType.64631072"
                    />
                  </span>
                }
              />
            )}
          </Form.Field>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <span className="font-medium text-sm text-gray-700">
          <FormattedMessage
            defaultMessage="Trạm quan trắc"
            id="screens.presentations.compoType.RealtimeType.1630238613"
          />
        </span>
        <div className="h-full w-full">
          <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'station']}>
            {(control) => (
              <Combobox
                value={control?.value}
                onChange={(value) => {
                  control.onChange(value);
                  handleStationChange(value);
                }}
                options={stationOptions}
                placeholder={intl.formatMessage({
                  defaultMessage: 'Chọn trạm',
                  id: 'screens.presentations.compoType.RealtimeType.375683652',
                })}
                contentClassName="p-1 rounded-xl [&_div.selected]:bg-gray-100 [&_[cmdk-group-items]]:flex [&_[cmdk-group-items]]:flex-col [&_[cmdk-group-items]]:gap-1"
                itemClassName="bg-white hover:bg-gray-100 data-[selected=true]:bg-gray-100 text-gray-700 cursor-pointer p-2"
                sideOffset={-45}
                modalPopover
                textEmpty={
                  <span className="text-current p-2 block w-full h-full">
                    <FormattedMessage
                      defaultMessage="Không có dữ liệu"
                      id="screens.presentations.compoType.RealtimeType.64631074"
                    />
                  </span>
                }
              />
            )}
          </Form.Field>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <span className="font-medium text-sm text-gray-700">
          <FormattedMessage
            defaultMessage="Thông số quan trắc"
            id="screens.presentations.compoType.RealtimeType.1619163834"
          />
        </span>
        <div className="h-full w-full">
          <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'measure']}>
            <MultipleSelect
              options={measuringOptions}
              maxTagCount={2}
              placeholder={intl.formatMessage({
                defaultMessage: 'Chọn loại dữ liệu',
                id: 'screens.presentations.compoType.RealtimeType.1787698267',
              })}
              triggerClassName="transition-all duration-300 h-11"
              contentClassName="p-1 rounded-xl"
              itemClassName="bg-white hover:bg-gray-100 text-gray-700 cursor-pointer p-2"
              sideOffset={-45}
              modalPopover
              textEmpty={
                <span className="text-current p-2 block w-full h-full">
                  <FormattedMessage
                    defaultMessage="Không có dữ liệu"
                    id="screens.presentations.compoType.RealtimeType.64631075"
                  />
                </span>
              }
            />
          </Form.Field>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <span className="font-medium text-sm text-gray-700">
          <FormattedMessage
            defaultMessage="Cấu hình camera"
            id="screens.presentations.compoType.RealtimeType.220788582"
          />
        </span>
        <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'camera', 'isShow']}>
          {(controlCamera) => {
            return (
              <GroupBox className="h-full w-full">
                <div className="flex flex-row gap-3 p-3 items-center">
                  <span className="flex-1 min-w-0 font-normal text-sm text-gray-700">
                    <FormattedMessage
                      defaultMessage="Hiển thị camera"
                      id="screens.presentations.compoType.RealtimeType.665826750"
                    />
                  </span>
                  <Switch
                    checked={controlCamera.value}
                    onCheckedChange={(check) => {
                      controlCamera.onChange(check);
                      const realtimeMetadata = dataConfig?.presentations[focusedIndex]?.content
                        ?.metadata as RealTimeMetadata;
                      const cameraSelected = realtimeMetadata?.cameras?.length
                        ? realtimeMetadata?.cameras || []
                        : dataContent?.metadata?.camera?.list?.length
                        ? dataContent?.metadata?.camera?.list
                        : cameraOptions?.map((camera) => camera.value) ?? [];
                      form.setFieldValue(
                        ['presentations', focusedIndex, 'content', 'metadata', 'camera', 'list'],
                        cameraSelected,
                      );
                    }}
                    className="flex-shrink-0"
                  />
                </div>
                {controlCamera.value && (
                  <div className="flex flex-col gap-2 p-3">
                    <span className="flex-1 min-w-0 font-medium text-sm text-gray-700">
                      <FormattedMessage
                        defaultMessage="Camera hiển thị"
                        id="screens.presentations.compoType.RealtimeType.2135278082"
                      />
                    </span>
                    <div className="h-full w-full">
                      <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'camera', 'list']}>
                        <MultipleSelect
                          options={cameraOptions}
                          maxTagCount={2}
                          placeholder={intl.formatMessage({
                            defaultMessage: 'Chọn camera',
                            id: 'screens.presentations.compoType.RealtimeType.478006785',
                          })}
                          triggerClassName="transition-all duration-300 h-11 [&>div>div>div]:gap-[6px] [&>div>div>div]:max-w-[105px]"
                          contentClassName="p-1 rounded-xl"
                          itemClassName="bg-white hover:bg-gray-100 text-gray-700 cursor-pointer p-2"
                          sideOffset={-45}
                          modalPopover
                          textEmpty={
                            <span className="text-current p-2 block w-full h-full">
                              <FormattedMessage
                                defaultMessage="Không có dữ liệu"
                                id="screens.presentations.compoType.RealtimeType.64631075"
                              />
                            </span>
                          }
                        />
                      </Form.Field>
                    </div>
                  </div>
                )}
              </GroupBox>
            );
          }}
        </Form.Field>
      </div>
      <div className="flex flex-col gap-2">
        <span className="font-medium text-sm text-gray-700">
          <FormattedMessage
            defaultMessage="Bố cục hiển thị"
            id="screens.presentations.compoType.RealtimeType.1057146701"
          />
        </span>
        <div className="h-full w-full">
          <Form.Field
            name={['presentations', focusedIndex, 'content', 'metadata', 'layout', 'template']}
            trigger="onValueChange"
            valuePropName="value"
          >
            <RadioGroup className="w-full h-10">
              <GroupBox isVertical={false} className="h-full rounded-lg">
                <label
                  className="flex-1 flex flex-row gap-2 items-center py-2 px-4 text-gray-700 cursor-pointer"
                  htmlFor={`radio-${ContentLayout.grid}`}
                >
                  <SquaresFourIcon size={18} weight="regular" className="flex-shrink-0 text-current" />
                  <span className="font-normal text-sm text-current flex-1 min-w-0">
                    <FormattedMessage defaultMessage="Lưới" id="screens.presentations.compoType.RealtimeType.2924242" />
                  </span>
                  <RadioGroupItem
                    value={ContentLayout.grid}
                    id={`radio-${ContentLayout.grid}`}
                    className="w-4 h-4 border-2 border-gray-200 data-[state=checked]:bg-primary-500 flex-shrink-0"
                  />
                </label>
                <label
                  className="flex-1 flex flex-row gap-2 items-center py-2 px-4 text-gray-700 cursor-pointer"
                  htmlFor={`radio-${ContentLayout.table}`}
                >
                  <TableIcon size={18} weight="regular" className="flex-shrink-0 text-current" />
                  <span className="font-normal text-sm text-current flex-1 min-w-0">
                    <FormattedMessage defaultMessage="Bảng" id="screens.presentations.compoType.RealtimeType.9506842" />
                  </span>
                  <RadioGroupItem
                    value={ContentLayout.table}
                    id={`radio-${ContentLayout.table}`}
                    className="w-4 h-4 border-2 border-gray-200 data-[state=checked]:bg-primary-500 flex-shrink-0"
                  />
                </label>
              </GroupBox>
            </RadioGroup>
          </Form.Field>
        </div>
      </div>
      <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'layout', 'template']}>
        {(control) => {
          if (control.value === ContentLayout.grid) {
            return <GridConfigTemplate focusedIndex={focusedIndex} />;
          }

          if (control.value === ContentLayout.table) {
            return <TalbeConfigTemplate focusedIndex={focusedIndex} />;
          }

          return null;
        }}
      </Form.Field>
      <Form.Field name="width">
        {(controlWidth) => {
          return (
            <Form.Field name={['presentations', focusedIndex, 'content']}>
              {(controlContent) => {
                const device = getDeviceFromSize(controlWidth.value);
                const {
                  metadata: { measure, camera, layout },
                } = controlContent.value;
                const measureCurrent = measure;
                const cameraCurrent = camera.list;
                const layoutCurrent = layout;
                const configGrid =
                  layoutCurrent?.[layoutCurrent?.template]?.view === GridView.custom
                    ? layoutCurrent?.[layoutCurrent?.template]?.[layoutCurrent?.[layoutCurrent?.template]?.view][device]
                    : layoutCurrent?.[layoutCurrent?.template]?.[layoutCurrent?.[layoutCurrent?.template]?.view];
                const totalPageMeasure = paginateArray(measureCurrent, configGrid.column * configGrid.row).length;
                const totalPageCamera = paginateArray(cameraCurrent, configGrid.cameraPerPage * 1).length;
                if (totalPageCamera > totalPageMeasure && camera.isShow)
                  return (
                    <div className="w-full">
                      <Alert
                        isVisible
                        variant="warning"
                        className="p-3 [&_>_div]:flex-col [&_>_div]:gap-0 [&_>_div_>_div:first-child]:gap-2 [&_>_div_>_div:first-child]:items-center"
                        title={intl.formatMessage({
                          defaultMessage: 'Một số camera sẽ bị ẩn',
                          id: 'screens.presentations.compoType.RealtimeType.1288792407',
                        })}
                        icon={<WarningIcon size={20} weight="regular" className="text-current flex-shrink-0" />}
                      >
                        <span className="text-current text-sm">
                          <FormattedMessage
                            defaultMessage="Vui lòng điều chỉnh số lượng camera hoặc bố cục hiển thị để đảm bảo tất cả camera có thể hiển thị trong các trang chiếu."
                            id="screens.presentations.compoType.RealtimeType.1835055841"
                          />
                        </span>
                      </Alert>
                    </div>
                  );
                return null;
              }}
            </Form.Field>
          );
        }}
      </Form.Field>
    </>
  );
};
