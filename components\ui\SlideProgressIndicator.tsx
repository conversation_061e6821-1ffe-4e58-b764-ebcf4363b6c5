import { cn } from '@/utils/tailwind';
import { usePresentationStores } from '@/stores/presentationStores';
import { PlayIcon, PauseIcon } from '@phosphor-icons/react';

interface SlideProgressIndicatorProps {
  className?: string;
  showAutoPlayStatus?: boolean;
  showSubSlideProgress?: boolean;
  showSlideNumbers?: boolean;
}

const SlideProgressIndicator = ({
  className,
  showAutoPlayStatus = true,
  showSubSlideProgress = true,
  showSlideNumbers = true,
}: SlideProgressIndicatorProps) => {
  const {
    currentSlideIndex,
    currentSubSlideIndex,
    totalSlides,
    totalNavigationSteps,
    currentNavigationStep,
    isAutoPlaying,
    getCurrentSlide,
    getCurrentSubSlide,
  } = usePresentationStores();

  const currentSlide = getCurrentSlide();

  // Calculate progress percentages
  const overallProgress = totalNavigationSteps > 0 ? (currentNavigationStep / totalNavigationSteps) * 100 : 0;

  // Calculate sub-slide progress within current slide
  const currentSlideSubSlides = currentSlide?.subSlides?.filter((subSlide) => subSlide.show) || [];
  const subSlideProgress =
    currentSlideSubSlides.length > 0 ? ((currentSubSlideIndex + 1) / currentSlideSubSlides.length) * 100 : 100;

  return (
    <div
      className={cn('flex flex-col gap-2 bg-black/50 backdrop-blur-sm rounded-lg p-3 text-white text-sm', className)}
    >
      {/* Auto-play status indicator */}
      {showAutoPlayStatus && (
        <div className="flex items-center gap-2">
          <div className={cn('flex items-center gap-1', isAutoPlaying ? 'text-green-400' : 'text-gray-400')}>
            {isAutoPlaying ? <PlayIcon size={14} /> : <PauseIcon size={14} />}
            <span className="text-xs">{isAutoPlaying ? 'Auto-playing' : 'Paused'}</span>
          </div>
        </div>
      )}

      {/* Slide numbers */}
      {showSlideNumbers && (
        <div className="flex items-center justify-between text-xs text-gray-300">
          <span>
            Slide {currentSlideIndex + 1} of {totalSlides}
          </span>
          {currentSlideSubSlides.length > 0 && (
            <span>
              Sub-slide {currentSubSlideIndex + 1} of {currentSlideSubSlides.length}
            </span>
          )}
        </div>
      )}

      {/* Overall progress bar */}
      <div className="space-y-1">
        <div className="flex items-center justify-between text-xs text-gray-300">
          <span>Overall Progress</span>
          <span>{Math.round(overallProgress)}%</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div
            className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${overallProgress}%` }}
          />
        </div>
      </div>

      {/* Sub-slide progress within current slide */}
      {showSubSlideProgress && currentSlideSubSlides.length > 0 && (
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs text-gray-300">
            <span>Current Slide Progress</span>
            <span>{Math.round(subSlideProgress)}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-1.5">
            <div
              className="bg-green-500 h-1.5 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${subSlideProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Navigation step indicator */}
      <div className="flex items-center justify-between text-xs text-gray-400">
        <span>
          Step {currentNavigationStep + 1} of {totalNavigationSteps}
        </span>
        {currentSlide && currentSlide.title && (
          <span className="truncate max-w-32" title={currentSlide.title.en || currentSlide.title.vi || 'Untitled'}>
            {currentSlide.title.en || currentSlide.title.vi || 'Untitled'}
          </span>
        )}
      </div>

      {/* Slide dots indicator */}
      <div className="flex items-center justify-center gap-1 mt-1">
        {Array.from({ length: totalSlides }, (_, index) => (
          <div
            key={index}
            className={cn(
              'w-2 h-2 rounded-full transition-all duration-200',
              index === currentSlideIndex ? 'bg-blue-500 scale-125' : 'bg-gray-600 hover:bg-gray-500',
            )}
            title={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Sub-slide dots for current slide */}
      {showSubSlideProgress && currentSlideSubSlides.length > 1 && (
        <div className="flex items-center justify-center gap-1">
          {currentSlideSubSlides.map((_, index) => (
            <div
              key={index}
              className={cn(
                'w-1.5 h-1.5 rounded-full transition-all duration-200',
                index === currentSubSlideIndex ? 'bg-green-500 scale-125' : 'bg-gray-600',
              )}
              title={`Sub-slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default SlideProgressIndicator;
