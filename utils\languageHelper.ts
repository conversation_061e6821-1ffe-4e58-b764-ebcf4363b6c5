import { ILanguageData } from '@/types';

/**
 * Get localized text from ILanguageData object
 * Prioritizes Vietnamese (vi) over English (en)
 */
export const getLocalizedText = (languageData: ILanguageData | string | undefined, fallback: string = ''): string => {
  if (!languageData) return fallback;
  
  if (typeof languageData === 'string') return languageData;
  
  return languageData.vi || languageData.en || fallback;
};