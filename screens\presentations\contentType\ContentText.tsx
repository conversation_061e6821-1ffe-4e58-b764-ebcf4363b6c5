import { usePagination } from '@/hooks/usePagination';
import { useLedConfigStores, useSlideConfigStores } from '@/stores';
import { EContentType, SlideConfigDto, TextContent } from '@/types/slide';
import { cn } from '@/utils/tailwind';
import { EmblaCarouselType } from 'embla-carousel';
import Fade from 'embla-carousel-fade';
import useEmblaCarousel from 'embla-carousel-react';
import React, { useMemo, useRef, useEffect } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { hexToRgba } from '@/utils';
import { ResponsiveTextWithOverride } from '../components/ResponsiveTextWithOverride';
import { priorityText } from '../compoType/constants';
type Props = {
  dataSlide: SlideConfigDto;
  isIframeReady: boolean;
  setEmblaApi: (emblaApi: EmblaCarouselType) => void;
};
export default function ContentText({ dataSlide, isIframeReady, setEmblaApi }: Props) {
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: false, watchDrag: false, containScroll: false }, [Fade()]);
  const { selectedIndex } = usePagination(emblaApi);
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const config = useSlideConfigStores((store) => store.config);

  // Ref for dynamic overflow calculation
  const containerRef = useRef<HTMLDivElement>(null);

  if (!dataConfig) return null;

  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );

  const dataContentSlide = useMemo(() => dataSlide.content, [dataSlide]) as {
    type: EContentType.text;
    metadata: TextContent;
  };

  const contentText = useMemo(() => dataContentSlide.metadata.content[locale], [locale, dataContentSlide]);

  // Check if text overflows and needs ellipsis
  useEffect(() => {
    const checkOverflow = () => {
      if (containerRef.current) {
        const container = containerRef.current;
        const textElement = container.querySelector('[data-text-content]') as HTMLElement;

        if (textElement) {
          // Reset styles to measure natural height
          textElement.style.maxHeight = 'none';
          textElement.style.overflow = 'visible';

          const containerHeight = container.clientHeight;
          const textHeight = textElement.scrollHeight;

          // If text overflows, apply ellipsis styles
          if (textHeight > containerHeight) {
            textElement.style.maxHeight = `${containerHeight}px`;
            textElement.style.overflow = 'hidden';
            textElement.style.display = '-webkit-box';
            textElement.style.webkitBoxOrient = 'vertical';
            textElement.style.textOverflow = 'ellipsis';

            // Calculate approximate lines that fit
            const lineHeight = parseFloat(getComputedStyle(textElement).lineHeight);
            const approximateLines = Math.floor(containerHeight / lineHeight);
            textElement.style.webkitLineClamp = approximateLines.toString();
          } else {
            // Text fits naturally, no ellipsis needed
            textElement.style.maxHeight = 'none';
            textElement.style.overflow = 'visible';
            textElement.style.display = 'block';
            textElement.style.webkitBoxOrient = '';
            textElement.style.textOverflow = '';
            textElement.style.webkitLineClamp = '';
          }
        }
      }
    };

    // Initial check
    const timer = setTimeout(checkOverflow, 100);

    // Set up ResizeObserver
    let resizeObserver: ResizeObserver | null = null;

    if (containerRef.current && typeof ResizeObserver !== 'undefined') {
      resizeObserver = new ResizeObserver(() => {
        setTimeout(checkOverflow, 50);
      });

      resizeObserver.observe(containerRef.current);
    }

    return () => {
      clearTimeout(timer);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [contentText]);
  return (
    <div className="h-full w-full overflow-hidden" ref={emblaRef}>
      <div className={cn('embla__container flex h-full w-full')}>
        <div className="embla__slide flex-[0_0_100%] min-w-0 h-full">
          <div
            className="flex-1 h-full w-full grid gap-[1px]"
            style={{
              backgroundColor: defaultConfig.slideConfig?.borderColor,
            }}
          >
            <div
              className="h-full w-full"
              style={{
                backgroundColor: defaultConfig.slideConfig?.background,
              }}
            >
              {contentText.length ? (
                <div
                  style={{
                    backgroundColor: dataContentSlide.metadata.config.background,
                    color: dataContentSlide.metadata.config.color,
                  }}
                  className={cn('h-full w-full flex items-center justify-center p-4 xl:p-8 3xl:p-10')}
                >
                  <div
                    ref={containerRef}
                    className="flex-1 h-full font-bold text-center w-full overflow-hidden"
                    style={{
                      wordBreak: 'break-word',
                      lineHeight: '1.4em',
                      position: 'relative',
                      fontSize: `${dataContentSlide.metadata.fontSize.config.content}px`,
                    }}
                  >
                    <div
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: 0,
                      }}
                    >
                      <ResponsiveTextWithOverride
                        data-text-content
                        value={dataContentSlide.metadata.fontSize.config.content}
                        style={{
                          wordBreak: 'break-word',
                          lineHeight: '1.4em',
                          textAlign: 'center',
                          fontWeight: 'bold',
                        }}
                      >
                        {contentText}
                      </ResponsiveTextWithOverride>
                    </div>
                  </div>
                </div>
              ) : (
                <div
                  className="h-full w-full flex justify-center items-center"
                  style={{
                    backgroundColor: dataContentSlide.metadata.config.background,
                  }}
                >
                  <div className="flex flex-col h-[224px] w-full items-center justify-center gap-4">
                    <div className="w-full flex flex-col gap-2 items-center justify-center">
                      <ResponsiveTextWithOverride
                        className="font-semibold"
                        options={config}
                        priority={priorityText.title as any}
                        style={{
                          color: dataContentSlide.metadata.config.color,
                        }}
                      >
                        <FormattedMessage
                          defaultMessage="Chưa có nội dung hiển thị"
                          id="screens.presentations.contentType.ContentText.160664721"
                        />
                      </ResponsiveTextWithOverride>
                      <ResponsiveTextWithOverride
                        className="font-normal text-center"
                        options={config}
                        priority={priorityText.description as any}
                        style={{
                          color: hexToRgba(dataContentSlide.metadata.config.color, 0.8),
                        }}
                      >
                        <FormattedMessage
                          defaultMessage="Vui lòng nhập nội dung sẽ hiển thị trên màn hình."
                          id="screens.presentations.contentType.ContentText.1260435920"
                        />
                      </ResponsiveTextWithOverride>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
