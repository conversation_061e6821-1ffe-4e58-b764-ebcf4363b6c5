'use client';

import GroupBox from '@/components/sessions/GroupBox';
import { NumberInput } from '@/components/ui/NumberInput';
import { TabsCommon } from '@/components/ui/TabsCommon';
import { GridView } from '@/constants';
import { useLedConfigStores, useLedStores } from '@/stores';
import { ContentType, RealTimeMetadata } from '@/types/slide';
import { cn } from '@/utils/tailwind';
import { CaretDownIcon, DeviceMobileSpeakerIcon, DeviceTabletSpeakerIcon, MonitorIcon } from '@phosphor-icons/react';
import { useEffect, useMemo, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import {
  Accordion,
  Combobox,
  Form,
  FormInstance,
  MultipleSelect,
  OptionItemType,
  RadioGroup,
  RadioGroupItem,
} from 'ui-components';
type Props = {
  focusedIndex: number;
  form: FormInstance;
  type: ContentType;
  measuringOptions: OptionItemType[];
  stationOptions: OptionItemType[];
  stationTypeOptions: OptionItemType[];
};
enum Tag {
  desktop = 'desktop',
  tablet = 'tablet',
  mobile = 'mobile',
}

export const HistoryType = ({ focusedIndex, form, stationTypeOptions, ...props }: Props) => {
  const dataLedDetail = useLedStores((store) => store.dataLedDetail);
  const [stationOptions, setStationOptions] = useState<OptionItemType[]>(props.stationOptions ?? []);
  const [measuringOptions, setMeasuringOptions] = useState<OptionItemType[]>(props.measuringOptions ?? []);
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';

  useEffect(() => {
    setStationOptions(props.stationOptions);
  }, [props.stationOptions]);
  useEffect(() => {
    setMeasuringOptions(props.measuringOptions);
  }, [props.measuringOptions]);

  const handleStationTypeChange = (stationType: string) => {
    const stationFilter = dataLedDetail?.dataStations.filter((station) => station.stationTypeKey === stationType) ?? [];
    const stations = stationFilter.map((station) => {
      return {
        title: station?.name[locale],
        value: station.key,
      };
    });

    setStationOptions(stations ?? []);
    const stationSelected = stations.length > 0 ? stations[0] : { title: '', value: '' };
    const measurings = stationFilter
      .find((station) => station.key === stationSelected.value)
      ?.measuringList.map((measure) => {
        return {
          title: measure?.name[locale],
          value: measure?.key,
        };
      });
    setMeasuringOptions(measurings ?? []);
    const measureSelected = measurings?.map((measure) => measure.value) ?? [];
    form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'station'], stationSelected.value);
    form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'measure'], measureSelected);
  };

  const handleStationChange = (station: string) => {
    const stationType = form.getFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'stationType']);
    const stationFilter = dataLedDetail?.dataStations.filter((station) => station.stationTypeKey === stationType) ?? [];

    const stations = stationFilter.map((station) => {
      return {
        title: station?.name[locale],
        value: station.key,
      };
    });

    setStationOptions(stations ?? []);
    const stationSelected = stations.find((data) => data.value === station) || { title: '', value: '' };
    const measurings = stationFilter
      .find((station) => station.key === stationSelected.value)
      ?.measuringList.map((measure) => {
        return {
          title: measure?.name[locale],
          value: measure?.key,
        };
      });
    setMeasuringOptions(measurings ?? []);
    const measureSelected = measurings?.map((measure) => measure.value) ?? [];
    form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'station'], stationSelected.value);
    form.setFieldValue(['presentations', focusedIndex, 'content', 'metadata', 'measure'], measureSelected);
  };

  const RangeTimeOptions = useMemo(() => {
    return [
      {
        title: intl.formatMessage({
          defaultMessage: '7 ngày gần nhất',
          id: 'screens.presentations.compoType.HistoryType.1819193952',
        }),
        value: '7 days',
      },
      {
        title: intl.formatMessage({
          defaultMessage: '15 ngày gần nhất',
          id: 'screens.presentations.compoType.HistoryType.442280563',
        }),
        value: '15 days',
      },
      {
        title: intl.formatMessage({
          defaultMessage: '30 ngày gần nhất',
          id: 'screens.presentations.compoType.HistoryType.411173370',
        }),
        value: '30 days',
      },
    ];
  }, [locale]);

  const RangeTimeType = useMemo(() => {
    return [
      {
        title: intl.formatMessage({
          defaultMessage: 'Hôm qua',
          id: 'screens.presentations.compoType.HistoryType.2026936738',
        }),
        value: 'yesterday',
      },
      {
        title: intl.formatMessage({
          defaultMessage: 'Hôm nay',
          id: 'screens.presentations.compoType.HistoryType.2026940217',
        }),
        value: 'today',
      },
    ];
  }, [locale]);

  const CalcOptions = useMemo(() => {
    return [
      {
        title: intl.formatMessage({
          defaultMessage: 'Trung bình ngày',
          id: 'screens.presentations.compoType.HistoryType.967860450',
        }),
        value: 'average_day',
      },
    ];
  }, [locale]);

  return (
    <>
      <div className="flex flex-col gap-2">
        <span className="font-medium text-sm text-gray-700">
          <FormattedMessage defaultMessage="Loại trạm" id="screens.presentations.compoType.RealtimeType.64631071" />
        </span>
        <div className="h-full w-full">
          <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'stationType']}>
            {(control) => (
              <Combobox
                value={control?.value}
                onChange={(value) => {
                  control.onChange(value);
                  handleStationTypeChange(value);
                }}
                options={stationTypeOptions}
                placeholder={intl.formatMessage({
                  defaultMessage: 'Chọn loại trạm',
                  id: 'screens.presentations.compoType.RealtimeType.214170661',
                })}
                contentClassName="p-1 rounded-xl [&_div.selected]:bg-gray-100 [&_[cmdk-group-items]]:flex [&_[cmdk-group-items]]:flex-col [&_[cmdk-group-items]]:gap-1"
                itemClassName="bg-white hover:bg-gray-100 data-[selected=true]:bg-gray-100 text-gray-700 cursor-pointer p-2"
                sideOffset={-45}
                modalPopover
                textEmpty={
                  <span className="text-current p-2 block w-full h-full">
                    <FormattedMessage
                      defaultMessage="Không có dữ liệu"
                      id="screens.presentations.compoType.RealtimeType.64631072"
                    />
                  </span>
                }
              />
            )}
          </Form.Field>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <span className="font-medium text-sm text-gray-700">
          <FormattedMessage
            defaultMessage="Trạm quan trắc"
            id="screens.presentations.compoType.RealtimeType.1630238613"
          />
        </span>
        <div className="h-full w-full">
          <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'station']}>
            {(control) => (
              <Combobox
                value={control?.value}
                onChange={(value) => {
                  control.onChange(value);
                  handleStationChange(value);
                }}
                options={stationOptions}
                placeholder={intl.formatMessage({
                  defaultMessage: 'Chọn trạm',
                  id: 'screens.presentations.compoType.RealtimeType.375683652',
                })}
                contentClassName="p-1 rounded-xl [&_div.selected]:bg-gray-100 [&_[cmdk-group-items]]:flex [&_[cmdk-group-items]]:flex-col [&_[cmdk-group-items]]:gap-1"
                itemClassName="bg-white hover:bg-gray-100 data-[selected=true]:bg-gray-100 text-gray-700 cursor-pointer p-2"
                sideOffset={-45}
                modalPopover
                textEmpty={
                  <span className="text-current p-2 block w-full h-full">
                    <FormattedMessage
                      defaultMessage="Không có dữ liệu"
                      id="screens.presentations.compoType.RealtimeType.64631074"
                    />
                  </span>
                }
              />
            )}
          </Form.Field>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <span className="font-medium text-sm text-gray-700">
          <FormattedMessage
            defaultMessage="Thông số quan trắc"
            id="screens.presentations.compoType.RealtimeType.1619163834"
          />
        </span>
        <div className="h-full w-full">
          <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'measure']}>
            <MultipleSelect
              options={measuringOptions}
              maxTagCount={2}
              placeholder={intl.formatMessage({
                defaultMessage: 'Chọn loại dữ liệu',
                id: 'screens.presentations.compoType.RealtimeType.1787698267',
              })}
              triggerClassName="transition-all duration-300 h-11"
              contentClassName="p-1 rounded-xl"
              itemClassName="bg-white hover:bg-gray-100 text-gray-700 cursor-pointer p-2"
              sideOffset={-45}
              modalPopover
              textEmpty={
                <span className="text-current p-2 block w-full h-full">
                  <FormattedMessage
                    defaultMessage="Không có dữ liệu"
                    id="screens.presentations.compoType.RealtimeType.64631075"
                  />
                </span>
              }
            />
          </Form.Field>
        </div>
      </div>
      <div className="flex flex-row gap-4">
        <div className="flex-1 min-w-0 flex flex-col gap-2">
          <span className="font-medium text-sm text-gray-700">
            <FormattedMessage
              defaultMessage="Thời gian hiển thị"
              id="screens.presentations.compoType.HistoryType.1576522092"
            />
          </span>
          <div className="h-full w-full">
            <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'timeRange']}>
              <Combobox
                options={RangeTimeOptions}
                placeholder=""
                contentClassName="p-1 rounded-xl [&_div.selected]:bg-gray-100 [&_[cmdk-group-items]]:flex [&_[cmdk-group-items]]:flex-col [&_[cmdk-group-items]]:gap-1"
                itemClassName="bg-white hover:bg-gray-100 data-[selected=true]:bg-gray-100 text-gray-700 cursor-pointer p-2"
                sideOffset={-45}
                modalPopover
              />
            </Form.Field>
          </div>
        </div>
        <div className="flex-1 min-w-0 flex flex-col gap-2">
          <span className="font-medium text-sm text-gray-700">
            <FormattedMessage
              defaultMessage="Tính từ ngày"
              id="screens.presentations.compoType.HistoryType.1244918798"
            />
          </span>
          <div className="h-full w-full">
            <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'typeTimeRange']}>
              <Combobox
                options={RangeTimeType}
                placeholder=""
                contentClassName="p-1 rounded-xl [&_div.selected]:bg-gray-100 [&_[cmdk-group-items]]:flex [&_[cmdk-group-items]]:flex-col [&_[cmdk-group-items]]:gap-1"
                itemClassName="bg-white hover:bg-gray-100 data-[selected=true]:bg-gray-100 text-gray-700 cursor-pointer p-2"
                sideOffset={-45}
                modalPopover
              />
            </Form.Field>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <span className="font-medium text-sm text-gray-700">
          <FormattedMessage
            defaultMessage="Công thức tính toán hiển thị"
            id="screens.presentations.compoType.HistoryType.1371798518"
          />
        </span>
        <div className="h-full w-full">
          <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'calc']}>
            <Combobox
              options={CalcOptions}
              placeholder=""
              contentClassName="p-1 rounded-xl [&_div.selected]:bg-gray-100"
              itemClassName="bg-white hover:bg-gray-100 data-[selected=true]:bg-gray-100 text-gray-700 cursor-pointer p-2"
              sideOffset={-45}
              modalPopover
            />
          </Form.Field>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <span className="font-medium text-sm text-gray-700">
          <FormattedMessage
            defaultMessage="Bố cục hiển thị"
            id="screens.presentations.compoType.RealtimeType.1057146701"
          />
        </span>
        <div>
          <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'layout', 'view']}>
            {(control) => {
              return (
                <RadioGroup value={control.value} onValueChange={control.onChange}>
                  <GroupBox className="gap-[1px] bg-gray-200 transition-all">
                    <Accordion
                      open={control.value === GridView.general}
                      className="overflow-hidden border-none rounded-none"
                    >
                      <Accordion.Header className="px-4 py-3 cursor-pointer">
                        <label
                          className="w-full flex flex-row gap-3 cursor-pointer"
                          htmlFor={`radio-${GridView.general}`}
                        >
                          <span className="flex flex-row gap-3 flex-1 min-w-0 font-normal text-sm text-gray-700 cursor-pointer">
                            <RadioGroupItem
                              value={GridView.general}
                              id={`radio-${GridView.general}`}
                              className="w-4 h-4 border-2 border-gray-200 data-[state=checked]:bg-primary-500"
                            />
                            <span className="flex-1 min-w-0 text-current">
                              <FormattedMessage
                                defaultMessage="Bố cục chung"
                                id="screens.presentations.components.GridConfigTemplate.940681017"
                              />
                            </span>
                            <CaretDownIcon
                              size={20}
                              className={cn('flex-shrink-0 text-gray-700 transition-all duration-300', {
                                'rotate-180': control.value === GridView.general,
                              })}
                              weight="regular"
                            />
                          </span>
                        </label>
                      </Accordion.Header>
                      <Accordion.Body className="gap-2 m-0 w-full p-3 pt-0">
                        <GroupBox className="w-full border border-primary-500">
                          <div className="flex flex-col gap-2 p-3 w-full">
                            <div className="flex-1 flex flex-row gap-4">
                              <div className="flex flex-col gap-2">
                                <span className="font-medium text-sm text-gray-700">
                                  <FormattedMessage
                                    defaultMessage="Số cột tối đa {value}"
                                    id="screens.presentations.components.TableConfigTemplate.1314136401"
                                    values={{
                                      value: '(4-8)',
                                    }}
                                  />
                                </span>
                                <Form.Field
                                  name={[
                                    'presentations',
                                    focusedIndex,
                                    'content',
                                    'metadata',
                                    'layout',
                                    'general',
                                    'column',
                                  ]}
                                  trigger="onBlurInput"
                                >
                                  <NumberInput
                                    placeholder=""
                                    precision={0}
                                    size="sm"
                                    keyboard
                                    controls
                                    clearable={false}
                                    min={4}
                                    max={8}
                                    defaultValue={7}
                                  />
                                </Form.Field>
                              </div>
                              <div className="flex flex-col gap-2">
                                <span className="font-medium text-sm text-gray-700">
                                  <FormattedMessage
                                    defaultMessage="Số hàng tối đa {value}"
                                    id="screens.presentations.components.TableConfigTemplate.281785528"
                                    values={{
                                      value: '(4-10)',
                                    }}
                                  />
                                </span>
                                <Form.Field
                                  name={[
                                    'presentations',
                                    focusedIndex,
                                    'content',
                                    'metadata',
                                    'layout',
                                    'general',
                                    'row',
                                  ]}
                                  trigger="onBlurInput"
                                >
                                  <NumberInput
                                    placeholder=""
                                    precision={0}
                                    size="sm"
                                    keyboard
                                    controls
                                    clearable={false}
                                    min={4}
                                    max={10}
                                    defaultValue={7}
                                  />
                                </Form.Field>
                              </div>
                            </div>
                          </div>
                        </GroupBox>
                        <span className="font-normal text-xs text-gray-700">
                          <FormattedMessage
                            defaultMessage="Cho phép tùy chỉnh số cột và hàng hiển thị áp dụng chung cho mọi thiết bị."
                            id="screens.presentations.components.GridConfigTemplate.1415847971"
                          />
                        </span>
                      </Accordion.Body>
                    </Accordion>
                    <ConfigTemplateCustom focusedIndex={focusedIndex} open={control.value} />
                  </GroupBox>
                </RadioGroup>
              );
            }}
          </Form.Field>
        </div>
      </div>
    </>
  );
};

type PropsCustom = {
  open: 'custom' | 'general';
  focusedIndex: number;
};

const dataTag = [
  {
    label: (
      <div className="flex-1">
        <MonitorIcon size={20} weight="regular" className="flex-shrink-0 text-current" />
      </div>
    ),
    value: Tag.desktop,
  },
  {
    label: (
      <div className="flex-1">
        <DeviceTabletSpeakerIcon size={20} weight="regular" className="flex-shrink-0 text-current" />
      </div>
    ),
    value: Tag.tablet,
  },
  {
    label: (
      <div className="flex-1">
        <DeviceMobileSpeakerIcon size={20} weight="regular" className="flex-shrink-0 text-current" />
      </div>
    ),
    value: Tag.mobile,
  },
];

const ConfigTemplateCustom = ({ open, focusedIndex }: PropsCustom) => {
  const [currentTag, setCurrentTag] = useState(Tag.desktop);
  const onChangeTag = (tag: Tag) => {
    if (currentTag === tag) return;
    setCurrentTag(tag);
  };
  return (
    <Accordion open={open === GridView.custom} className="overflow-hidden border-none rounded-none">
      <Accordion.Header className="px-4 py-3 cursor-pointer">
        <label className="w-full flex flex-row gap-3 cursor-pointer" htmlFor={`radio-${GridView.custom}`}>
          <span className="flex flex-row gap-3 flex-1 min-w-0 font-normal text-sm text-gray-700 cursor-pointer">
            <RadioGroupItem
              value={GridView.custom}
              id={`radio-${GridView.custom}`}
              className="w-4 h-4 border-2 border-gray-200 data-[state=checked]:bg-primary-500"
            />
            <span className="flex-1 min-w-0 text-current">
              <FormattedMessage
                defaultMessage="Bố cục tùy chỉnh"
                id="screens.presentations.components.GridConfigTemplate.100098174"
              />
            </span>
            <CaretDownIcon
              size={20}
              className={cn('flex-shrink-0 text-gray-700 transition-all duration-300', {
                'rotate-180': open === GridView.custom,
              })}
              weight="regular"
            />
          </span>
        </label>
      </Accordion.Header>
      <Accordion.Body className="gap-2 m-0 w-full p-3 pt-0">
        <div className="flex flex-col rounded-lg border border-primary-500 w-full overflow-hidden">
          <div className="w-full">
            <TabsCommon.Border
              className="w-full"
              classNames={{
                item: 'flex-1 transition-all duration-300',
              }}
              dataTag={dataTag}
              onSwitchTag={(tag) => onChangeTag(tag as Tag)}
              actionTag={currentTag}
            />
          </div>
          <div className="flex flex-row transition-transform duration-300 ease-in-out">
            <div className="w-full min-w-0 flex-shrink-0">
              <SectionDevice focusedIndex={focusedIndex} device={currentTag} />
            </div>
          </div>
        </div>

        <span className="font-normal text-xs text-gray-700">
          {Tag.desktop === currentTag && (
            <FormattedMessage
              defaultMessage="Áp dụng cho thiết bị có màn hình lớn (> 960px), như máy tính để bàn hoặc laptop."
              id="screens.presentations.components.GridConfigTemplate.118472795"
            />
          )}
          {Tag.tablet === currentTag && (
            <FormattedMessage
              defaultMessage="Áp dụng cho thiết bị có màn hình trung bình (481px – 960px), như máy tính bảng."
              id="screens.presentations.components.GridConfigTemplate.56747109"
            />
          )}
          {Tag.mobile === currentTag && (
            <FormattedMessage
              defaultMessage="Áp dụng cho thiết bị có màn hình nhỏ (≤ 480px), như điện thoại di động."
              id="screens.presentations.components.GridConfigTemplate.2096532185"
            />
          )}
        </span>
      </Accordion.Body>
    </Accordion>
  );
};

const SectionDevice = ({ focusedIndex, device }: { focusedIndex: number; device: Tag }) => {
  return (
    <GroupBox className="flex flex-col border-none rounded-none">
      <div className="flex flex-col gap-2 p-3 w-full">
        <div className="flex-1 flex flex-row gap-4">
          <div className="flex flex-col gap-2">
            <span className="font-medium text-sm text-gray-700">
              <FormattedMessage
                defaultMessage="Số cột tối đa {value}"
                id="screens.presentations.components.TableConfigTemplate.1314136401"
                values={{
                  value: '(4-8)',
                }}
              />
            </span>
            <Form.Field
              name={['presentations', focusedIndex, 'content', 'metadata', 'layout', 'custom', device, 'column']}
              trigger="onBlurInput"
              preserve
            >
              <NumberInput
                placeholder=""
                precision={0}
                size="sm"
                keyboard
                controls
                clearable={false}
                min={4}
                max={8}
                defaultValue={7}
              />
            </Form.Field>
          </div>
          <div className="flex flex-col gap-2">
            <span className="font-medium text-sm text-gray-700">
              <FormattedMessage
                defaultMessage="Số hàng tối đa {value}"
                id="screens.presentations.components.TableConfigTemplate.281785528"
                values={{
                  value: '(4-10)',
                }}
              />
            </span>
            <Form.Field
              name={['presentations', focusedIndex, 'content', 'metadata', 'layout', 'custom', device, 'row']}
              trigger="onBlurInput"
              preserve
            >
              <NumberInput
                placeholder=""
                precision={0}
                size="sm"
                keyboard
                controls
                clearable={false}
                min={4}
                max={10}
                defaultValue={7}
              />
            </Form.Field>
          </div>
        </div>
      </div>
    </GroupBox>
  );
};
