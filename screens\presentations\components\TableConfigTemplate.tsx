import GroupBox from '@/components/sessions/GroupBox';
import { NumberInput } from '@/components/ui/NumberInput';
import { TabsCommon } from '@/components/ui/TabsCommon';
import { GridView } from '@/constants';
import { cn } from '@/utils/tailwind';
import {
  CaretDownIcon,
  DeviceMobileSpeakerIcon,
  DeviceTabletSpeakerIcon,
  MonitorIcon,
  SquaresFourIcon,
  WebcamIcon,
} from '@phosphor-icons/react';
import { useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { Accordion, Form, RadioGroup, RadioGroupItem } from 'ui-components';

type Props = {
  focusedIndex: number;
};

export default function TalbeConfigTemplate({ focusedIndex }: Props) {
  return (
    <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'layout', 'table', 'view']}>
      {(control) => {
        return (
          <Form.Field name={['presentations', focusedIndex, 'content', 'metadata', 'camera', 'isShow']}>
            {(controlCamera) => {
              return (
                <RadioGroup value={control.value} onValueChange={control.onChange}>
                  <GroupBox className="gap-[1px] bg-gray-200 transition-all">
                    <Accordion
                      open={control.value === GridView.general}
                      className="overflow-hidden border-none rounded-none"
                    >
                      <Accordion.Header className="px-4 py-3 cursor-pointer">
                        <label
                          className="w-full flex flex-row gap-3 cursor-pointer"
                          htmlFor={`radio-${GridView.general}`}
                        >
                          <span className="flex flex-row gap-3 flex-1 min-w-0 font-normal text-sm text-gray-700 cursor-pointer">
                            <RadioGroupItem
                              value={GridView.general}
                              id={`radio-${GridView.general}`}
                              className="w-4 h-4 border-2 border-gray-200 data-[state=checked]:bg-primary-500"
                            />
                            <span className="flex-1 min-w-0 text-current">
                              <FormattedMessage
                                defaultMessage="Bố cục chung"
                                id="screens.presentations.components.GridConfigTemplate.940681017"
                              />
                            </span>
                            <CaretDownIcon
                              size={20}
                              className={cn('flex-shrink-0 text-gray-700 transition-all duration-300', {
                                'rotate-180': control.value === GridView.general,
                              })}
                              weight="regular"
                            />
                          </span>
                        </label>
                      </Accordion.Header>
                      <Accordion.Body className="gap-2 m-0 w-full p-3 pt-0">
                        <GroupBox className="w-full border border-primary-500">
                          <div className="flex flex-col gap-2 p-3 w-full">
                            <div className="flex flex-row gap-2 items-center">
                              <SquaresFourIcon size={18} className="text-gray-700 flex-shrink-0" />
                              <span className="flex-1 min-w-0 font-medium text-sm text-gray-700">
                                <FormattedMessage
                                  defaultMessage="Bố cục thông số"
                                  id="screens.presentations.components.GridConfigTemplate.1929447243"
                                />
                              </span>
                            </div>
                            <div className="flex-1 flex flex-row gap-4">
                              <div className="flex flex-col gap-2">
                                <span className="font-medium text-sm text-gray-700">
                                  <FormattedMessage
                                    defaultMessage="Số hàng tối đa {value}"
                                    id="screens.presentations.components.TableConfigTemplate.281785528"
                                    values={{
                                      value: '(4-10)',
                                    }}
                                  />
                                </span>
                                <Form.Field
                                  name={[
                                    'presentations',
                                    focusedIndex,
                                    'content',
                                    'metadata',
                                    'layout',
                                    'table',
                                    'general',
                                    'column',
                                  ]}
                                  trigger="onBlurInput"
                                >
                                  <NumberInput
                                    precision={0}
                                    size="sm"
                                    keyboard
                                    controls
                                    placeholder=""
                                    clearable={false}
                                    min={4}
                                    max={10}
                                    defaultValue={7}
                                  />
                                </Form.Field>
                              </div>
                            </div>
                          </div>
                          {controlCamera.value && (
                            <div className="flex flex-col gap-2 p-3 w-full">
                              <div className="flex flex-row gap-2 items-center">
                                <WebcamIcon size={18} className="text-gray-700 flex-shrink-0" />
                                <span className="flex-1 min-w-0 font-medium text-sm text-gray-700">
                                  <FormattedMessage
                                    defaultMessage="Bố cục camera"
                                    id="screens.presentations.components.GridConfigTemplate.910370731"
                                  />
                                </span>
                              </div>
                              <div className="flex-1 flex flex-row gap-4">
                                <div className="flex flex-col gap-2">
                                  <span className="font-medium text-sm text-gray-700">
                                    <FormattedMessage
                                      defaultMessage="Số hàng tối đa (1-3)"
                                      id="screens.presentations.components.GridConfigTemplate.1696341717"
                                    />
                                  </span>
                                  <Form.Field
                                    name={[
                                      'presentations',
                                      focusedIndex,
                                      'content',
                                      'metadata',
                                      'layout',
                                      'table',
                                      'general',
                                      'cameraPerPage',
                                    ]}
                                    trigger="onBlurInput"
                                  >
                                    <NumberInput
                                      precision={0}
                                      size="sm"
                                      keyboard
                                      controls
                                      placeholder=""
                                      clearable={false}
                                      min={1}
                                      max={3}
                                      defaultValue={3}
                                    />
                                  </Form.Field>
                                </div>
                              </div>
                            </div>
                          )}
                          <div className="flex flex-row gap-1 p-3 h-[144px] w-full">
                            <Form.Field
                              name={[
                                'presentations',
                                focusedIndex,
                                'content',
                                'metadata',
                                'layout',
                                'table',
                                'general',
                                'column',
                              ]}
                            >
                              {(controlColumn) => {
                                const gridItem = 4 * (Number(controlColumn.value) ?? 3);
                                return (
                                  <div
                                    className="flex-[3] min-w-0 grid gap-[2px] rounded p-2 bg-gray-100"
                                    style={{
                                      gridTemplateColumns: 'repeat(4, minmax(0, 1fr))',
                                      gridTemplateRows: `repeat(${controlColumn.value ?? 7}, minmax(0, 1fr))`,
                                    }}
                                  >
                                    {Array.from({ length: gridItem }).map((item, index) => (
                                      <div key={index} className="bg-gray-200 rounded-[2px]" />
                                    ))}
                                  </div>
                                );
                              }}
                            </Form.Field>
                            {controlCamera.value && (
                              <div className="flex-[1] min-w-0 h-auto flex flex-col gap-1">
                                <Form.Field
                                  name={[
                                    'presentations',
                                    focusedIndex,
                                    'content',
                                    'metadata',
                                    'layout',
                                    'table',
                                    'general',
                                    'cameraPerPage',
                                  ]}
                                >
                                  {(controlCameraPerPage) => {
                                    return (
                                      <>
                                        {Array.from({ length: controlCameraPerPage.value }).map((_, index) => (
                                          <div
                                            key={index}
                                            className="bg-gray-100 rounded flex-1 h-full flex items-center justify-center"
                                          >
                                            <WebcamIcon size={20} className="text-gray-500 flex-shrink-0" />
                                          </div>
                                        ))}
                                      </>
                                    );
                                  }}
                                </Form.Field>
                              </div>
                            )}
                          </div>
                        </GroupBox>
                        <span className="font-normal text-xs text-gray-700">
                          <FormattedMessage
                            defaultMessage="Cho phép tùy chỉnh số cột và hàng hiển thị áp dụng chung cho mọi thiết bị."
                            id="screens.presentations.components.GridConfigTemplate.1415847971"
                          />
                        </span>
                      </Accordion.Body>
                    </Accordion>
                    <ConfigTemplateCustom
                      focusedIndex={focusedIndex}
                      open={control.value}
                      isShowCamera={controlCamera.value}
                    />
                  </GroupBox>
                </RadioGroup>
              );
            }}
          </Form.Field>
        );
      }}
    </Form.Field>
  );
}

type PropsCustom = {
  open: 'custom' | 'general';
  focusedIndex: number;
  isShowCamera: boolean;
};

enum Tag {
  desktop = 'desktop',
  tablet = 'tablet',
  mobile = 'mobile',
}

const dataTag = [
  {
    label: (
      <div className="flex-1">
        <MonitorIcon size={20} weight="regular" className="flex-shrink-0 text-current" />
      </div>
    ),
    value: Tag.desktop,
  },
  {
    label: (
      <div className="flex-1">
        <DeviceTabletSpeakerIcon size={20} weight="regular" className="flex-shrink-0 text-current" />
      </div>
    ),
    value: Tag.tablet,
  },
  {
    label: (
      <div className="flex-1">
        <DeviceMobileSpeakerIcon size={20} weight="regular" className="flex-shrink-0 text-current" />
      </div>
    ),
    value: Tag.mobile,
  },
];

const ConfigTemplateCustom = ({ open, focusedIndex, isShowCamera }: PropsCustom) => {
  const [currentTag, setCurrentTag] = useState(Tag.desktop);
  const onChangeTag = (tag: Tag) => {
    if (currentTag === tag) return;
    setCurrentTag(tag);
  };
  return (
    <Accordion open={open === GridView.custom} className="overflow-hidden border-none rounded-none">
      <Accordion.Header className="px-4 py-3 cursor-pointer">
        <label className="w-full flex flex-row gap-3 cursor-pointer" htmlFor={`radio-${GridView.custom}`}>
          <span className="flex flex-row gap-3 flex-1 min-w-0 font-normal text-sm text-gray-700 cursor-pointer">
            <RadioGroupItem
              value={GridView.custom}
              id={`radio-${GridView.custom}`}
              className="w-4 h-4 border-2 border-gray-200 data-[state=checked]:bg-primary-500"
            />
            <span className="flex-1 min-w-0 text-current">
              <FormattedMessage
                defaultMessage="Bố cục tùy chỉnh"
                id="screens.presentations.components.GridConfigTemplate.100098174"
              />
            </span>
            <CaretDownIcon
              size={20}
              className={cn('flex-shrink-0 text-gray-700 transition-all duration-300', {
                'rotate-180': open === GridView.custom,
              })}
              weight="regular"
            />
          </span>
        </label>
      </Accordion.Header>
      <Accordion.Body className="gap-2 m-0 w-full p-3 pt-0">
        <div className="flex flex-col rounded-lg border border-primary-500 w-full overflow-hidden">
          <div className="w-full">
            <TabsCommon.Border
              className="w-full"
              classNames={{
                item: 'flex-1 transition-all duration-300',
              }}
              dataTag={dataTag}
              onSwitchTag={(tag) => onChangeTag(tag as Tag)}
              actionTag={currentTag}
            />
          </div>
          <div className="flex flex-row transition-transform duration-300 ease-in-out">
            <div className="w-full min-w-0 flex-shrink-0">
              <SectionDevice focusedIndex={focusedIndex} device={currentTag} isShowCamera={isShowCamera} />
            </div>
          </div>
        </div>

        <span className="font-normal text-xs text-gray-700">
          {Tag.desktop === currentTag && (
            <FormattedMessage
              defaultMessage="Áp dụng cho thiết bị có màn hình lớn (> 960px), như máy tính để bàn hoặc laptop."
              id="screens.presentations.components.GridConfigTemplate.118472795"
            />
          )}
          {Tag.tablet === currentTag && (
            <FormattedMessage
              defaultMessage="Áp dụng cho thiết bị có màn hình trung bình (481px – 960px), như máy tính bảng."
              id="screens.presentations.components.GridConfigTemplate.56747109"
            />
          )}
          {Tag.mobile === currentTag && (
            <FormattedMessage
              defaultMessage="Áp dụng cho thiết bị có màn hình nhỏ (≤ 480px), như điện thoại di động."
              id="screens.presentations.components.GridConfigTemplate.2096532185"
            />
          )}
        </span>
      </Accordion.Body>
    </Accordion>
  );
};

const SectionDevice = ({
  focusedIndex,
  device,
  isShowCamera,
}: {
  focusedIndex: number;
  device: Tag;
  isShowCamera: boolean;
}) => {
  return (
    <GroupBox className="flex flex-col border-none rounded-none">
      <div className="flex flex-col gap-2 p-3 w-full">
        <div className="flex flex-row gap-2 items-center">
          <SquaresFourIcon size={18} className="text-gray-700 flex-shrink-0" />
          <span className="flex-1 min-w-0 font-medium text-sm text-gray-700">
            <FormattedMessage
              defaultMessage="Bố cục thông số"
              id="screens.presentations.components.GridConfigTemplate.1929447243"
            />
          </span>
        </div>
        <div className="flex-1 flex flex-row gap-4">
          <div className="flex flex-col gap-2">
            <span className="font-medium text-sm text-gray-700">
              <FormattedMessage
                defaultMessage="Số hàng tối đa {value}"
                id="screens.presentations.components.TableConfigTemplate.281785528"
                values={{
                  value: '(4-10)',
                }}
              />
            </span>
            <Form.Field
              name={[
                'presentations',
                focusedIndex,
                'content',
                'metadata',
                'layout',
                'table',
                'custom',
                device,
                'column',
              ]}
              trigger="onBlurInput"
              preserve
            >
              <NumberInput
                precision={0}
                size="sm"
                keyboard
                controls
                placeholder=""
                clearable={false}
                min={4}
                max={10}
                defaultValue={7}
              />
            </Form.Field>
          </div>
        </div>
      </div>
      {isShowCamera && (
        <div className="flex flex-col gap-2 p-3 w-full">
          <div className="flex flex-row gap-2 items-center">
            <WebcamIcon size={18} className="text-gray-700 flex-shrink-0" />
            <span className="flex-1 min-w-0 font-medium text-sm text-gray-700">
              <FormattedMessage
                defaultMessage="Bố cục camera"
                id="screens.presentations.components.GridConfigTemplate.910370731"
              />
            </span>
          </div>
          <div className="flex-1 flex flex-row gap-4">
            <div className="flex flex-col gap-2">
              <span className="font-medium text-sm text-gray-700">
                <FormattedMessage
                  defaultMessage="Số hàng tối đa (1-3)"
                  id="screens.presentations.components.GridConfigTemplate.1696341717"
                />
              </span>
              <Form.Field
                name={[
                  'presentations',
                  focusedIndex,
                  'content',
                  'metadata',
                  'layout',
                  'table',
                  'custom',
                  device,
                  'cameraPerPage',
                ]}
                trigger="onBlurInput"
                preserve
              >
                <NumberInput
                  precision={0}
                  size="sm"
                  keyboard
                  controls
                  placeholder=""
                  clearable={false}
                  min={1}
                  max={3}
                  defaultValue={3}
                />
              </Form.Field>
            </div>
          </div>
        </div>
      )}
      <div className={cn('flex flex-row gap-1 p-3 h-[144px] w-full', { 'flex-col': device !== Tag.desktop })}>
        <Form.Field
          name={['presentations', focusedIndex, 'content', 'metadata', 'layout', 'table', 'custom', device, 'column']}
        >
          {(controlColumn) => {
            const gridItem = 4 * (Number(controlColumn.value) ?? 3);
            return (
              <div
                className="flex-[3] min-w-0 grid gap-[2px] p-2 rounded bg-gray-100"
                style={{
                  gridTemplateColumns: 'repeat(4, minmax(0, 1fr))',
                  gridTemplateRows: `repeat(${controlColumn.value ?? 7}, minmax(0, 1fr))`,
                }}
              >
                {Array.from({ length: gridItem }).map((item, index) => (
                  <div key={index} className="bg-gray-200 rounded-[2px]" />
                ))}
              </div>
            );
          }}
        </Form.Field>
        {isShowCamera && (
          <div className={cn('flex-[1] min-w-0 h-auto flex flex-col gap-1', { 'flex-row': device !== Tag.desktop })}>
            <Form.Field
              name={[
                'presentations',
                focusedIndex,
                'content',
                'metadata',
                'layout',
                'table',
                'custom',
                device,
                'cameraPerPage',
              ]}
              preserve
            >
              {(controlCameraPerPage) => {
                return (
                  <>
                    {Array.from({ length: controlCameraPerPage.value }).map((_, index) => (
                      <div
                        key={index}
                        className={cn('bg-gray-100 rounded flex-1 h-full flex items-center justify-center', {
                          'min-h-[32px]': device !== Tag.desktop,
                        })}
                      >
                        <WebcamIcon size={20} className="text-gray-500 flex-shrink-0" />
                      </div>
                    ))}
                  </>
                );
              }}
            </Form.Field>
          </div>
        )}
      </div>
    </GroupBox>
  );
};
