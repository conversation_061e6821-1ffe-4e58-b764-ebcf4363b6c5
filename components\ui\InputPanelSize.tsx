import { cn } from '@/utils/tailwind';
import { ArrowsHorizontalIcon, ArrowsVerticalIcon, CaretUpDownIcon } from '@phosphor-icons/react';
import { ValueType } from 'rc-input-number';
import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { defineMessages, useIntl } from 'react-intl';
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  OptionItemType,
  Separator,
  toast
} from 'ui-components';
import IconCommon from '../sessions/IconLayout';
import { InputNumberProps, NumberInput } from './NumberInput';

export const InputPanelSize = ({
  className,
  disabled,
  value,
  onChange,
  initValue = [0, 0],
  unit = 'px',
  onBlur,
}: {
  className?: string;
  disabled?: boolean;
  value?: ValueType[];
  onChange?: (value: ValueType[]) => void;
  initValue?: ValueType[];
  unit?: string;
  onBlur?: (value: ValueType[]) => void;
}) => {
  const intl = useIntl();
  const [_value, setValue] = useState(() => {
    return value !== undefined && value !== null ? value : initValue;
  });
  const suffix = useMemo(() => <span className="font-normal text-sm text-gray-700">{unit}</span>, [unit]);

  useEffect(() => {
    setValue(value !== undefined && value !== null ? value : initValue);
  }, [value, initValue]);

  const handleEmptyValue = (index: number, currentValue: ValueType | null) => {
    if (
      currentValue === null ||
      currentValue === undefined ||
      (typeof currentValue === 'string' && currentValue.length === 0)
    ) {
      return initValue[index];
    }
    return Math.round(+currentValue);
  };

  return (
    <div
      className={cn(
        'w-full h-11 flex flex-row items-center justify-between border border-gray-200 rounded-lg overflow-hidden bg-gray-200 hover:ring-2 transition-all duration-300 ease-in',
        { 'bg-gray-50 hover:bg-gray-50 hover:ring-0 active:ring-0': disabled },
        className,
      )}
    >
      {_value.map((val, index) => {
        return (
          <Fragment key={index}>
            <NumberInput
              placeholder=""
              precision={0}
              keyboard
              classNameWrapper={cn('flex-1 h-full rounded-none border-none hover:ring-0 p-2', {
                'flex-1 h-full rounded-none border-none hover:ring-0 p-2': index === 1,
              })}
              defaultValue={initValue[index]}
              suffix={suffix}
              disabled={disabled}
              value={val}
              clearable={false}
              onChange={(val: ValueType | null) => {
                const oldValue = _value[_value.length - 1 - index] || initValue[_value.length - 1 - index];
                const newValues = index === 0 ? [val ?? null, oldValue] : [oldValue, val ?? null];
                setValue(newValues as ValueType[]);
                onChange?.(newValues as ValueType[]);
              }}
              onBlurInput={(value) => {
                const newValue = handleEmptyValue(index, value);
                const oldValue = _value[_value.length - 1 - index] || initValue[_value.length - 1 - index];
                const newValues = index === 0 ? [+newValue, +oldValue] : [+oldValue, +newValue];
                setValue(newValues as ValueType[]);
                onBlur?.(newValues as ValueType[]);
              }}
              onKeyDown={(event) => {
                const allowedKeys = ['Backspace', 'Tab', 'Delete', 'ArrowLeft', 'ArrowRight'];

                if (!/^[0-9]$/.test(event.key) && !allowedKeys.includes(event.key)) {
                  event.preventDefault();
                }
              }}
            />
            {index === 0 && <Separator orientation="vertical" className="h-full border-l border-gray-200" />}
          </Fragment>
        );
      })}
    </div>
  );
};

const message = defineMessages({
  resizeError: {
    defaultMessage: 'Kích thước không hợp lệ. Vui lòng nhập giá trị từ 320 px đến 3840 px.',
    id: 'app.led.[led_key].(configure).presentations.page.1682010608',
  },
  submitSuccess: {
    defaultMessage: 'Đã lưu cấu hình trang trình chiếu thành công.',
    id: 'app.led.[led_key].(configure).presentations.page.129890065',
  },
  submitError: {
    defaultMessage: 'Không thể lưu cấu hình trang trình chiếu. Vui lòng thử lại sau.',
    id: 'app.led.[led_key].(configure).presentations.page.1103570150',
  },
  saveError: {
    defaultMessage: 'Không thể chuyển sang cấu hình giá trị mặc định. Vui lòng thử lại sau.',
    id: 'app.led.[led_key].(configure).presentations.page.1962099229',
  },
});

export const InputPanelSizeGroup = ({
  className,
  disabled,
  value,
  onChange,
  onBlur,
  initValue = [0, 0],
  unit = 'px',
  selectConfig,
  ...props
}: {
  className?: string;
  disabled?: boolean;
  value?: ValueType[];
  onChange?: (value: ValueType[]) => void;
  onBlur?: (value: ValueType[]) => void;
  initValue?: ValueType[];
  unit?: string;
  selectConfig?:
    | false
    | {
        selectOptions?: OptionItemType[];
      };
} & Partial<Omit<InputNumberProps, 'value' | 'onChange' | 'onBlur' | 'disabled' | 'className'>>) => {
  const intl = useIntl();
  const [_value, setValue] = useState(() => {
    return value !== undefined && value !== null ? value : initValue;
  });

  useEffect(() => {
    setValue(value !== undefined && value !== null ? value : initValue);
  }, [value, initValue]);

  const handleEmptyValue = useCallback(
    (index: number, currentValue: ValueType | null) => {
      if (
        currentValue === null ||
        currentValue === undefined ||
        (typeof currentValue === 'string' && currentValue.length === 0)
      ) {
        return initValue[index];
      }
      return Math.round(+currentValue);
    },
    [initValue],
  );

  const validateDimension = useCallback((value: number, min: number, max: number, defaultValue: number) => {
    if (value < min || value > max) {
      return defaultValue;
    }
    return value;
  }, []);

  return (
    <div
      className={cn(
        'w-full h-11 flex flex-row items-center gap-[1px] justify-between border border-gray-200 rounded-lg overflow-hidden bg-gray-200 hover:ring-2 transition-all duration-300 ease-in',
        { 'bg-gray-50 hover:bg-gray-50 hover:ring-0 active:ring-0': disabled },
        className,
      )}
    >
      {_value.map((val, index) => {
        const prefix =
          index === 0 ? (
            <ArrowsHorizontalIcon size={16} weight="regular" className="flex-shrink-0 text-current" />
          ) : (
            <ArrowsVerticalIcon size={16} weight="regular" className="flex-shrink-0 text-current" />
          );
        return (
          <Fragment key={index}>
            <NumberInput
              {...props}
              placeholder=""
              keyboard
              classNameWrapper={cn('flex-1 w-[120px] h-full rounded-none border-none hover:ring-0 p-2', {
                'flex-1 h-full rounded-none border-none hover:ring-0 p-2': index === 1,
              })}
              precision={0}
              prefix={<span className="font-normal text-sm text-gray-700">{prefix}</span>}
              suffix={<span className="font-normal text-sm text-gray-700">{unit}</span>}
              disabled={disabled}
              value={val}
              min={320}
              max={3840}
              defaultValue={initValue[index]}
              clearable={false}
              controls={false}
              onChange={(val: ValueType | null) => {
                const oldValue = _value[_value.length - 1 - index] || initValue[_value.length - 1 - index];
                const newValues = index === 0 ? [val ?? null, oldValue] : [oldValue, val ?? null];
                setValue(newValues as ValueType[]);
                onChange?.(newValues as ValueType[]);
              }}
              onBlur={(event) => {
                const inputVal = parseFloat(event.target.value);
                let newValue = +inputVal;
                if (isNaN(inputVal) || inputVal === null || inputVal === undefined) {
                  newValue = +initValue[index];
                } else {
                  newValue = +handleEmptyValue(index, inputVal);
                }
                const oldValue = _value[_value.length - 1 - index] || initValue[_value.length - 1 - index];
                const newValues = index === 0 ? [+newValue, +oldValue] : [+oldValue, +newValue];
                const [width, height] = newValues;
                const validatedWidth = validateDimension(Number(width), 320, 3840, 1920);
                const validatedHeight = validateDimension(Number(height), 320, 3840, 1080);
                if (validatedWidth !== Number(width) || validatedHeight !== Number(height)) {
                  toast({
                    type: 'error',
                    title: intl.formatMessage(message.resizeError),
                    options: {
                      position: 'top-center',
                    },
                  });
                }
                setValue([validatedWidth, validatedHeight] as ValueType[]);
                onBlur?.([validatedWidth, validatedHeight] as ValueType[]);
              }}
              onKeyDown={(event) => {
                const allowedKeys = ['Backspace', 'Tab', 'Delete', 'ArrowLeft', 'ArrowRight'];
                if (!/^[0-9]$/.test(event.key) && !allowedKeys.includes(event.key)) {
                  event.preventDefault();
                }
              }}
            />
          </Fragment>
        );
      })}
      {Boolean(selectConfig) && typeof selectConfig === 'object' && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button icon type="button" variant="gray" className="bg-white h-full rounded-none">
              <CaretUpDownIcon size={20} weight="regular" className="flex-shrink-0 text-current text-gray-700" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="bg-white rounded-xl p-2 min-w-[180px]">
            {selectConfig.selectOptions?.map((option) => {
              return (
                <DropdownMenuItem
                  key={option.value}
                  className="cursor-pointer hover:bg-gray-100 rounded-lg p-2 font-normal text-sm text-gray-700 flex flex-row gap-3"
                  onSelect={() => {
                    const arr = option.value.split('x').map(Number);
                    setValue(arr);
                    if (onChange) onChange(arr);
                    if (onBlur) onBlur(arr);
                  }}
                >
                  <IconCommon size={20} name={option.prefix as string} className="flex-shrink-0 text-current" />
                  {option.title}
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
};
