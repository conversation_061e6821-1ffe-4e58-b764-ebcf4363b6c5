import { DATE_FORMAT, GridView } from '@/constants';
import { useLedConfigStores, useLedStores, useSlideConfigStores } from '@/stores';
import { EContentType, HistoryMetadata, IConfigLayoutData, SlideConfigDto } from '@/types/slide';
import { useCallback, useEffect, useMemo } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { cn } from '@/utils/tailwind';
import useEmblaCarousel from 'embla-carousel-react';
import Fade from 'embla-carousel-fade';
import NotConfigure from '../components/NotConfigure';
import { EmblaCarouselType } from 'embla-carousel';
import TableHistories from '../components/TableHistories';
import { usePagination } from '@/hooks/usePagination';
import { buildPages } from '@/utils/slide';
import { ResponsiveTextWithOverride } from '../components/ResponsiveTextWithOverride';
import { useCreateClient } from '@/api/useCreateClient';

type Props = {
  dataSlide: SlideConfigDto;
  isIframeReady: boolean;
  setEmblaApi: (emblaApi: EmblaCarouselType) => void;
};

enum Tag {
  desktop = 'desktop',
  tablet = 'tablet',
  mobile = 'mobile',
}

export default function ContentHistories({ dataSlide, isIframeReady, setEmblaApi }: Props) {
  const dataLedDetail = useLedStores((store) => store.dataLedDetail);
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const config = useSlideConfigStores((store) => store.config);
  const decimalFormat = dataLedDetail?.decimal ?? 2;
  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );
  const { width } = config;
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: false, watchDrag: false, containScroll: false }, [Fade()]);
  const { selectedIndex } = usePagination(emblaApi);

  const dataContentSlide = useMemo(() => dataSlide.content, [dataSlide]) as {
    type: EContentType.histories;
    metadata: HistoryMetadata;
  };

  const slide = useMemo(() => dataSlide, [dataSlide]);
  const metadata = slide?.content?.metadata as HistoryMetadata;

  const getDeviceFromSize = useCallback((width: number): Tag => {
    if (width <= 480) {
      return Tag.mobile;
    }
    if (width > 480 && width <= 960) {
      return Tag.tablet;
    }
    return Tag.desktop;
  }, []);

  const device = useMemo(() => getDeviceFromSize(width), [width]);

  const configGrid = useMemo(() => {
    const view = dataContentSlide.metadata?.layout.view;
    if (view === GridView.general) {
      const settings = dataContentSlide.metadata?.layout.settings as IConfigLayoutData;
      return {
        column: settings?.column ?? 8,
        row: settings?.row ?? 7,
      };
    }

    const settingsDevice = dataContentSlide.metadata?.layout.settings as {
      desktop: IConfigLayoutData;
      mobile: IConfigLayoutData;
      tablet: IConfigLayoutData;
    };
    return {
      column: settingsDevice?.[device]?.column ?? 8,
      row: settingsDevice?.[device]?.row ?? 7,
    };
  }, [dataContentSlide, device]);

  const dataStations = useMemo(
    () => dataLedDetail?.dataStations.find((station) => station.key === dataContentSlide.metadata?.station),
    [dataLedDetail, dataContentSlide],
  );

  const pages = useMemo(() => {
    // Ensure we have all required data before calling buildPages
    if (!dataStations || !dataContentSlide.metadata) {
      return [];
    }

    return buildPages({
      dataContentSlide: dataContentSlide.metadata,
      row: configGrid.row,
      column: configGrid.column,
      intl,
      dataStations: dataStations,
      decimalFormat,
      dateFormat: DATE_FORMAT,
    });
  }, [
    // Only include dependencies that actually affect the output
    configGrid.row,
    configGrid.column,
    dataContentSlide.metadata,
    intl,
    dataStations,
    decimalFormat,
  ]);
  useEffect(() => {
    if (emblaApi && isIframeReady) {
      setEmblaApi(emblaApi);
    }
  }, [emblaApi, pages, setEmblaApi, isIframeReady, dataContentSlide]);

  return (
    <div className="h-full w-full overflow-hidden" ref={emblaRef}>
      <div className={cn('embla__container flex h-full w-full')}>
        {pages.length > 0 ? (
          pages.map((page, index) => (
            <div key={index} className="embla__slide flex-[0_0_100%] min-w-0 h-full">
              <div
                className="flex-1 h-full w-full flex flex-col gap-[2px]"
                style={{
                  backgroundColor: defaultConfig.slideConfig?.borderColor,
                }}
              >
                {(dataContentSlide.metadata?.config.showOptions.isShowStationName ||
                  dataContentSlide.metadata?.config.warningColor.isThresholdApproaching ||
                  dataContentSlide.metadata?.config.warningColor.isThresholdExceeded) && (
                  <div
                    className={cn('flex-shrink-0 flex flex-col w-full', 'sm:flex-row sm:items-center')}
                    style={{
                      backgroundColor: defaultConfig.slideConfig?.background,
                    }}
                  >
                    <div
                      className={cn(
                        'flex-1 min-h-0 px-1 flex items-center sm:h-full sm:min-w-0',
                        'sm:px-2',
                        'md:px-3',
                        'lg:px-[10px]',
                        'xl:px-[14px]',
                        '2xl:px-4',
                        '3xl:px-5',
                        {
                          'hidden sm:opacity-0 sm:flex':
                            !dataContentSlide.metadata?.config.showOptions.isShowStationName,
                        },
                      )}
                    >
                      <div className="h-full w-full flex flex-col justify-center">
                        <ResponsiveTextWithOverride
                          value={dataContentSlide.metadata?.fontSize?.config?.stationName}
                          className="font-semibold truncate"
                        >
                          {dataStations?.name?.[locale]}
                        </ResponsiveTextWithOverride>
                      </div>
                    </div>
                    {(dataContentSlide.metadata?.config.warningColor?.isThresholdApproaching ||
                      dataContentSlide.metadata?.config.warningColor?.isThresholdExceeded) && (
                      <div
                        className={cn(
                          'flex-shrink-0 flex flex-row gap-1 px-1',
                          'sm:px-2 sm:gap-2',
                          'md:px-3 md:gap-3',
                          'lg:px-[10px]',
                          'xl:px-[14px]',
                          '2xl:px-4',
                          '3xl:px-5',
                          'sm:h-full sm:flex sm:justify-end',
                        )}
                      >
                        <ResponsiveTextWithOverride
                          value={dataContentSlide.metadata?.fontSize?.config?.legendWarning}
                          className="h-full w-fit flex flex-row items-center font-normal"
                        >
                          <svg
                            height={'1.2em'}
                            width={'1em'}
                            className="h-[1.2em] w-[1em] flex-shrink-0 text-[100%]"
                            viewBox="0 0 1 6"
                            xmlns="http://www.w3.org/2000/svg"
                            style={{
                              fill: defaultConfig.init?.colors.color_within_limit,
                            }}
                          >
                            <rect x="0" y="0" width="1.5" height="6" />
                          </svg>
                          <span className="font-normal w-fit text-current text-[100%]">
                            <FormattedMessage
                              defaultMessage="Trong ngưỡng"
                              id="screens.presentations.contentType.ContentRealtime.1604652103"
                            />
                          </span>
                        </ResponsiveTextWithOverride>
                        {dataContentSlide.metadata?.config.warningColor?.isThresholdApproaching && (
                          <ResponsiveTextWithOverride
                            className="h-full w-fit flex flex-row items-center font-normal"
                            value={dataContentSlide.metadata?.fontSize?.config?.legendWarning}
                          >
                            <svg
                              height={'1.2em'}
                              width={'1em'}
                              className="h-[1.2em] w-[1em] flex-shrink-0 text-[100%]"
                              viewBox="0 0 1 6"
                              xmlns="http://www.w3.org/2000/svg"
                              style={{
                                fill: defaultConfig.init?.colors.color_near_limit,
                              }}
                            >
                              <rect x="0" y="0" width="1.5" height="6" />
                            </svg>
                            <span className="font-normal w-fit text-current text-[100%]">
                              <FormattedMessage
                                defaultMessage="Chuẩn bị vượt"
                                id="screens.presentations.contentType.ContentRealtime.130682665"
                              />
                            </span>
                          </ResponsiveTextWithOverride>
                        )}
                        {dataContentSlide.metadata?.config.warningColor?.isThresholdExceeded && (
                          <ResponsiveTextWithOverride
                            className="h-full w-fit flex flex-row items-center font-normal"
                            value={dataContentSlide.metadata?.fontSize?.config?.legendWarning}
                          >
                            <svg
                              height={'1.2em'}
                              width={'1em'}
                              className="h-[1.2em] w-[1em] flex-shrink-0 text-[100%]"
                              viewBox="0 0 1 6"
                              xmlns="http://www.w3.org/2000/svg"
                              style={{
                                fill: defaultConfig.init?.colors.color_over_limit,
                              }}
                            >
                              <rect x="0" y="0" width="1.5" height="6" />
                            </svg>
                            <span className="font-normal w-fit text-current text-[100%]">
                              <FormattedMessage
                                defaultMessage="Vượt ngưỡng"
                                id="screens.presentations.contentType.ContentRealtime.802954264"
                              />
                            </span>
                          </ResponsiveTextWithOverride>
                        )}
                      </div>
                    )}
                  </div>
                )}
                <div
                  className="flex-1 min-h-0"
                  style={{
                    backgroundColor: defaultConfig.slideConfig?.background,
                  }}
                >
                  <TableHistories
                    dataSlide={
                      slide as {
                        content: {
                          metadata: HistoryMetadata;
                        };
                      }
                    }
                    device={device}
                    dataStations={dataStations}
                    currentPage={page}
                  />
                </div>
              </div>
            </div>
          ))
        ) : (
          <NotConfigure />
        )}
      </div>
    </div>
  );
}
