'use client';

import { useCreateClient } from '@/api/useCreateClient';
import InputLanguage from '@/components/ui/InputLanguage/InputLanguage';
import { PaletteColorPicker } from '@/components/ui/PaletteColorPicker';
import UploadFile from '@/components/ui/UploadFile';
import { usePreventNavigation } from '@/hooks/usePreventNavigation';
import { useLedConfigStores } from '@/stores';
import { IInitData } from '@/types';
import { cn } from '@/utils/tailwind';
import { ArrowCounterClockwiseIcon, InfoIcon } from '@phosphor-icons/react';
import { use, useCallback, useEffect, useMemo, useState } from 'react';
import { defineMessages, FormattedMessage, useIntl } from 'react-intl';
import { Alert, Button, Form, toast, Tooltip, TooltipContent, TooltipTrigger } from 'ui-components';

const messageToast = defineMessages({
  success: {
    defaultMessage: '<PERSON><PERSON> lưu cấu hình dữ liệu mặc định thành công.',
    id: 'app.led.[led_key].(configure).config.default-data.page.1194256638',
  },
  fail: {
    defaultMessage: 'Không thể lưu cấu hình dữ liệu mặc định. Vui lòng thử lại sau.',
    id: 'app.led.[led_key].(configure).config.default-data.page.1132865453',
  },
});

const initColor = {
  color_within_limit: '#FFFFFF',
  color_near_limit: '#F59E0B',
  color_over_limit: '#EF4444',
  color_disconnected: '#71717A',
};

export default function LedConfigDefaultData({ params }: { params: Promise<{ led_key: string }> }) {
  const { led_key } = use(params) as { led_key: string };
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const intl = useIntl();
  const { ledConfig } = useCreateClient();
  const [form] = Form.useForm();
  const [formChange, setFormChange] = useState(false);
  const { mutateAsync: mutateAsyncConfigDefault, isPending: isPendingMutateAsync } = ledConfig.configDefaultData({
    invalidateQueries: [
      {
        enable: true,
      },
      {
        enable: true,
        queryKey: ['LedServiceClient', 'getLedConfig', led_key],
        exact: true,
      },
    ],
  });

  usePreventNavigation({
    isDirty: formChange,
  });
  const initFormValue = useMemo(() => {
    return {
      title: dataConfig?.init.title || { vi: '', en: '' },
      sub_title: dataConfig?.init.sub_title || { vi: '', en: '' },
      avatar: dataConfig?.init.avatar || '',
      colors: {
        color_within_limit: dataConfig?.init.colors.color_within_limit || initColor.color_within_limit,
        color_near_limit: dataConfig?.init.colors.color_near_limit || initColor.color_near_limit,
        color_over_limit: dataConfig?.init.colors.color_over_limit || initColor.color_over_limit,
        color_disconnected: dataConfig?.init.colors.color_disconnected || initColor.color_disconnected,
      },
    };
  }, []);

  useEffect(() => {
    form?.setFieldsValue(initFormValue);
  }, [initFormValue, form]);

  const onSubmit = async (values: IInitData) => {
    try {
      await mutateAsyncConfigDefault([led_key, values]);
      toast({
        type: 'success',
        title: intl.formatMessage(messageToast.success),
        options: {
          position: 'top-center',
        },
      });
      setFormChange(false);
    } catch (error) {
      console.log({ error });
      toast({
        type: 'error',
        title: intl.formatMessage(messageToast.fail),
        options: {
          position: 'top-center',
        },
      });
    }
  };

  const handleResetColor = useCallback(() => {
    form?.setFieldsValue({
      colors: initColor,
    });
  }, []);

  return (
    <Form
      form={form}
      initialValues={initFormValue}
      onFinish={onSubmit}
      onFieldsChange={(changedFields, allFields) => {
        const isChange = allFields.some((field) => {
          return field.touched;
        });
        setFormChange(isChange);
      }}
      name="default-data-form"
      className="flex-1 flex flex-col w-full min-h-0 items-center"
    >
      <div className="w-[600px] flex-1 min-h-0 max-h-fit rounded-3xl flex flex-col gap-4 bg-white">
        <span className="font-semibold text-lg text-gray-700 flex-shrink-0 px-6 pt-6">
          <FormattedMessage
            defaultMessage="Cấu hình dữ liệu mặc định"
            id="app.led.[led_key].(configure).config.default-data.page.164931504"
          />
        </span>
        <div className="flex-1 min-h-0 max-h-full overflow-auto flex flex-col gap-4 px-6">
          <Alert variant="primary" isVisible>
            <FormattedMessage
              defaultMessage="Khi bạn cấu hình dữ liệu mặc định, nếu các trường dữ liệu không có nội dung, giá trị mặc định sẽ thay thế hiển thị."
              id="app.led.[led_key].(configure).config.default-data.page.1079198337"
            />
          </Alert>
          <Form.Field name="avatar">
            <UploadFile />
          </Form.Field>
          <div className="flex flex-col gap-2">
            <div className="flex flex-row gap-2 items-center">
              <span className="font-medium text-sm text-gray-700">
                <FormattedMessage
                  defaultMessage="Tiêu đề trang trình chiếu"
                  id="app.led.[led_key].(configure).config.default-data.page.1995737682"
                />
              </span>
              <Tooltip>
                <TooltipTrigger asChild>
                  <InfoIcon size={18} weight="regular" className="text-gray-500 flex-shrink-0 cursor-pointer" />
                </TooltipTrigger>
                <TooltipContent
                  className="p-2 text-gray-300 bg-gray-700 rounded-lg shadow font-normal text-xs max-w-[269px]"
                  side="right"
                  align="start"
                >
                  <FormattedMessage
                    defaultMessage="Tiêu đề trang trình chiếu không được nhập quá 72 ký tự."
                    id="app.led.[led_key].(configure).config.default-data.page.775148511"
                  />
                </TooltipContent>
              </Tooltip>
            </div>
            <Form.Field name="title">
              {(control) => {
                return (
                  <div className="h-full w-full">
                    <InputLanguage
                      placeholder={intl.formatMessage({
                        defaultMessage: 'Nhập tiêu đề',
                        id: 'app.led.[led_key].(configure).config.default-data.page.412702605',
                      })}
                      name="title"
                      value={control.value}
                      onChange={control.onChange}
                      maxLenght={72}
                    />
                  </div>
                );
              }}
            </Form.Field>
          </div>
          <div className="flex flex-col gap-2">
            <div className="flex flex-row gap-2 items-center">
              <span className="font-medium text-sm text-gray-700">
                <FormattedMessage
                  defaultMessage="Phụ đề trang trình chiếu"
                  id="app.led.[led_key].(configure).config.default-data.page.1715188257"
                />
              </span>
              <Tooltip>
                <TooltipTrigger asChild>
                  <InfoIcon size={18} weight="regular" className="text-gray-500 flex-shrink-0 cursor-pointer" />
                </TooltipTrigger>
                <TooltipContent
                  className="p-2 text-gray-300 bg-gray-700 rounded-lg shadow font-normal text-xs max-w-[269px]"
                  side="right"
                  align="start"
                >
                  <FormattedMessage
                    defaultMessage="Phụ đề trang trình chiếu không được nhập quá 100 ký tự."
                    id="app.led.[led_key].(configure).config.default-data.page.1879174810"
                  />
                </TooltipContent>
              </Tooltip>
            </div>
            <Form.Field name="sub_title">
              {(control) => {
                return (
                  <div className="h-full w-full">
                    <InputLanguage
                      placeholder={intl.formatMessage({
                        defaultMessage: 'Nhập phụ đề',
                        id: 'app.led.[led_key].(configure).config.default-data.page.1259021402',
                      })}
                      name="sub_title"
                      value={control.value}
                      onChange={control.onChange}
                      maxLenght={100}
                    />
                  </div>
                );
              }}
            </Form.Field>
          </div>
          <div className="flex flex-col gap-2">
            <div className="w-full flex-shrink-0 flex flex-row justify-normal items-center">
              <span className="flex-1 min-w-0 font-medium text-sm text-gray-700">
                <FormattedMessage
                  defaultMessage="Màu cảnh báo"
                  id="app.led.[led_key].(configure).config.default-data.page.1573266568"
                />
              </span>
              <Button
                type="button"
                className="flex-shrink-0 flex flex-row gap-2 items-center text-primary-500 h-[21px] rounded-none bg-white hover:bg-transparent active:bg-transparent focus:bg-transparent"
                onClick={handleResetColor}
              >
                <ArrowCounterClockwiseIcon size={20} weight="regular" className="flex-shrink-0 text-current" />
                <span className="flex-1 min-w-0 font-medium text-sm leading-[150%]">
                  <FormattedMessage
                    defaultMessage="Mặc định"
                    id="app.led.[led_key].(configure).config.default-data.page.1243419333"
                  />
                </span>
              </Button>
            </div>
            <div
              className={cn(
                'flex flex-col border border-gray-200 rounded-xl overflow-hidden',
                '[&_>:not(:first-child)]:border-t [&_>:not(:first-child)]:border-gray-200',
              )}
            >
              <div className="flex flex-row">
                <div className="h-11 flex-1 min-w-0 p-3 border-r border-gray-200">
                  <span className="font-normal text-sm text-gray-700">
                    <FormattedMessage
                      defaultMessage="Trong ngưỡng"
                      id="app.led.[led_key].(configure).config.default-data.page.1604652103"
                    />
                  </span>
                </div>
                <div className="w-[160px] flex-shrink-0">
                  <Form.Field name={['colors', 'color_within_limit']}>
                    <PaletteColorPicker className="border-none" />
                  </Form.Field>
                </div>
              </div>
              <div className="flex flex-row">
                <div className="h-11 flex-1 min-w-0 p-3 border-r border-gray-200">
                  <span className="font-normal text-sm text-gray-700">
                    <FormattedMessage
                      defaultMessage="Chuẩn bị vượt ngưỡng"
                      id="app.led.[led_key].(configure).config.default-data.page.163121484"
                    />
                  </span>
                </div>
                <div className="w-[160px] flex-shrink-0">
                  <Form.Field name={['colors', 'color_near_limit']}>
                    <PaletteColorPicker className="border-none" />
                  </Form.Field>
                </div>
              </div>
              <div className="flex flex-row">
                <div className="h-11 flex-1 min-w-0 p-3 border-r border-gray-200">
                  <span className="font-normal text-sm text-gray-700">
                    <FormattedMessage
                      defaultMessage="Vượt ngưỡng"
                      id="app.led.[led_key].(configure).config.default-data.page.802954264"
                    />
                  </span>
                </div>
                <div className="w-[160px] flex-shrink-0">
                  <Form.Field name={['colors', 'color_over_limit']}>
                    <PaletteColorPicker className="border-none" />
                  </Form.Field>
                </div>
              </div>
              <div className="flex flex-row">
                <div className="h-11 flex-1 min-w-0 p-3 border-r border-gray-200">
                  <span className="font-normal text-sm text-gray-700">
                    <FormattedMessage
                      defaultMessage="Mất tín hiệu"
                      description=""
                      id="app.led.[led_key].(configure).config.default-data.page.393704478"
                    />
                  </span>
                </div>
                <div className="w-[160px] flex-shrink-0">
                  <Form.Field name={['colors', 'color_disconnected']}>
                    <PaletteColorPicker className="border-none" />
                  </Form.Field>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="px-6 pb-6 w-full flex">
          <Button className="w-full" type="submit" disabled={isPendingMutateAsync}>
            <FormattedMessage
              defaultMessage="Lưu cấu hình"
              id="app.led.[led_key].(configure).config.slide-shows.page.1043319462"
            />
          </Button>
        </div>
      </div>
    </Form>
  );
}
