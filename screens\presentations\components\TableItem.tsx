import { useStateThreshold } from '@/hooks/useStateThreshold';
import { useLedConfigStores, useLedStores } from '@/stores';
import { ICameraList, IDataStation, IMeasuring } from '@/types';
import { EStatus, ISlide, RealtimeContent, RealTimeMetadata, SlideConfigDto } from '@/types/slide';
import { cn } from '@/utils/tailwind';
import { useMemo } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import BoxCamera from './BoxCamera';
import { ResponsiveTextWithOverride } from './ResponsiveTextWithOverride';
import { getLimitText } from '@/utils/slide';

type Props = {
  configGrid: {
    row: number;
    column: number;
    cameraPerPage: number;
  };
  currentItems: string[];
  dataStations: IDataStation;
  dataSlide: SlideConfigDto;
  cameras: ICameraList[];
  camera: ICameraList[] | undefined;
};

export default function TableItem({ configGrid, currentItems, dataStations, dataSlide, cameras, camera }: Props) {
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  if (!dataConfig) return null;

  const dataMetadata = dataSlide.content.metadata as RealTimeMetadata;
  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );

  return (
    <div
      className={cn('flex flex-col gap-[2px] h-full w-full landscape:flex-row')}
      style={{ backgroundColor: defaultConfig.slideConfig?.borderColor }}
    >
      <div className={cn('flex-1 min-h-0 w-full overflow-hidden', 'landscape:h-full landscape:min-w-0')}>
        <table className="h-full w-full">
          <thead>
            <tr>
              <ResponsiveTextWithOverride
                value={dataMetadata.layout.config.fontSize.config.headerTable}
                as="th"
                align="left"
                className={cn(
                  'font-bold',
                  'px-1',
                  'sm:px-2',
                  'md:px-3',
                  'lg:px-[10px]',
                  'xl:px-[14px]',
                  '2xl:px-4',
                  '3xl:px-5 truncate',
                )}
                style={{
                  maxWidth: 'calc(80vw / 4)',
                }}
              >
                <FormattedMessage
                  defaultMessage="Tên thông số"
                  id="screens.presentations.contentType.ContentRealtime.1436455565"
                />
              </ResponsiveTextWithOverride>
              <ResponsiveTextWithOverride
                value={dataMetadata.layout.config.fontSize.config.headerTable}
                as="th"
                align="right"
                className={cn(
                  'font-bold',
                  'px-1',
                  'sm:px-2',
                  'md:px-3',
                  'lg:px-[10px]',
                  'xl:px-[14px]',
                  '2xl:px-4',
                  '3xl:px-5 truncate',
                )}
                style={{
                  maxWidth: 'calc(80vw / 4)',
                }}
              >
                <FormattedMessage
                  defaultMessage="Giá trị"
                  id="screens.presentations.contentType.ContentRealtime.1603181196"
                />
              </ResponsiveTextWithOverride>
              <ResponsiveTextWithOverride
                value={dataMetadata.layout.config.fontSize.config.headerTable}
                as="th"
                align="left"
                className={cn(
                  'font-bold',
                  'px-1',
                  'sm:px-2',
                  'md:px-3',
                  'lg:px-[10px]',
                  'xl:px-[14px]',
                  '2xl:px-4',
                  '3xl:px-5 truncate',
                )}
                style={{
                  maxWidth: 'calc(80vw / 4)',
                }}
              >
                <FormattedMessage
                  defaultMessage="Đơn vị"
                  id="screens.presentations.contentType.ContentRealtime.414377960"
                />
              </ResponsiveTextWithOverride>
              <ResponsiveTextWithOverride
                value={dataMetadata.layout.config.fontSize.config.headerTable}
                as="th"
                align="left"
                className={cn(
                  'font-bold',
                  'px-1',
                  'sm:px-2',
                  'md:px-3',
                  'lg:px-[10px]',
                  'xl:px-[14px]',
                  '2xl:px-4',
                  '3xl:px-5 truncate',
                )}
                style={{
                  maxWidth: 'calc(80vw / 4)',
                }}
              >
                <FormattedMessage
                  defaultMessage="Giới hạn"
                  id="screens.presentations.contentType.ContentRealtime.785718693"
                />
              </ResponsiveTextWithOverride>
            </tr>
          </thead>
          <tbody>
            {Array.from({ length: configGrid.column }).map((i, index) => {
              const dataItem = dataStations?.measuringList
                .map((measure) => {
                  const measuringLogs = dataStations.lastLog?.measuringLogs[measure.key];
                  return {
                    ...measure,
                    value: measuringLogs?.value || null,
                  };
                })
                ?.find((item) => item.key === currentItems[index]) as IMeasuring & {
                value: number | null;
              };

              return (
                <CellItem
                  key={index}
                  currentItems={currentItems}
                  index={index}
                  dataItem={dataItem}
                  dataSlide={dataSlide}
                  configGrid={configGrid}
                />
              );
            })}
          </tbody>
        </table>
      </div>

      {camera !== undefined ? (
        <div
          className={cn(
            'flex-shrink-0 h-[20dvh] min-h-[20dvh] items-center gap-[1px] w-full flex flex-row',
            'landscape:flex-col landscape:h-full landscape:max-w-[25dvw] landscape:w-[25dvw]',
            'mobile-short-height:hidden',
          )}
        >
          {Array.from({ length: configGrid.cameraPerPage }).map((_, index) => {
            return (
              <BoxCamera
                key={index}
                fontSize={dataMetadata.layout.config.fontSize.config.limitThreshold}
                className={cn('flex-1 portrait:min-w-0 portrait:h-full bg-black', 'landscape:min-h-0 landscape:w-full')}
                camera={cameras[index]}
              />
            );
          })}
        </div>
      ) : null}
    </div>
  );
}

type PropsCell = {
  index: number;
  dataItem: IMeasuring & {
    value: number | null;
  };
  currentItems: string[];
  dataSlide: SlideConfigDto;
  className?: string;
  configGrid: {
    row: number;
    column: number;
    cameraPerPage: number;
  };
};

const CellItem = ({ index, dataItem, currentItems, dataSlide, className, configGrid }: PropsCell) => {
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';
  const dataLedDetail = useLedStores((store) => store.dataLedDetail);
  const dataConfig = useLedConfigStores((store) => store.dataConfig);
  const dataMetadata = dataSlide.content.metadata as RealTimeMetadata;

  if (!dataConfig) return null;

  const decimalFormat = dataLedDetail?.decimal ?? 2;
  const defaultConfig = useMemo(
    () => ({
      init: dataConfig?.init,
      slideConfig: dataConfig?.slide_config_default,
    }),
    [dataConfig],
  );

  const { isStateThreshold, styleThreshold } = useStateThreshold(dataItem, dataMetadata);

  return (
    <tr key={index} className={className}>
      <ResponsiveTextWithOverride
        as="td"
        value={dataMetadata.layout.config.fontSize.config.measureCode}
        className={cn(
          'font-bold visible truncate',
          'px-1',
          'sm:px-2',
          'md:px-3',
          'lg:px-[10px]',
          'xl:px-[14px]',
          '2xl:px-4',
          '3xl:px-5',
        )}
        style={{
          maxWidth: 'calc(80vw / 4)',
        }}
      >
        {dataItem?.name[locale] || <span className="invisible">---</span>}
      </ResponsiveTextWithOverride>
      <ResponsiveTextWithOverride
        as="td"
        align="right"
        value={dataMetadata.layout.config.fontSize.config.measureValue}
        className={cn(
          'font-bold visible truncate',
          'px-1',
          'sm:px-2',
          'md:px-3',
          'lg:px-[10px]',
          'xl:px-[14px]',
          '2xl:px-4',
          '3xl:px-5',
        )}
        style={{
          ...(currentItems?.[index] && styleThreshold),
          ...(isStateThreshold === EStatus.Good && { color: defaultConfig?.init.colors.color_within_limit }),
          maxWidth: 'calc(80vw / 4)',
        }}
      >
        {currentItems?.[index] ? (
          dataItem.value !== null ? (
            new Intl.NumberFormat('en-US', {
              minimumFractionDigits: decimalFormat,
              maximumFractionDigits: decimalFormat,
            }).format(dataItem.value)
          ) : (
            '---'
          )
        ) : (
          <span className="invisible">---</span>
        )}
      </ResponsiveTextWithOverride>
      <ResponsiveTextWithOverride
        value={dataMetadata.layout.config.fontSize.config.measureUnit}
        as="td"
        className={cn(
          'font-bold visible truncate',
          'px-1',
          'sm:px-2',
          'md:px-3',
          'lg:px-[10px]',
          'xl:px-[14px]',
          '2xl:px-4',
          '3xl:px-5',
        )}
        style={{
          maxWidth: 'calc(80vw / 4)',
        }}
      >
        {currentItems?.[index] ? dataItem?.unit || '' : <span className="invisible">---</span>}
      </ResponsiveTextWithOverride>
      <ResponsiveTextWithOverride
        value={dataMetadata.layout.config.fontSize.config.limitThreshold}
        as="td"
        className={cn(
          'font-bold visible truncate ',
          'px-1',
          'sm:px-2',
          'md:px-3',
          'lg:px-[10px]',
          'xl:px-[14px]',
          '2xl:px-4',
          '3xl:px-5',
        )}
        style={{
          maxWidth: 'calc(80vw / 4)',
        }}
      >
        {currentItems?.[index] ? getLimitText(dataItem) : <span className="invisible">---</span>}
      </ResponsiveTextWithOverride>
    </tr>
  );
};
