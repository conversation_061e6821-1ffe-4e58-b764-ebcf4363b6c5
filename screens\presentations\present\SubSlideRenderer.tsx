import { ISlide, ISubSlide, SlideConfigDto } from '@/types/slide';
import { cn } from '@/utils/tailwind';
import React, { useEffect, useState, useMemo, useCallback, useRef } from 'react';
import { usePresentationStores } from '@/stores/presentationStores';
import SubSlideErrorBoundary from '@/components/ui/SubSlideErrorBoundary';
import { safeExecuteSync, ErrorType, createError } from '@/utils/errorHandling';
import WrapperSessionPresent from '../sessions/WrapperSessionPresent';
import Wrapper from '../contentType/Wrapper';
import useEmblaCarousel from 'embla-carousel-react';
import { prepareDataSlide } from '@/utils/slide';

interface SubSlideRendererProps {
  slide: SlideConfigDto;
  isIframeReady: boolean;
  isTargeted: boolean;
  className?: string;
}

/**
 * Validates subSlide content structure based on content type
 * @param subSlide - The subSlide to validate
 * @returns Validation result with error details
 */
const validateSubSlideContent = (subSlide: ISubSlide): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!subSlide.content) {
    errors.push('Missing content');
    return { isValid: false, errors };
  }

  const contentType = subSlide.content.type;

  if (contentType === 'realtime') {
    const realtimeContent = subSlide.content;
    if (!realtimeContent.measure || !Array.isArray(realtimeContent.measure)) {
      errors.push('Realtime content missing measure array');
    }
    if (!realtimeContent.station) {
      errors.push('Realtime content missing station');
    }
    if (!realtimeContent.layout) {
      errors.push('Realtime content missing layout');
    }
  } else if (contentType === 'histories') {
    const historiesContent = subSlide.content;
    if (!historiesContent.pageData) {
      errors.push('Histories content missing pageData');
    }
    if (!historiesContent.metadata) {
      errors.push('Histories content missing metadata');
    }
  }

  return { isValid: errors.length === 0, errors };
};

const SubSlideRenderer: React.FC<SubSlideRendererProps> = ({ slide, isIframeReady, isTargeted, className }) => {
  const { currentSubSlideIndex } = usePresentationStores();

  return (
    <SubSlideErrorBoundary
      slideId={slide?.id}
      subSlideId={slide?.subSlides?.[currentSubSlideIndex]?.id}
      onError={(error, errorInfo) => {
        // Log additional context for debugging
        console.error('SubSlideRenderer Error:', {
          slideId: slide?.id,
          subSlideIndex: currentSubSlideIndex,
          hasSubSlides: !!(slide?.subSlides && slide.subSlides.length > 0),
          error: error.message,
          componentStack: errorInfo.componentStack,
        });
      }}
    >
      <SubSlideRendererContent
        slide={slide}
        isIframeReady={isIframeReady}
        isTargeted={isTargeted}
        className={className}
      />
    </SubSlideErrorBoundary>
  );
};

const SubSlideRendererContent: React.FC<SubSlideRendererProps> = ({ slide, isIframeReady, isTargeted, className }) => {
  const { currentSubSlideIndex } = usePresentationStores();
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Validate slide data with comprehensive error handling
  const slideValidation = safeExecuteSync(
    () => {
      if (!slide) {
        throw createError.validation('Slide data is null or undefined');
      }
      if (!slide.id) {
        throw createError.validation('Slide missing required ID', { slide });
      }
      return true;
    },
    false,
    ErrorType.VALIDATION_ERROR,
    { slide },
  );

  if (!slideValidation) {
    return (
      <div className={cn('flex items-center justify-center w-full h-full bg-gray-900 text-white', className)}>
        <div className="text-center">
          <p className="text-lg">Invalid slide data</p>
          <p className="text-sm text-gray-400">Please check slide configuration</p>
        </div>
      </div>
    );
  }

  // Memoize visible sub-slides to prevent re-render loop
  const visibleSubSlides = useMemo(() => {
    return (
      slide.subSlides?.filter((subSlide) => {
        if (!subSlide || !subSlide.id || subSlide.show !== true) {
          return false;
        }

        // Validate subSlide content structure using helper function
        const validation = validateSubSlideContent(subSlide);
        if (!validation.isValid) {
          console.warn(`SubSlide ${subSlide.id} validation failed:`, validation.errors);
          return false;
        }

        return true;
      }) || []
    );
  }, [slide.subSlides]);

  const hasSubSlides = visibleSubSlides.length > 0;

  // Get current sub-slide content with validation - memoize to prevent re-render
  const currentSubSlide = useMemo(() => {
    if (!hasSubSlides || currentSubSlideIndex < 0 || currentSubSlideIndex >= visibleSubSlides.length) {
      return null;
    }
    return visibleSubSlides[currentSubSlideIndex];
  }, [hasSubSlides, currentSubSlideIndex, visibleSubSlides]);

  // Validate current sub-slide index - memoize to prevent re-calculation
  const validSubSlideIndex = useMemo(() => {
    return hasSubSlides && currentSubSlideIndex >= 0 && currentSubSlideIndex < visibleSubSlides.length;
  }, [hasSubSlides, currentSubSlideIndex, visibleSubSlides.length]);

  // Initialize nested Embla carousel for sub-slides
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    watchDrag: false,
    containScroll: false,
    skipSnaps: false,
  });

  // Sync embla carousel with presentation store sub-slide navigation
  useEffect(() => {
    if (emblaApi && hasSubSlides && validSubSlideIndex) {
      try {
        emblaApi.scrollTo(currentSubSlideIndex, false); // Don't animate to avoid conflicts
      } catch (error) {
        console.error('Failed to sync sub-slide carousel position:', error);
      }
    }
  }, [emblaApi, currentSubSlideIndex, hasSubSlides, validSubSlideIndex]);

  // Handle sub-slide transitions with animation and debugging
  // Use a ref to track the current subSlide to avoid unnecessary state updates
  const previousSubSlideIndexRef = useRef(currentSubSlideIndex);

  useEffect(() => {
    // Only trigger transition if the subSlide index actually changed
    if (hasSubSlides && validSubSlideIndex && previousSubSlideIndexRef.current !== currentSubSlideIndex) {
      previousSubSlideIndexRef.current = currentSubSlideIndex;

      const currentSubSlide = visibleSubSlides[currentSubSlideIndex];

      setIsTransitioning(true);
      const timer = setTimeout(() => {
        setIsTransitioning(false);
      }, 300); // 300ms transition duration

      return () => {
        clearTimeout(timer);
      };
    }
  }, [currentSubSlideIndex, hasSubSlides, validSubSlideIndex, slide.id, visibleSubSlides]);

  // Create slides with sub-slide content for each visible sub-slide
  const slidesWithSubSlideContent: SlideConfigDto[] = useMemo(() => {
    if (!hasSubSlides) {
      return [slide];
    }

    return visibleSubSlides.map((subSlide) => {
      try {
        // Handle different content types for subSlides
        const subSlideContent = subSlide.content;

        if (subSlideContent.type === 'realtime') {
          // Handle realtime subSlide content
          return {
            ...slide,
            content: {
              ...slide.content,
              type: 'realtime',
              metadata: {
                measure: subSlideContent.measure,
                station: subSlideContent.station,
                stationType: subSlideContent.stationType,
                cameras: subSlideContent.cameras,
                showOptions: subSlideContent.showOptions,
                layout: subSlideContent.layout,
              },
            },
          };
        } else if (subSlideContent.type === 'histories') {
          // Handle histories subSlide content with pageData
          const historiesMetadata = subSlideContent.metadata;

          return {
            ...slide,
            content: {
              ...slide.content,
              type: 'histories',
              metadata: {
                measure: historiesMetadata.measure,
                station: historiesMetadata.station,
                stationType: historiesMetadata.stationType,
                timeRange: historiesMetadata.timeRange,
                typeTimeRange: historiesMetadata.typeTimeRange,
                calc: 'average_day',
                layout: historiesMetadata.layout,
                config: historiesMetadata.config,
                fontSize: historiesMetadata.fontSize,
                // Add page-specific data for rendering
                pageData: subSlideContent.pageData,
                pageIndex: subSlideContent.pageIndex,
                totalPages: subSlideContent.totalPages,
                renderingContext: subSlideContent.renderingContext,
              },
            },
          };
        } else {
          // Fallback for other content types or legacy structure
          return {
            ...slide,
            content: {
              ...slide.content,
              metadata: subSlideContent,
            },
          };
        }
      } catch (error) {
        console.error('Error creating slide with sub-slide content:', error);
        // Return original slide as fallback
        return slide;
      }
    });
  }, [slide, hasSubSlides, visibleSubSlides]);

  const currentSlide = useMemo(() => {
    return slide;
  }, [slide]);

  // If no sub-slides, render the main slide normally
  if (!hasSubSlides) {
    return (
      <WrapperSessionPresent mode="preview" slide={currentSlide}>
        <Wrapper
          dataSlide={currentSlide}
          isIframeReady={isIframeReady}
          setEmblaApi={() => {}}
          isTargeted={isTargeted}
        />
      </WrapperSessionPresent>
    );
  }

  return (
    <div className={cn('relative w-full h-full', className)}>
      <WrapperSessionPresent mode="preview" slide={currentSlide}>
        <Wrapper
          dataSlide={currentSlide}
          isIframeReady={isIframeReady}
          setEmblaApi={() => {}}
          isTargeted={isTargeted}
        />
      </WrapperSessionPresent>
    </div>
  );
};

export default SubSlideRenderer;
