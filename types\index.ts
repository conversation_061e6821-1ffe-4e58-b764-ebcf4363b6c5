import { EFontSupport } from '@/constants';
import { SlideConfigDto } from './slide';

export interface ILanguageData {
  en: string;
  vi: string;
}

export interface IMeasuring {
  key: string;
  name: ILanguageData;
  unit: string;
  maxLimit: number | null;
  minLimit: number | null;
  maxTend: number | null;
  minTend: number | null;
  maxRange: number | null;
  minRange: number | null;
  numericalOrder: number | null;
  order: number | null;
}

export interface ICameraList {
  _id: string;
  cameraId: string;
  name: string;
  status: string;
  rtspUrl: string;
  rawRtsp: string;
  thumbnailUrl?: string;
}

export interface IDataStation {
  key: string;
  name: ILanguageData;
  stationTypeKey: string;
  measuringList: IMeasuring[];
  cameras?: ICameraList[];
  lastLog?: {
    receivedAt: string;
    measuringLogs: {
      [key: string]: {
        value: number;
        statusDevice: number;
      };
    };
  };
}

export interface ILedDetailData {
  key: string;
  name: ILanguageData;
  dataStations: IDataStation[];
  password: string;
  decimal: number;
}

export interface IInitData {
  title?: ILanguageData;
  sub_title?: ILanguageData;
  avatar?: string | File | null;
  colors: {
    color_disconnected: string;
    color_near_limit: string;
    color_over_limit: string;
    color_standar_qcvn: string;
    color_un_standar_qcvn: string;
    color_within_limit: string;
  };
}

export interface ISlideConfigData {
  id: string;
  font_family: EFontSupport;
  duration: number;
  color: string;
  background: string;
  borderColor: string;
}

export interface ILedConfigData {
  id?: string;
  led_key: string;
  name: ILanguageData;
  stations: IDataStation[];
  password: string;
  init: IInitData;
  slide_config_default: ISlideConfigData;
  presentations: SlideConfigDto[];
}

export interface IStationType {
  _id: string;
  key: string;
  name: ILanguageData;
  numericalOrder: number;
}

interface GradientObject {
  id?: number;
  type: string;
  colorStops: GradientColorStop[];
  global?: boolean;
}
interface GradientColorStop {
  offset: number;
  color: string;
}
interface LinearGradientObject extends GradientObject {
  type: 'linear';
  x: number;
  y: number;
  x2: number;
  y2: number;
}

interface RadialGradientObject extends GradientObject {
  type: 'radial';
  x: number;
  y: number;
  r: number;
}

export type Color = string | LinearGradientObject | RadialGradientObject;
